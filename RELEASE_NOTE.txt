Release Note:
- feat: 占星卜卦成功後直接跳轉至 AI 解讀頁面
- refactor: AI 解讀結果頁面流式響應顯示邏輯調整
- fix: 統一 AstReal 的 YouTube 頻道連結
- feat: 調整 UI 佈局並隱藏部分開發中功能
- feat: 新增原生啟動畫面
- fix: 更新 Android 啟動樣式並調整圖示資源
- feat: 配置 Android Release Build 的簽名
- fix: 調整事件標題生成與顯示邏輯
- refactor: 將 `aspect` 欄位更名為 `aspectType` 並調整事件偵測頁面
- fix: 更新檔案頁面匯入匯出按鈕文字
- feat: 優化關係分析頁面及 AI 費用計算
- fix: 修改安卓 APK 命名規則
- feat: 事件偵測服務與頁面優化
- feat: 增強 CSV 導入功能，支援更多欄位名並調整資料處理邏輯
- feat: 時間校正工具支持關係推運盤並優化星盤計算
- feat: 優化應用程式啟動流程及啟動畫面
- refactor: 更新 PWA manifest 背景色與主題色為白色
- feat: 新增問題回饋功能及日誌相關增強
- refactor: 移除意見回饋與預約相關頁面
- feat: 增強 AI 費用計算與模型感知能力
- feat: 新增 API 費用計算與顯示功能
- fix: 確保 AstroEvent 中圖標正確加載
- feat: 新增 GPT-5 模型支援並調整 OpenAI 請求體
- feat: 新增事件偵測設定服務及相關 UI
- feat: 新增分類事件時間軸功能
- feat: 細化事件類型判斷邏輯，整合行星所在宮位資訊
- refactor: 優化推運分析邏輯，直接使用星盤中的相位數據
- feat: 優化事件偵測服務及時間線數據處理
- feat: 新增事件偵測快取有效期設定功能
- feat: 新增事件偵測快取有效期設定功能
