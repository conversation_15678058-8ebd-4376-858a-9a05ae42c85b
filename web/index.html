<!DOCTYPE html><html><head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->
  <base href="$FLUTTER_BASE_HREF">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="提供初心者與占星師雙模式，整合命盤解析、推運分析與應用建議，讓占星更清晰、更有系統。">

  <!-- 防快取設定 -->
  <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
  <meta http-equiv="Pragma" content="no-cache">
  <meta http-equiv="Expires" content="0">
  <meta name="version" content="{{BUILD_VERSION}}">

  <!-- Android Chrome / Edge -->
  <meta name="theme-color" content="transparent">

  <!-- iOS meta tags & icons -->
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
  <meta name="apple-mobile-web-app-title" content="AstReal">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png">

  <title>AstReal</title>
  <link rel="manifest" href="manifest.json">


  <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport">


  <!-- Google Sign-In Configuration -->
  <meta name="google-signin-client_id" content="470077449550-4kcl1ng61mk48gtqns86f8irlqsiab48.apps.googleusercontent.com">

  <!-- Firebase SDK Configuration -->
  <script src="https://www.gstatic.com/firebasejs/10.7.0/firebase-app-compat.js"></script>
  <script src="https://www.gstatic.com/firebasejs/10.7.0/firebase-messaging-compat.js"></script>



  <style id="splash-screen-style">
    html {
      height: 100%
    }

    body {
      margin: 0;
      min-height: 100%;
      background-color: #FFFFFF;
          background-size: 100% 100%;
    }

    .center {
      margin: 0;
      position: absolute;
      top: 50%;
      left: 50%;
      -ms-transform: translate(-50%, -50%);
      transform: translate(-50%, -50%);
    }

    .contain {
      display:block;
      width:100%; height:100%;
      object-fit: contain;
    }

    .stretch {
      display:block;
      width:100%; height:100%;
    }

    .cover {
      display:block;
      width:100%; height:100%;
      object-fit: cover;
    }

    .bottom {
      position: absolute;
      bottom: 0;
      left: 50%;
      -ms-transform: translate(-50%, 0);
      transform: translate(-50%, 0);
    }

    .bottomLeft {
      position: absolute;
      bottom: 0;
      left: 0;
    }

    .bottomRight {
      position: absolute;
      bottom: 0;
      right: 0;
    }
  </style>
  <script id="splash-screen-script">
    function removeSplashFromWeb() {
      document.getElementById("splash")?.remove();
      document.getElementById("splash-branding")?.remove();
      document.body.style.background = "transparent";
    }
  </script>
</head>
<body>
  <picture id="splash">
      <source srcset="splash/img/light-1x.png 1x, splash/img/light-2x.png 2x, splash/img/light-3x.png 3x, splash/img/light-4x.png 4x" media="(prefers-color-scheme: light)">
      <source srcset="splash/img/dark-1x.png 1x, splash/img/dark-2x.png 2x, splash/img/dark-3x.png 3x, splash/img/dark-4x.png 4x" media="(prefers-color-scheme: dark)">
      <img class="center" aria-hidden="true" src="splash/img/light-1x.png" alt="">
  </picture>











  <script src="flutter_bootstrap.js" async=""></script>
  <script src="simple_version_check.js"></script>

  <!-- 開發環境下載入推播通知除錯工具 -->
  <script>
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
      const debugScript = document.createElement('script');
      debugScript.src = 'debug-push-notification.js';
      document.head.appendChild(debugScript);
    }
  </script>

  <!-- 註冊增強版 Service Worker -->
  <script>
    if ('serviceWorker' in navigator) {
      window.addEventListener('load', function() {
        navigator.serviceWorker.register('/sw_enhanced.js')
          .then(function(registration) {
            console.log('✅ Enhanced Service Worker registered:', registration);

            // 監聽 Service Worker 更新
            registration.addEventListener('updatefound', function() {
              const newWorker = registration.installing;
              console.log('🔄 New Service Worker installing...');

              newWorker.addEventListener('statechange', function() {
                if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                  console.log('🚀 New Service Worker installed, ready to activate');
                  // 可以在這裡觸發更新通知
                }
              });
            });
          })
          .catch(function(error) {
            console.log('❌ Enhanced Service Worker registration failed:', error);
          });
      });
    }
  </script>


</body></html>