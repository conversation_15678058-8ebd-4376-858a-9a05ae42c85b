# AstReal 多平台建置腳本說明

## 概述

本專案提供了完整的多平台建置腳本，支援 Web、Android、iOS 和 macOS 平台的自動化建置和部署。所有腳本都會自動設定正確的建置時間戳，確保 `getBuildInfo` 中的 `buildDate` 能正常顯示實際的打包時間。

## 腳本列表

### 主要腳本

- **`build_all.sh`** - 主要建置腳本，提供互動式選單
- **`web_build.sh`** - Web 平台建置腳本
- **`android_build.sh`** - Android 平台建置腳本
- **`ios_build.sh`** - iOS 平台建置腳本
- **`macos_build.sh`** - macOS 平台建置腳本

### 舊版腳本（參考用）

- **`apk_upload.sh`** - 原始 Android 上傳腳本

## 使用方法

### 1. 互動式建置（推薦）

```bash
./build_all.sh
```

執行後會顯示選單，讓您選擇：
- 要建置的平台（Web、Android、iOS、macOS 或全部）
- 建置類型（Release 或 Debug）

### 2. 單獨建置各平台

#### Web 平台
```bash
# Release 版本（強制更新）
./web_build.sh release --force-update

# Debug 版本
./web_build.sh debug
```

**功能：**
- 自動設定建置時間戳
- 建置 Flutter Web 應用
- 生成版本資訊文件
- 添加快取破壞參數
- 部署到 Firebase Hosting

#### Android 平台
```bash
# Release 版本
./android_build.sh release

# Debug 版本
./android_build.sh debug
```

**功能：**
- 自動設定建置時間戳
- 建置 APK 檔案
- 重新命名為有意義的檔案名稱
- 上傳到 Firebase App Distribution
- 自動發送通知給測試者

#### iOS 平台
```bash
# Release 版本
./ios_build.sh release

# Debug 版本
./ios_build.sh debug
```

**功能：**
- 自動設定建置時間戳
- 建置 iOS 應用
- 建立 IPA 檔案
- 上傳到 Firebase App Distribution
- 自動發送通知給測試者

**注意：** 僅能在 macOS 上執行

#### macOS 平台
```bash
# Release 版本
./macos_build.sh release

# Debug 版本
./macos_build.sh debug
```

**功能：**
- 自動設定建置時間戳
- 清除應用儲存資料（確保全新狀態）
- 建置 macOS 應用
- 打包成 DMG 檔案
- 建立 Applications 連結

**注意：** 僅能在 macOS 上執行

## 建置時間戳功能

所有腳本都會自動設定以下參數：

### 時間戳格式
- **TIMESTAMP**: `YYYYMMDD_HHMMSS` (例如: `20250808_143000`)
- **COMPILATION_TIMESTAMP**: `YYYYMMDDHHMMSS` (例如: `20250808143000`)

### 建置號碼格式
- **Release**: `20250808_143000`
- **Debug**: `20250808_143000_debug`
- **強制更新**: `20250808_143000_force_update`

### Flutter 建置參數
```bash
flutter build [platform] --[build_type] \
  --build-name=$VERSION \
  --build-number=$BUILD_NUMBER \
  --dart-define=COMPILATION_TIMESTAMP=$COMPILATION_TIMESTAMP
```

## 配置設定

### Firebase 設定

#### Android App Distribution
- **APP_ID**: `1:470077449550:android:4971c9e15686127296aa1f`
- **測試者**: `<EMAIL>`

#### iOS App Distribution
- **APP_ID**: `1:470077449550:ios:dcbc192966cde49096aa1f`
- **測試者**: `<EMAIL>`

#### Web Hosting
- **專案**: `astreal-d3f70`
- **網址**: `https://astreal.web.app`

### 版本號讀取

所有腳本都會自動從 `pubspec.yaml` 讀取版本號：
```yaml
version: 1.0.0+1
```

## 系統需求

### 通用需求
- Flutter SDK
- Git（用於取得提交記錄）

### Web 平台
- Firebase CLI

### Android 平台
- Android SDK
- Firebase CLI

### iOS 平台（僅 macOS）
- Xcode
- iOS SDK
- Firebase CLI

### macOS 平台（僅 macOS）
- Xcode
- macOS SDK
- hdiutil（系統內建）

## 輸出檔案

### Web 平台
- 部署到 Firebase Hosting
- 版本資訊：`build/web/version.json`

### Android 平台
- APK 檔案：`build/app/outputs/flutter-apk/Astreal_[type]_v[version]+[build].apk`
- 發布記錄：`RELEASE_NOTE.txt`

### iOS 平台
- IPA 檔案：`ios/build/Astreal_[type]_v[version]+[build].ipa`
- 發布記錄：`RELEASE_NOTE.txt`

### macOS 平台
- DMG 檔案：`build/macos/AstReal_[type]_v[version]+[build].dmg`
- 發布記錄：`RELEASE_NOTE.txt`

## 故障排除

### 常見問題

1. **權限錯誤**
   ```bash
   chmod +x *.sh
   ```

2. **Flutter 未找到**
   ```bash
   export PATH="/Users/<USER>/development/flutter/bin:$PATH"
   ```

3. **Firebase CLI 未安裝**
   ```bash
   npm install -g firebase-tools
   firebase login
   ```

4. **iOS/macOS 建置失敗**
   - 確保在 macOS 上執行
   - 檢查 Xcode 是否正確安裝
   - 確認 Apple Developer 帳號設定

### 檢查建置結果

#### Web 平台
```bash
# 檢查版本資訊
curl "https://astreal.web.app/version.json"
```

#### Android/iOS 平台
- 檢查 Firebase Console 中的 App Distribution
- 確認測試者收到通知郵件

#### macOS 平台
- 檢查 DMG 檔案是否正確建立
- 測試 DMG 安裝流程

## 最佳實踐

1. **建置前檢查**
   - 確認所有更改已提交到 Git
   - 執行本地測試
   - 檢查版本號是否正確

2. **Release 建置**
   - 使用 Release 模式進行正式發布
   - 確認所有平台建置成功
   - 測試各平台的安裝和執行

3. **版本管理**
   - 建置時間戳會自動生成
   - 確保 `pubspec.yaml` 中的版本號正確
   - 定期更新版本號

4. **測試流程**
   - 先使用 Debug 模式測試
   - 確認功能正常後再建置 Release 版本
   - 在不同設備上測試安裝包

## 支援

如有問題，請檢查：
1. 腳本執行權限
2. 系統需求是否滿足
3. Firebase 配置是否正確
4. 網路連線是否正常
