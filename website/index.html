<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AstReal - 專業占星應用程式</title>
    <meta name="description" content="AstReal 是一款專業的占星應用程式，提供完整的本命盤分析、深入剖析、多種星盤類型支持，適用於 iOS、Android、macOS 和 Web 平台。">
    <meta name="keywords" content="占星,星盤,本命盤,占星軟體,深入剖析,星座,行星,相位,宮位">
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://astreal.app/">
    <meta property="og:title" content="AstReal - 專業占星應用程式">
    <meta property="og:description" content="專業的占星應用程式，提供完整的本命盤分析、深入剖析，支持多平台使用。">
    <meta property="og:image" content="https://astreal.app/images/og-image.png">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://astreal.app/">
    <meta property="twitter:title" content="AstReal - 專業占星應用程式">
    <meta property="twitter:description" content="專業的占星應用程式，提供完整的本命盤分析、深入剖析，支持多平台使用。">
    <meta property="twitter:image" content="https://astreal.app/images/og-image.png">

    <!-- Favicon -->
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+TC:wght@300;400;500;700&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans TC', sans-serif;
            line-height: 1.6;
            color: #333;
            overflow-x: hidden;
        }

        /* Header */
        .header {
            background: linear-gradient(135deg, #3F51B5 0%, #303F9F 100%);
            color: white;
            padding: 80px 0;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1.5" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="90" cy="90" r="2.5" fill="rgba(255,255,255,0.1)"/></svg>');
            animation: float 20s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            position: relative;
            z-index: 1;
        }

        .header h1 {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header .subtitle {
            font-size: 1.3rem;
            margin-bottom: 40px;
            opacity: 0.9;
        }

        .cta-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 40px;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 10px;
            padding: 15px 30px;
            border: none;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 500;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .btn-primary {
            background: #F5A623;
            color: white;
            box-shadow: 0 4px 15px rgba(245, 166, 35, 0.4);
        }

        .btn-primary:hover {
            background: #E6941A;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(245, 166, 35, 0.6);
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        /* Features Section */
        .features {
            padding: 100px 0;
            background: #f8f9fa;
        }

        .section-title {
            text-align: center;
            margin-bottom: 60px;
        }

        .section-title h2 {
            font-size: 2.5rem;
            color: #2d3748;
            margin-bottom: 20px;
        }

        .section-title p {
            font-size: 1.2rem;
            color: #718096;
            max-width: 600px;
            margin: 0 auto;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 40px;
            margin-top: 60px;
        }

        .feature-card {
            background: white;
            padding: 40px 30px;
            border-radius: 20px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .feature-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #3F51B5 0%, #7986CB 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            color: white;
            font-size: 2rem;
        }

        .feature-card h3 {
            font-size: 1.5rem;
            color: #3F51B5;
            margin-bottom: 15px;
        }

        .feature-card p {
            color: #718096;
            line-height: 1.6;
        }

        /* Download Section */
        .download {
            padding: 100px 0;
            background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
            color: white;
        }

        .download-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin-top: 60px;
        }

        .download-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease;
        }

        .download-card:hover {
            transform: translateY(-5px);
        }

        .platform-icon {
            font-size: 3rem;
            margin-bottom: 20px;
            display: block;
        }

        .download-card h3 {
            font-size: 1.3rem;
            margin-bottom: 15px;
        }

        .download-card p {
            opacity: 0.8;
            margin-bottom: 20px;
            font-size: 0.9rem;
        }

        .download-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            background: #ff6b6b;
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .download-btn:hover {
            background: #ff5252;
            transform: scale(1.05);
        }

        .coming-soon {
            background: #718096;
            cursor: not-allowed;
        }

        .coming-soon:hover {
            background: #718096;
            transform: none;
        }

        /* Footer */
        .footer {
            background: #1a202c;
            color: white;
            padding: 60px 0 30px;
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 40px;
            margin-bottom: 40px;
        }

        .footer-section h3 {
            margin-bottom: 20px;
            color: #ff6b6b;
        }

        .footer-section p,
        .footer-section a {
            color: #a0aec0;
            text-decoration: none;
            line-height: 1.8;
            margin-bottom: 8px;
        }

        .footer-section a:hover {
            color: #ff6b6b;
        }

        .footer-section a i {
            margin-right: 8px;
            width: 16px;
            text-align: center;
            opacity: 0.8;
        }

        .footer-section a:hover i {
            opacity: 1;
        }

        .footer-bottom {
            border-top: 1px solid #2d3748;
            padding-top: 30px;
            text-align: center;
            color: #718096;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2.5rem;
            }

            .header .subtitle {
                font-size: 1.1rem;
            }

            .cta-buttons {
                flex-direction: column;
                align-items: center;
            }

            .features-grid {
                grid-template-columns: 1fr;
            }

            .download-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <h1>AstReal</h1>
            <p class="subtitle">專業的占星應用程式，探索星象的奧秘</p>
            <p>提供完整的本命盤分析、深入剖析、多種星盤類型支持</p>
            
            <!-- 主要下載按鈕 -->
            <div style="margin: 30px 0;">
                <a href="#download" class="btn btn-primary" style="font-size: 1.2rem; padding: 18px 40px; box-shadow: 0 6px 20px rgba(245, 166, 35, 0.5);">
                    <span class="material-icons" style="font-size: 1.4rem;">download</span>
                    立即下載
                </a>
            </div>

            <div class="cta-buttons">
                <a href="vision.html" class="btn btn-secondary">
                    <span class="material-icons">auto_awesome</span>
                    願景理念
                </a>
                <a href="#features" class="btn btn-secondary">
                    <span class="material-icons">explore</span>
                    了解更多
                </a>
                <a href="contact.html" class="btn btn-secondary">
                    <span class="material-icons">contact_mail</span>
                    聯絡我們
                </a>
            </div>
        </div>
    </header>

    <!-- Features Section -->
    <section id="features" class="features">
        <div class="container">
            <div class="section-title">
                <h2>強大功能</h2>
                <p>專業的占星工具，讓您深入探索星象的奧秘</p>
            </div>

            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <span class="material-icons">auto_awesome</span>
                    </div>
                    <h3>深入剖析</h3>
                    <p>運用先進的分析技術，為您提供個人化的星盤解讀和深度分析，讓占星知識更容易理解。</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <span class="material-icons">account_circle</span>
                    </div>
                    <h3>完整本命盤分析</h3>
                    <p>精確計算您的本命盤，包含行星位置、宮位分析、相位關係等完整資訊，專業級的占星計算引擎。</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <span class="material-icons">timeline</span>
                    </div>
                    <h3>多種星盤類型</h3>
                    <p>支援本命盤、合盤、推運盤、時刻盤等多種星盤類型，滿足不同的占星分析需求。</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <span class="material-icons">psychology</span>
                    </div>
                    <h3>占星卜卦</h3>
                    <p>提供專業的占星卜卦功能，透過時刻盤為您的問題提供指引和建議。</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <span class="material-icons">settings</span>
                    </div>
                    <h3>專業設定</h3>
                    <p>支援多種宮位系統、行星顯示選項、相位設定等專業功能，滿足不同占星師的需求。</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <span class="material-icons">devices</span>
                    </div>
                    <h3>跨平台支援</h3>
                    <p>支援 iOS、Android、macOS 和 Web 平台，讓您隨時隨地都能使用專業的占星工具。</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Download Section -->
    <section id="download" class="download">
        <div class="container">
            <div class="section-title">
                <h2>立即下載</h2>
                <p>選擇適合您的平台，開始您的占星之旅</p>
            </div>

            <div class="download-grid">
                <div class="download-card">
                    <span class="platform-icon">📱</span>
                    <h3>iOS</h3>
                    <p>適用於 iPhone 和 iPad<br>iOS 12.0 或更高版本</p>
                    <a href="https://apps.apple.com/app/astreal/id123456789" class="download-btn coming-soon">
                        <span class="material-icons">download</span>
                        即將推出
                    </a>
                </div>

                <div class="download-card">
                    <span class="platform-icon">🤖</span>
                    <h3>Android</h3>
                    <p>適用於 Android 設備<br>Android 6.0 或更高版本</p>
                    <a href="https://play.google.com/store/apps/details?id=com.one.astreal" class="download-btn coming-soon">
                        <span class="material-icons">download</span>
                        即將推出
                    </a>
                </div>

                <div class="download-card">
                    <span class="platform-icon">💻</span>
                    <h3>macOS</h3>
                    <p>適用於 Mac 電腦<br>macOS 10.15 或更高版本</p>
                    <a href="https://astreal.app/download/macos" class="download-btn coming-soon">
                        <span class="material-icons">download</span>
                        即將推出
                    </a>
                </div>

                <div class="download-card">
                    <span class="platform-icon">🌐</span>
                    <h3>Web 版本</h3>
                    <p>無需安裝，直接使用<br>支援所有現代瀏覽器</p>
                    <a href="https://astreal.web.app/" class="download-btn">
                        <span class="material-icons">launch</span>
                        立即使用
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>AstReal</h3>
                    <p>專業的占星應用程式，提供完整的本命盤分析、深入剖析，讓占星知識更容易理解和應用。</p>
                    <p>探索星象的奧秘，發現真實的自己。</p>
                </div>

                <div class="footer-section">
                    <h3>功能特色</h3>
                    <p><a href="#features">深入剖析</a></p>
                    <p><a href="#features">完整本命盤分析</a></p>
                    <p><a href="#features">多種星盤類型</a></p>
                    <p><a href="#features">占星卜卦</a></p>
                    <p><a href="#features">專業設定</a></p>
                </div>

                <div class="footer-section">
                    <h3>下載應用</h3>
                    <p><a href="https://apps.apple.com/app/astreal/id123456789">iOS App Store</a></p>
                    <p><a href="https://play.google.com/store/apps/details?id=com.one.astreal">Google Play</a></p>
                    <p><a href="https://astreal.app/download/macos">macOS 版本</a></p>
                    <p><a href="https://astreal-d3f70.web.app/">Web 版本</a></p>
                </div>

                <div class="footer-section">
                    <h3>聯絡我們</h3>
                    <p><a href="https://astreal-website.web.app" target="_blank">
                        <i class="fas fa-globe"></i> 官方網站
                    </a></p>
                    <p><a href="https://astreal.web.app" target="_blank">
                        <i class="fas fa-mobile-alt"></i> 占星應用程式
                    </a></p>
                    <p><a href="https://www.youtube.com/@astreal.astrology" target="_blank">
                        <i class="fab fa-youtube"></i> YouTube
                    </a></p>
                    <p><a href="https://instagram.com/astreal.astrology" target="_blank">
                        <i class="fab fa-instagram"></i> Instagram
                    </a></p>
                    <p><a href="https://facebook.com/astreal.astrology" target="_blank">
                        <i class="fab fa-facebook"></i> Facebook 粉絲專頁
                    </a></p>
                    <p><a href="mailto:<EMAIL>">
                        <i class="fas fa-envelope"></i> <EMAIL>
                    </a></p>
                </div>

                <div class="footer-section">
                    <h3>法律資訊</h3>
                    <p><a href="/privacy-policy.html">隱私權政策</a></p>
                    <p><a href="/terms-of-service.html">服務條款</a></p>
                </div>
            </div>

            <div class="footer-bottom">
                <p>&copy; 2025 AstReal. 保留所有權利。</p>
                <p>版本 1.0.0 | 專業占星應用程式</p>
            </div>
        </div>
    </footer>

    <!-- Smooth Scrolling Script -->
    <script>
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Add animation on scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe feature cards
        document.querySelectorAll('.feature-card, .download-card').forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';
            card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(card);
        });
    </script>
</body>
</html>
