#!/bin/bash
# AstReal 多平台建置腳本
# 使用方式: ./build_all.sh

set -e  # 遇到錯誤時停止執行

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
MAGENTA='\033[0;35m'
NC='\033[0m' # No Color

# 顯示標題
echo -e "${BLUE}╔══════════════════════════════════════════════════════════════╗${NC}"
echo -e "${BLUE}║                    ${CYAN}AstReal 多平台建置工具${BLUE}                    ║${NC}"
echo -e "${BLUE}╚══════════════════════════════════════════════════════════════╝${NC}"
echo ""

# 檢查腳本是否存在
check_script() {
    local script_name=$1
    if [ ! -f "$script_name" ]; then
        echo -e "${RED}❌ 找不到腳本: $script_name${NC}"
        return 1
    fi
    if [ ! -x "$script_name" ]; then
        echo -e "${YELLOW}⚠️  設定執行權限: $script_name${NC}"
        chmod +x "$script_name"
    fi
    return 0
}

# 顯示平台選單
show_platform_menu() {
    echo -e "${CYAN}請選擇要建置的平台：${NC}"
    echo ""
    echo -e "${GREEN}1)${NC} 🌐 Web 平台 (Firebase Hosting)"
    echo -e "${GREEN}2)${NC} 🤖 Android 平台 (Firebase App Distribution)"
    echo -e "${GREEN}3)${NC} 🍎 iOS 平台 (Firebase App Distribution)"
    echo -e "${GREEN}4)${NC} 💻 macOS 平台 (DMG 檔案)"
    echo -e "${GREEN}5)${NC} 🚀 全部平台 (依序建置)"
    echo -e "${GREEN}0)${NC} ❌ 退出"
    echo ""
}

# 顯示建置類型選單
show_build_type_menu() {
    echo -e "${CYAN}請選擇建置類型：${NC}"
    echo ""
    echo -e "${GREEN}1)${NC} 🏭 Release (正式版本)"
    echo -e "${GREEN}2)${NC} 🔧 Debug (除錯版本)"
    echo ""
}

# 取得建置類型
get_build_type() {
    while true; do
        show_build_type_menu
        read -p "請輸入選項 (1-2): " build_choice
        case $build_choice in
            1)
                echo "release"
                return
                ;;
            2)
                echo "debug"
                return
                ;;
            *)
                echo -e "${RED}❌ 無效選項，請重新選擇${NC}"
                echo ""
                ;;
        esac
    done
}

# 建置 Web 平台
build_web() {
    local build_type=$1
    echo -e "${BLUE}🌐 開始建置 Web 平台...${NC}"
    if check_script "web_build.sh"; then
        if [ "$build_type" = "release" ]; then
            ./web_build.sh release --force-update
        else
            ./web_build.sh debug
        fi
    else
        echo -e "${RED}❌ Web 建置腳本不存在${NC}"
        return 1
    fi
}

# 建置 Android 平台
build_android() {
    local build_type=$1
    echo -e "${BLUE}🤖 開始建置 Android 平台...${NC}"
    if check_script "android_build.sh"; then
        ./android_build.sh "$build_type"
    else
        echo -e "${RED}❌ Android 建置腳本不存在${NC}"
        return 1
    fi
}

# 建置 iOS 平台
build_ios() {
    local build_type=$1
    echo -e "${BLUE}🍎 開始建置 iOS 平台...${NC}"
    if [[ "$OSTYPE" != "darwin"* ]]; then
        echo -e "${RED}❌ iOS 建置只能在 macOS 上執行${NC}"
        return 1
    fi
    if check_script "ios_build.sh"; then
        ./ios_build.sh "$build_type"
    else
        echo -e "${RED}❌ iOS 建置腳本不存在${NC}"
        return 1
    fi
}

# 建置 macOS 平台
build_macos() {
    local build_type=$1
    echo -e "${BLUE}💻 開始建置 macOS 平台...${NC}"
    if [[ "$OSTYPE" != "darwin"* ]]; then
        echo -e "${RED}❌ macOS 建置只能在 macOS 上執行${NC}"
        return 1
    fi
    if check_script "macos_build.sh"; then
        ./macos_build.sh "$build_type"
    else
        echo -e "${RED}❌ macOS 建置腳本不存在${NC}"
        return 1
    fi
}

# 建置全部平台
build_all_platforms() {
    local build_type=$1
    echo -e "${MAGENTA}🚀 開始建置全部平台...${NC}"
    echo ""

    local success_count=0
    local total_count=0

    # Web 平台
    total_count=$((total_count + 1))
    if build_web "$build_type"; then
        success_count=$((success_count + 1))
        echo -e "${GREEN}✅ Web 平台建置成功${NC}"
    else
        echo -e "${RED}❌ Web 平台建置失敗${NC}"
    fi
    echo ""

    # Android 平台
    total_count=$((total_count + 1))
    if build_android "$build_type"; then
        success_count=$((success_count + 1))
        echo -e "${GREEN}✅ Android 平台建置成功${NC}"
    else
        echo -e "${RED}❌ Android 平台建置失敗${NC}"
    fi
    echo ""

    # iOS 和 macOS 平台（僅在 macOS 上）
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # iOS 平台
        total_count=$((total_count + 1))
        if build_ios "$build_type"; then
            success_count=$((success_count + 1))
            echo -e "${GREEN}✅ iOS 平台建置成功${NC}"
        else
            echo -e "${RED}❌ iOS 平台建置失敗${NC}"
        fi
        echo ""

        # macOS 平台
        total_count=$((total_count + 1))
        if build_macos "$build_type"; then
            success_count=$((success_count + 1))
            echo -e "${GREEN}✅ macOS 平台建置成功${NC}"
        else
            echo -e "${RED}❌ macOS 平台建置失敗${NC}"
        fi
        echo ""
    else
        echo -e "${YELLOW}⚠️  跳過 iOS 和 macOS 平台（需要 macOS 系統）${NC}"
        echo ""
    fi

    # 顯示總結
    echo -e "${MAGENTA}📊 建置總結：${NC}"
    echo -e "${GREEN}成功：$success_count/$total_count 個平台${NC}"

    if [ $success_count -eq $total_count ]; then
        echo -e "${GREEN}🎉 所有平台建置成功！${NC}"
    else
        echo -e "${YELLOW}⚠️  部分平台建置失敗，請檢查錯誤訊息${NC}"
    fi
}

# 主程式
main() {
    while true; do
        show_platform_menu
        read -p "請輸入選項 (0-5): " choice

        case $choice in
            1)
                build_type=$(get_build_type)
                echo ""
                build_web "$build_type"
                ;;
            2)
                build_type=$(get_build_type)
                echo ""
                build_android "$build_type"
                ;;
            3)
                build_type=$(get_build_type)
                echo ""
                build_ios "$build_type"
                ;;
            4)
                build_type=$(get_build_type)
                echo ""
                build_macos "$build_type"
                ;;
            5)
                build_type=$(get_build_type)
                echo ""
                build_all_platforms "$build_type"
                ;;
            0)
                echo -e "${GREEN}👋 再見！${NC}"
                exit 0
                ;;
            *)
                echo -e "${RED}❌ 無效選項，請重新選擇${NC}"
                echo ""
                continue
                ;;
        esac

        echo ""
        echo -e "${CYAN}按 Enter 鍵繼續...${NC}"
        read
        echo ""
    done
}

# 執行主程式
main
