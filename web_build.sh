#!/bin/bash
# Web 平台打包腳本
# 使用方式: ./web_build.sh [release|debug] [--force-update]
# ./web_build.sh release --force-update
# ./web_build.sh debug

set -e  # 遇到錯誤時停止執行

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 設定 Flutter PATH
export PATH="/Users/<USER>/development/flutter/bin:$PATH"

# 參數解析
BUILD_TYPE="release"
FORCE_UPDATE=false

while [[ $# -gt 0 ]]; do
  case $1 in
    release|debug)
      BUILD_TYPE="$1"
      shift
      ;;
    --force-update)
      FORCE_UPDATE=true
      shift
      ;;
    *)
      echo -e "${RED}未知參數: $1${NC}"
      echo "使用方式: $0 [release|debug] [--force-update]"
      exit 1
      ;;
  esac
done

echo -e "${BLUE}=== AstReal Web 打包腳本 ===${NC}"
echo -e "${YELLOW}建置類型：${NC}$BUILD_TYPE"
echo -e "${YELLOW}強制更新：${NC}$FORCE_UPDATE"
echo ""

# 防呆檢查
command -v flutter >/dev/null 2>&1 || { echo -e "${RED}❌ Flutter 未安裝${NC}"; exit 1; }
command -v firebase >/dev/null 2>&1 || { echo -e "${RED}❌ Firebase CLI 未安裝${NC}"; exit 1; }

# 生成時間戳
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
COMPILATION_TIMESTAMP=$(date +"%Y%m%d%H%M%S")
BUILD_DATE=$(date +"%Y-%m-%d %H:%M:%S")

echo -e "${YELLOW}時間戳：${NC}$TIMESTAMP"
echo -e "${YELLOW}建置時間：${NC}$BUILD_DATE"
echo ""

# 從 pubspec.yaml 讀取版本號
VERSION=$(grep '^version:' pubspec.yaml | sed 's/version: //' | sed 's/+.*//')
if [ -z "$VERSION" ]; then
    echo -e "${RED}錯誤：無法從 pubspec.yaml 讀取版本號${NC}"
    exit 1
fi

# 設定建置號碼
if [ "$FORCE_UPDATE" = true ]; then
    BUILD_NUMBER="${TIMESTAMP}_force_update"
    echo -e "${YELLOW}強制更新版本：${NC}是"
else
    BUILD_NUMBER="$TIMESTAMP"
fi

echo -e "${YELLOW}應用版本：${NC}$VERSION"
echo -e "${YELLOW}建置號碼：${NC}$BUILD_NUMBER"
echo ""

# 取得最新 Git commit messages
echo -e "${BLUE}📝 取得 Git 提交記錄...${NC}"
GIT_MESSAGES=$(git log -10 --pretty=format:"- %s")

# 寫入到 RELEASE_NOTE 檔案
echo "Release Note:" > RELEASE_NOTE.txt
echo "$GIT_MESSAGES" >> RELEASE_NOTE.txt

# 清理舊的建置檔案
echo -e "${BLUE}🧹 清理舊的建置檔案...${NC}"
rm -rf build/web

# 建置 Flutter Web
echo -e "${BLUE}🔨 建置 Flutter Web...${NC}"
flutter build web --$BUILD_TYPE \
  --build-name=$VERSION \
  --build-number=$BUILD_NUMBER \
  --dart-define=COMPILATION_TIMESTAMP=$COMPILATION_TIMESTAMP \
  --pwa-strategy=none

# 生成唯一的資源版本號（用於避免快取）
ASSET_VERSION=$(date +"%Y%m%d%H%M%S")
echo -e "${YELLOW}資源版本號：${NC}$ASSET_VERSION"

# 進入建置目錄處理快取問題
cd build/web

# 生成版本資訊文件
echo -e "${BLUE}📝 生成版本資訊文件...${NC}"
BUILD_TIMESTAMP=$(date -u +"%Y-%m-%dT%H:%M:%S.%3NZ")
BUILD_NUMBER_NUMERIC=$(date +%s)

cat > version.json << EOF
{
  "version": "$BUILD_NUMBER",
  "buildNumber": $BUILD_NUMBER_NUMERIC,
  "buildTime": "$BUILD_TIMESTAMP",
  "assetVersion": "$ASSET_VERSION",
  "description": "AstReal 占星應用",
  "forceCacheClear": $( [ "$FORCE_UPDATE" = true ] && echo "true" || echo "false" ),
  "updateType": "$( [ "$FORCE_UPDATE" = true ] && echo "important" || echo "normal" )",
  "updateMessage": "$( [ "$FORCE_UPDATE" = true ] && echo "重要更新可用，建議立即更新以獲得最佳體驗" || echo "新版本已發布，請重新載入以獲得最新功能" )",
  "features": [
    "效能優化與穩定性改善",
    "使用者介面優化",
    "錯誤修復與功能增強"
  ],
  "isActive": true
}
EOF

# 為所有 JS 和 CSS 檔案添加版本參數避免快取
echo -e "${BLUE}🔧 添加版本查詢參數...${NC}"
find . -name "*.html" -type f -exec sed -i.bak "s/\\.js\"/\\.js?v=$ASSET_VERSION\"/g" {} \;
find . -name "*.html" -type f -exec sed -i.bak "s/\\.css\"/\\.css?v=$ASSET_VERSION\"/g" {} \;
find . -name "*.html" -type f -exec sed -i.bak "s/\\.dart\\.js\"/\\.dart\\.js?v=$ASSET_VERSION\"/g" {} \;

# 特別處理 Service Worker
if [ -f "flutter_service_worker.js" ]; then
    echo "// Version: $BUILD_NUMBER" | cat - flutter_service_worker.js > temp && mv temp flutter_service_worker.js
fi

# 清理備份檔案
find . -name "*.bak" -delete

cd ../..

# 部署到 Firebase Hosting
echo -e "${BLUE}🚀 部署到 Firebase Hosting...${NC}"
firebase deploy --only hosting

echo ""
echo -e "${GREEN}✅ Web 版本建置並部署完成！${NC}"
echo -e "${YELLOW}版本：${NC}$BUILD_NUMBER"
echo -e "${YELLOW}建置時間：${NC}$BUILD_DATE"
echo -e "${YELLOW}網址：${NC}https://astreal.web.app"
echo ""
echo -e "${BLUE}💡 提示：${NC}"
echo "   - 如果看不到更新，請按 Ctrl+Shift+R (或 Cmd+Shift+R) 強制重新載入"
echo "   - 或使用無痕模式開啟網站"
echo "   - 版本檢查器會在 1 分鐘後開始檢查更新"

# 可選：自動開啟網站
read -p "是否要開啟網站檢查部署結果？(y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    open "https://astreal.web.app"
fi
