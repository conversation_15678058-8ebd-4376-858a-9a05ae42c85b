#!/bin/bash
# iOS 平台打包腳本
# 使用方式: ./ios_build.sh [release|debug]

set -e  # 遇到錯誤時停止執行

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 設定 Flutter PATH
export PATH="/Users/<USER>/development/flutter/bin:$PATH"

# 參數解析
BUILD_TYPE="release"

if [ $# -gt 0 ]; then
    case $1 in
        release|debug)
            BUILD_TYPE="$1"
            ;;
        *)
            echo -e "${RED}未知參數: $1${NC}"
            echo "使用方式: $0 [release|debug]"
            exit 1
            ;;
    esac
fi

echo -e "${BLUE}=== AstReal iOS 打包腳本 ===${NC}"
echo -e "${YELLOW}建置類型：${NC}$BUILD_TYPE"
echo ""

# 防呆檢查
command -v flutter >/dev/null 2>&1 || { echo -e "${RED}❌ Flutter 未安裝${NC}"; exit 1; }
command -v firebase >/dev/null 2>&1 || { echo -e "${RED}❌ Firebase CLI 未安裝${NC}"; exit 1; }
command -v xcodebuild >/dev/null 2>&1 || { echo -e "${RED}❌ Xcode 未安裝或未設定命令列工具${NC}"; exit 1; }

# 檢查是否在 macOS 上執行
if [[ "$OSTYPE" != "darwin"* ]]; then
    echo -e "${RED}❌ iOS 建置只能在 macOS 上執行${NC}"
    exit 1
fi

# Firebase App Distribution 設定
APP_ID="1:470077449550:ios:dcbc192966cde49096aa1f"
TESTERS="<EMAIL>"

# 生成時間戳
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
COMPILATION_TIMESTAMP=$(date +"%Y%m%d%H%M%S")
BUILD_DATE=$(date +"%Y-%m-%d %H:%M:%S")

echo -e "${YELLOW}時間戳：${NC}$TIMESTAMP"
echo -e "${YELLOW}建置時間：${NC}$BUILD_DATE"
echo ""

# 從 pubspec.yaml 讀取版本號
VERSION=$(grep '^version:' pubspec.yaml | sed 's/version: //' | sed 's/+.*//')
if [ -z "$VERSION" ]; then
    echo -e "${RED}錯誤：無法從 pubspec.yaml 讀取版本號${NC}"
    exit 1
fi

# 設定建置號碼
BUILD_NUMBER="$TIMESTAMP"

echo -e "${YELLOW}應用版本：${NC}$VERSION"
echo -e "${YELLOW}建置號碼：${NC}$BUILD_NUMBER"
echo ""

# 取得最新 Git commit messages
echo -e "${BLUE}📝 取得 Git 提交記錄...${NC}"
GIT_MESSAGES=$(git log -30 --pretty=format:"- %s")

# 寫入到 RELEASE_NOTE 檔案
echo "Release Note:" > RELEASE_NOTE.txt
echo "$GIT_MESSAGES" >> RELEASE_NOTE.txt

# 清理舊的建置檔案
echo -e "${BLUE}🧹 清理舊的建置檔案...${NC}"
flutter clean
flutter pub get

# 建置 iOS
echo -e "${BLUE}🔨 建置 iOS...${NC}"
flutter build ios --$BUILD_TYPE \
  --build-name=$VERSION \
  --build-number=$BUILD_NUMBER \
  --dart-define=COMPILATION_TIMESTAMP=$COMPILATION_TIMESTAMP \
  --no-codesign

# 檢查建置是否成功
if [ ! -d "build/ios/iphoneos/Runner.app" ]; then
    echo -e "${RED}❌ iOS 建置失敗，找不到 Runner.app${NC}"
    exit 1
fi

# 建立 IPA 檔案（使用 xcodebuild）
echo -e "${BLUE}📦 建立 IPA 檔案...${NC}"
cd ios

# 檢查 exportOptions.plist 是否存在
if [ ! -f "exportOptions.plist" ]; then
    echo -e "${YELLOW}⚠️  exportOptions.plist 不存在，建立預設檔案...${NC}"
    cat > exportOptions.plist << EOF
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>method</key>
    <string>development</string>
    <key>uploadBitcode</key>
    <false/>
    <key>uploadSymbols</key>
    <true/>
    <key>compileBitcode</key>
    <false/>
    <key>signingStyle</key>
    <string>automatic</string>
</dict>
</plist>
EOF
fi

# 使用 xcodebuild 建立 archive
echo -e "${YELLOW}建立 Archive...${NC}"
xcodebuild -workspace Runner.xcworkspace \
  -scheme Runner \
  -configuration $( [ "$BUILD_TYPE" = "release" ] && echo "Release" || echo "Debug" ) \
  -destination generic/platform=iOS \
  -archivePath build/Runner.xcarchive \
  archive

# 檢查 archive 是否成功
if [ ! -d "build/Runner.xcarchive" ]; then
    echo -e "${RED}❌ Archive 建立失敗${NC}"
    cd ..
    exit 1
fi

# 建立 IPA
echo -e "${YELLOW}匯出 IPA...${NC}"
xcodebuild -exportArchive \
  -archivePath build/Runner.xcarchive \
  -exportPath build \
  -exportOptionsPlist exportOptions.plist

cd ..

# 檢查 IPA 是否建立成功
IPA_PATH="ios/build/Runner.ipa"
if [ ! -f "$IPA_PATH" ]; then
    echo -e "${RED}❌ 找不到 IPA 檔案，建置可能失敗${NC}"
    exit 1
fi

# 重新命名 IPA
IPA_NAME="Astreal_${BUILD_TYPE}_v${VERSION}+${BUILD_NUMBER}.ipa"
NEW_IPA_PATH="ios/build/${IPA_NAME}"
mv "$IPA_PATH" "$NEW_IPA_PATH"

echo -e "${YELLOW}IPA 檔案：${NC}$IPA_NAME"

# 上傳到 Firebase App Distribution
echo -e "${BLUE}🚀 上傳到 Firebase App Distribution...${NC}"
firebase appdistribution:distribute "$NEW_IPA_PATH" \
  --app "$APP_ID" \
  --release-notes "$GIT_MESSAGES" \
  --testers "$TESTERS"

echo ""
echo -e "${GREEN}✅ iOS IPA 建置並上傳完成！${NC}"
echo -e "${YELLOW}檔案名稱：${NC}$IPA_NAME"
echo -e "${YELLOW}建置時間：${NC}$BUILD_DATE"
echo -e "${YELLOW}版本：${NC}$VERSION ($BUILD_NUMBER)"
echo ""
echo -e "${BLUE}💡 提示：${NC}"
echo "   - 測試者將收到 Firebase App Distribution 的通知郵件"
echo "   - 可以在 Firebase Console 中查看分發狀態"
echo "   - IPA 檔案已保存在: $NEW_IPA_PATH"
echo "   - 確保已在 Apple Developer 中設定適當的 Provisioning Profile"
