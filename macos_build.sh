#!/bin/bash
# macOS 平台打包腳本
# 使用方式: ./macos_build.sh [release|debug]
# ./macos_build.sh release

set -e  # 遇到錯誤時停止執行

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 設定 Flutter PATH
export PATH="/Users/<USER>/development/flutter/bin:$PATH"

# 參數解析
BUILD_TYPE="release"

if [ $# -gt 0 ]; then
    case $1 in
        release|debug)
            BUILD_TYPE="$1"
            ;;
        *)
            echo -e "${RED}未知參數: $1${NC}"
            echo "使用方式: $0 [release|debug]"
            exit 1
            ;;
    esac
fi

echo -e "${BLUE}=== AstReal macOS 打包腳本 ===${NC}"
echo -e "${YELLOW}建置類型：${NC}$BUILD_TYPE"
echo ""

# 防呆檢查
command -v flutter >/dev/null 2>&1 || { echo -e "${RED}❌ Flutter 未安裝${NC}"; exit 1; }
command -v hdiutil >/dev/null 2>&1 || { echo -e "${RED}❌ hdiutil 未找到（macOS 工具）${NC}"; exit 1; }

# 檢查是否在 macOS 上執行
if [[ "$OSTYPE" != "darwin"* ]]; then
    echo -e "${RED}❌ macOS 建置只能在 macOS 上執行${NC}"
    exit 1
fi

# 生成時間戳
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
COMPILATION_TIMESTAMP=$(date +"%Y%m%d%H%M%S")
BUILD_DATE=$(date +"%Y-%m-%d %H:%M:%S")

echo -e "${YELLOW}時間戳：${NC}$TIMESTAMP"
echo -e "${YELLOW}建置時間：${NC}$BUILD_DATE"
echo ""

# 從 pubspec.yaml 讀取版本號
VERSION=$(grep '^version:' pubspec.yaml | sed 's/version: //' | sed 's/+.*//')
if [ -z "$VERSION" ]; then
    echo -e "${RED}錯誤：無法從 pubspec.yaml 讀取版本號${NC}"
    exit 1
fi

# 設定建置號碼
BUILD_NUMBER="$TIMESTAMP"

echo -e "${YELLOW}應用版本：${NC}$VERSION"
echo -e "${YELLOW}建置號碼：${NC}$BUILD_NUMBER"
echo ""

# 取得最新 Git commit messages
echo -e "${BLUE}📝 取得 Git 提交記錄...${NC}"
GIT_MESSAGES=$(git log -10 --pretty=format:"- %s")

# 寫入到 RELEASE_NOTE 檔案
echo "Release Note:" > RELEASE_NOTE.txt
echo "$GIT_MESSAGES" >> RELEASE_NOTE.txt

# 清理舊的建置檔案和應用資料
echo -e "${BLUE}🧹 清理舊的建置檔案和應用資料...${NC}"
flutter clean
flutter pub get

# 清除 macOS 應用的儲存資料（確保全新狀態）
echo -e "${BLUE}🗑️  清除應用儲存資料...${NC}"
APP_SUPPORT_DIR="$HOME/Library/Application Support/com.one.astreal"
PREFERENCES_DIR="$HOME/Library/Preferences"
CACHES_DIR="$HOME/Library/Caches/com.one.astreal"

if [ -d "$APP_SUPPORT_DIR" ]; then
    echo -e "${YELLOW}清除 Application Support 資料...${NC}"
    rm -rf "$APP_SUPPORT_DIR"
fi

if [ -f "$PREFERENCES_DIR/com.one.astreal.plist" ]; then
    echo -e "${YELLOW}清除 Preferences 資料...${NC}"
    rm -f "$PREFERENCES_DIR/com.one.astreal.plist"
fi

if [ -d "$CACHES_DIR" ]; then
    echo -e "${YELLOW}清除 Caches 資料...${NC}"
    rm -rf "$CACHES_DIR"
fi

# 建置 macOS
echo -e "${BLUE}🔨 建置 macOS 應用...${NC}"
flutter build macos --$BUILD_TYPE \
  --build-name=$VERSION \
  --build-number=$BUILD_NUMBER \
  --dart-define=COMPILATION_TIMESTAMP=$COMPILATION_TIMESTAMP

# 檢查建置是否成功
APP_PATH="build/macos/Build/Products/$( [ "$BUILD_TYPE" = "release" ] && echo "Release" || echo "Debug" )/astreal.app"
if [ ! -d "$APP_PATH" ]; then
    echo -e "${RED}❌ macOS 建置失敗，找不到 astreal.app${NC}"
    exit 1
fi

# 建立 DMG 檔案
echo -e "${BLUE}📦 建立 DMG 檔案...${NC}"

DMG_NAME="AstReal_${BUILD_TYPE}_v${VERSION}_${BUILD_NUMBER}.dmg"
DMG_PATH="build/macos/${DMG_NAME}"
TEMP_DMG_PATH="build/macos/temp_${DMG_NAME}"

# 建立暫時的 DMG 目錄
TEMP_DIR="build/macos/dmg_temp"
rm -rf "$TEMP_DIR"
mkdir -p "$TEMP_DIR"

# 複製應用到暫時目錄
cp -R "$APP_PATH" "$TEMP_DIR/"

# 建立 Applications 連結
ln -s /Applications "$TEMP_DIR/Applications"

# 建立 DMG
echo -e "${YELLOW}建立 DMG 檔案...${NC}"
hdiutil create -volname "AstReal v$VERSION" \
  -srcfolder "$TEMP_DIR" \
  -ov -format UDZO \
  "$TEMP_DMG_PATH"

# 最佳化 DMG
echo -e "${YELLOW}最佳化 DMG 檔案...${NC}"
hdiutil convert "$TEMP_DMG_PATH" -format UDZO -o "$DMG_PATH"

# 清理暫時檔案
rm -rf "$TEMP_DIR"
rm -f "$TEMP_DMG_PATH"

# 檢查 DMG 是否建立成功
if [ ! -f "$DMG_PATH" ]; then
    echo -e "${RED}❌ DMG 檔案建立失敗${NC}"
    exit 1
fi

# 取得檔案大小
DMG_SIZE=$(du -h "$DMG_PATH" | cut -f1)

echo ""
echo -e "${GREEN}✅ macOS DMG 建置完成！${NC}"
echo -e "${YELLOW}檔案名稱：${NC}$DMG_NAME"
echo -e "${YELLOW}檔案大小：${NC}$DMG_SIZE"
echo -e "${YELLOW}建置時間：${NC}$BUILD_DATE"
echo -e "${YELLOW}版本：${NC}$VERSION ($BUILD_NUMBER)"
echo -e "${YELLOW}檔案位置：${NC}$DMG_PATH"
echo ""
echo -e "${BLUE}💡 提示：${NC}"
echo "   - DMG 檔案已建立，可以分發給用戶"
echo "   - 應用資料已完全清除，首次啟動將是全新狀態"
echo "   - 用戶可以將應用拖拽到 Applications 資料夾安裝"
echo "   - 如需程式碼簽名，請使用 Xcode 或 codesign 工具"

# 可選：自動開啟 Finder 顯示 DMG 檔案
read -p "是否要在 Finder 中顯示 DMG 檔案？(y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    open -R "$DMG_PATH"
fi
