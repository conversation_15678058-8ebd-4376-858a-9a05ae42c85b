/// Firebase Firestore Collection 常數定義
/// 
/// 統一管理所有 Firestore collection 名稱，確保命名一致性
/// 並提供完整的文檔說明
class FirebaseCollections {
  // ==================== 用戶相關集合 ====================
  
  /// 用戶檔案集合
  /// 存儲用戶基本資訊、登入記錄、解讀次數等
  /// 文檔 ID: userId
  static const String userProfiles = 'user_profiles';
  
  /// 用戶偏好設定集合
  /// 存儲用戶的應用設定、主題、語言等偏好
  /// 文檔 ID: userId
  static const String userPreferences = 'user_preferences';
  
  /// 用戶訂閱狀態集合
  /// 存儲用戶的訂閱狀態、到期時間等
  /// 文檔 ID: userId
  static const String userSubscriptions = 'user_subscriptions';
  
  // ==================== 支付相關集合 ====================
  
  /// 支付記錄集合
  /// 存儲所有支付交易記錄
  /// 文檔 ID: 自動生成
  static const String payments = 'payments';
  
  /// 用戶支付記錄集合
  /// 存儲特定用戶的支付記錄（子集合）
  /// 路徑: user_payments/{userId}/payments/{paymentId}
  static const String userPayments = 'user_payments';
  
  /// 訂閱狀態集合（舊版本，可能與 userSubscriptions 重複）
  /// 文檔 ID: userId
  static const String subscriptions = 'subscriptions';
  
  // ==================== AI 使用量相關集合 ====================
  
  /// AI 使用量記錄集合
  /// 存儲每日每個 AI Provider 的使用量統計
  /// 文檔 ID: {YYYY-MM-DD}_{provider}
  static const String aiUsage = 'ai_usage';
  
  // ==================== 應用版本相關集合 ====================
  
  /// 應用版本控制集合
  /// 存儲各平台的版本資訊、更新訊息等
  /// 文檔 ID: platform (android, ios, macos, windows, web)
  static const String appVersions = 'app_versions';
  
  // ==================== 可能存在的其他集合 ====================
  
  /// 出生資料集合（如果使用雲端存儲）
  /// 文檔 ID: 自動生成或 userId 相關
  static const String birthData = 'birth_data';
  
  /// 使用日誌集合
  /// 存儲應用使用日誌
  static const String usageLogs = 'usage_logs';
  
  /// 用戶分析集合
  /// 存儲用戶行為分析資料
  static const String userAnalytics = 'user_analytics';
  
  /// 用戶回饋集合
  /// 存儲用戶回饋和建議
  static const String userFeedback = 'user_feedback';
  
  /// 用戶通知集合
  /// 存儲用戶通知記錄
  static const String userNotifications = 'user_notifications';
  
  /// 占卜記錄集合
  /// 存儲占卜相關記錄
  static const String divinationRecords = 'divination_records';

  // ==================== 工具方法 ====================
  
  /// 獲取所有活躍使用的集合名稱
  static List<String> get activeCollections => [
    userProfiles,
    userPreferences,
    userSubscriptions,
    payments,
    userPayments,
    aiUsage,
    appVersions,
    userFeedback,
  ];
  
  /// 獲取所有用戶相關的主要集合名稱
  static List<String> get userMainCollections => [
    userProfiles,
    userPreferences,
    userSubscriptions,
  ];
  
  /// 獲取所有可能包含用戶資料的集合名稱（用於帳戶刪除）
  static List<String> get allUserDataCollections => [
    userProfiles,
    userPreferences,
    userSubscriptions,
    subscriptions,
    birthData,
    usageLogs,
    userAnalytics,
    userFeedback,
    userNotifications,
    divinationRecords,
  ];
}
