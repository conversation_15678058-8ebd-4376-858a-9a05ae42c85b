import 'dart:convert';

import 'package:flutter/services.dart' show rootBundle;
import 'package:flutter/services.dart';
import 'package:turf/turf.dart';
class TimezoneService {

  /// 讀取並解析時區 GeoJSON
  /// # 下載最新 2025b 版本（完整版約 200MB）
  // wget https://github.com/evansiroky/timezone-boundary-builder/releases/download/2025b/timezones.geojson.zip
  //
  // # 解壓並替換
  // unzip timezones.geojson.zip
  // mv combined.json assets/combined.json
  Future<List<Map<String, dynamic>>> loadTimeZoneGeoJSON() async {
    String geojsonData = await rootBundle.loadString('assets/json/combined.json');
    Map<String, dynamic> geojson = jsonDecode(geojsonData);
    return List<Map<String, dynamic>>.from(geojson['features']);
  }

  /// 透過 LatLng 查詢對應的時區
  Future<String?> getTimeZoneFromLatLng(double lat, double lng) async {
    List<Map<String, dynamic>> timeZoneFeatures = await loadTimeZoneGeoJSON();

    // ✅ 修正：使用 `Position` 而非 `Point`
    Position searchPosition = Position(lng, lat);

    for (var feature in timeZoneFeatures) {
      var geometry = feature['geometry'];
      var properties = feature['properties'];

      if (geometry['type'] == 'Polygon') {
        // ✅ 修正：解析 Polygon 座標
        List<List<Position>> polygonCoordinates = (geometry['coordinates'] as List)
            .map<List<Position>>((ring) => (ring as List)
            .map<Position>((coordinate) => Position(coordinate[0], coordinate[1]))
            .toList())
            .toList();

        Polygon polygon = Polygon(coordinates: polygonCoordinates);

        // ✅ 使用 `Position` 作為參數
        if (booleanPointInPolygon(searchPosition, polygon)) {
          return properties['tzid'];
        }
      } else if (geometry['type'] == 'MultiPolygon') {
        // ✅ 修正 `MultiPolygon` 格式
        List<List<List<Position>>> multiPolygonCoordinates = (geometry['coordinates'] as List)
            .map<List<List<Position>>>((polygon) => (polygon as List)
            .map<List<Position>>((ring) => (ring as List)
            .map<Position>((coordinate) => Position(coordinate[0], coordinate[1]))
            .toList())
            .toList())
            .toList();

        MultiPolygon multiPolygon = MultiPolygon(coordinates: multiPolygonCoordinates);

        // ✅ 使用 `Position` 作為參數
        if (booleanPointInPolygon(searchPosition, multiPolygon)) {
          return properties['tzid'];
        }
      }
    }

    return null; // 找不到對應時區
  }

}
