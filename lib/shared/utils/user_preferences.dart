import 'package:shared_preferences/shared_preferences.dart';

import '../../core/utils/logger_utils.dart';

/// 用戶偏好設定管理工具類
/// 統一管理應用中的用戶偏好設定
class UserPreferences {
  // SharedPreferences 鍵值常數
  static const String _userListSortOptionKey = 'user_list_sort_option';
  static const String _userListSortDirectionKey = 'user_list_sort_direction';
  
  // 其他可能的偏好設定鍵值
  static const String _themeKey = 'app_theme';
  static const String _languageKey = 'app_language';
  static const String _userModeKey = 'user_mode';
  static const String _streamingModeKey = 'use_streaming_mode';
  static const String _isFirstTimeUserKey = 'is_first_time_user';

  // 每日星相推播設定
  static const String _dailyAstrologyNotificationKey = 'daily_astrology_notification_enabled';
  static const String _dailyAstrologyTimeKey = 'daily_astrology_notification_time';
  static const String _dailyAstrologyPersonalizedKey = 'daily_astrology_personalized_enabled';

  // 事件偵測設定
  static const String _eventMinimumScoreKey = 'event_minimum_score';
  static const String _eventEnabledTypesKey = 'event_enabled_types';
  static const String _eventCacheExpiryDaysKey = 'event_cache_expiry_days';
  
  /// 獲取 SharedPreferences 實例
  static Future<SharedPreferences> _getPrefs() async {
    return await SharedPreferences.getInstance();
  }

  // ==================== 用戶列表排序偏好 ====================
  
  /// 保存用戶列表排序選項
  static Future<bool> saveUserListSortOption(int sortOptionIndex) async {
    try {
      final prefs = await _getPrefs();
      final result = await prefs.setInt(_userListSortOptionKey, sortOptionIndex);
      logger.d('保存用戶列表排序選項: $sortOptionIndex');
      return result;
    } catch (e) {
      logger.e('保存用戶列表排序選項失敗: $e');
      return false;
    }
  }
  
  /// 獲取用戶列表排序選項
  static Future<int?> getUserListSortOption() async {
    try {
      final prefs = await _getPrefs();
      final result = prefs.getInt(_userListSortOptionKey);
      logger.d('獲取用戶列表排序選項: $result');
      return result;
    } catch (e) {
      logger.e('獲取用戶列表排序選項失敗: $e');
      return null;
    }
  }
  
  /// 保存用戶列表排序方向
  static Future<bool> saveUserListSortDirection(int sortDirectionIndex) async {
    try {
      final prefs = await _getPrefs();
      final result = await prefs.setInt(_userListSortDirectionKey, sortDirectionIndex);
      logger.d('保存用戶列表排序方向: $sortDirectionIndex');
      return result;
    } catch (e) {
      logger.e('保存用戶列表排序方向失敗: $e');
      return false;
    }
  }
  
  /// 獲取用戶列表排序方向
  static Future<int?> getUserListSortDirection() async {
    try {
      final prefs = await _getPrefs();
      final result = prefs.getInt(_userListSortDirectionKey);
      logger.d('獲取用戶列表排序方向: $result');
      return result;
    } catch (e) {
      logger.e('獲取用戶列表排序方向失敗: $e');
      return null;
    }
  }
  
  /// 同時保存用戶列表排序偏好
  static Future<bool> saveUserListSortPreferences(
    int sortOptionIndex, 
    int sortDirectionIndex
  ) async {
    try {
      final prefs = await _getPrefs();
      final results = await Future.wait([
        prefs.setInt(_userListSortOptionKey, sortOptionIndex),
        prefs.setInt(_userListSortDirectionKey, sortDirectionIndex),
      ]);
      
      final success = results.every((result) => result);
      logger.d('保存用戶列表排序偏好: 選項=$sortOptionIndex, 方向=$sortDirectionIndex, 成功=$success');
      return success;
    } catch (e) {
      logger.e('保存用戶列表排序偏好失敗: $e');
      return false;
    }
  }

  // ==================== 應用主題偏好 ====================
  
  /// 保存應用主題
  static Future<bool> saveTheme(String theme) async {
    try {
      final prefs = await _getPrefs();
      final result = await prefs.setString(_themeKey, theme);
      logger.d('保存應用主題: $theme');
      return result;
    } catch (e) {
      logger.e('保存應用主題失敗: $e');
      return false;
    }
  }
  
  /// 獲取應用主題
  static Future<String?> getTheme() async {
    try {
      final prefs = await _getPrefs();
      final result = prefs.getString(_themeKey);
      logger.d('獲取應用主題: $result');
      return result;
    } catch (e) {
      logger.e('獲取應用主題失敗: $e');
      return null;
    }
  }

  // ==================== 語言偏好 ====================
  
  /// 保存應用語言
  static Future<bool> saveLanguage(String language) async {
    try {
      final prefs = await _getPrefs();
      final result = await prefs.setString(_languageKey, language);
      logger.d('保存應用語言: $language');
      return result;
    } catch (e) {
      logger.e('保存應用語言失敗: $e');
      return false;
    }
  }
  
  /// 獲取應用語言
  static Future<String?> getLanguage() async {
    try {
      final prefs = await _getPrefs();
      final result = prefs.getString(_languageKey);
      logger.d('獲取應用語言: $result');
      return result;
    } catch (e) {
      logger.e('獲取應用語言失敗: $e');
      return null;
    }
  }

  // ==================== 用戶模式偏好 ====================
  
  /// 保存用戶模式
  static Future<bool> saveUserMode(String userMode) async {
    try {
      final prefs = await _getPrefs();
      final result = await prefs.setString(_userModeKey, userMode);
      logger.d('保存用戶模式: $userMode');
      return result;
    } catch (e) {
      logger.e('保存用戶模式失敗: $e');
      return false;
    }
  }
  
  /// 獲取用戶模式
  static Future<String?> getUserMode() async {
    try {
      final prefs = await _getPrefs();
      final result = prefs.getString(_userModeKey);
      logger.d('獲取用戶模式: $result');
      return result;
    } catch (e) {
      logger.e('獲取用戶模式失敗: $e');
      return null;
    }
  }

  // ==================== 流式模式偏好 ====================

  /// 保存流式模式設定
  static Future<bool> saveStreamingMode(bool enabled) async {
    try {
      final prefs = await _getPrefs();
      final result = await prefs.setBool(_streamingModeKey, enabled);
      logger.d('保存流式模式設定: $enabled');
      return result;
    } catch (e) {
      logger.e('保存流式模式設定失敗: $e');
      return false;
    }
  }

  /// 獲取流式模式設定
  static Future<bool> getStreamingMode() async {
    try {
      final prefs = await _getPrefs();
      final result = prefs.getBool(_streamingModeKey) ?? true; // 預設啟用
      logger.d('獲取流式模式設定: $result');
      return result;
    } catch (e) {
      logger.e('獲取流式模式設定失敗: $e');
      return true; // 預設啟用
    }
  }

  // ==================== 首次啟動偏好 ====================

  /// 檢查是否為首次啟動用戶
  static Future<bool> isFirstTimeUser() async {
    try {
      final prefs = await _getPrefs();
      final result = prefs.getBool(_isFirstTimeUserKey) ?? true;
      logger.d('檢查是否為首次啟動用戶: $result');
      return result;
    } catch (e) {
      logger.e('檢查首次啟動狀態失敗: $e');
      return true; // 發生錯誤時預設為首次啟動
    }
  }

  /// 標記用戶已完成首次啟動流程
  static Future<bool> markFirstTimeUserCompleted() async {
    try {
      final prefs = await _getPrefs();
      final result = await prefs.setBool(_isFirstTimeUserKey, false);
      logger.d('標記首次啟動流程已完成');
      return result;
    } catch (e) {
      logger.e('標記首次啟動流程完成失敗: $e');
      return false;
    }
  }

  /// 重置首次啟動狀態（用於測試或重置）
  static Future<bool> resetFirstTimeUserStatus() async {
    try {
      final prefs = await _getPrefs();
      final result = await prefs.setBool(_isFirstTimeUserKey, true);
      logger.d('重置首次啟動狀態');
      return result;
    } catch (e) {
      logger.e('重置首次啟動狀態失敗: $e');
      return false;
    }
  }

  // ==================== 每日星相推播設定 ====================

  /// 保存每日星相推播啟用狀態
  static Future<bool> saveDailyAstrologyNotificationEnabled(bool enabled) async {
    try {
      final prefs = await _getPrefs();
      final result = await prefs.setBool(_dailyAstrologyNotificationKey, enabled);
      logger.d('保存每日星相推播設定: $enabled');
      return result;
    } catch (e) {
      logger.e('保存每日星相推播設定失敗: $e');
      return false;
    }
  }

  /// 獲取每日星相推播啟用狀態
  static Future<bool> getDailyAstrologyNotificationEnabled() async {
    try {
      final prefs = await _getPrefs();
      final result = prefs.getBool(_dailyAstrologyNotificationKey) ?? true; // 預設啟用
      logger.d('獲取每日星相推播設定: $result');
      return result;
    } catch (e) {
      logger.e('獲取每日星相推播設定失敗: $e');
      return true; // 預設啟用
    }
  }

  /// 保存每日星相推播時間
  static Future<bool> saveDailyAstrologyNotificationTime(String time) async {
    try {
      final prefs = await _getPrefs();
      final result = await prefs.setString(_dailyAstrologyTimeKey, time);
      logger.d('保存每日星相推播時間: $time');
      return result;
    } catch (e) {
      logger.e('保存每日星相推播時間失敗: $e');
      return false;
    }
  }

  /// 獲取每日星相推播時間
  static Future<String> getDailyAstrologyNotificationTime() async {
    try {
      final prefs = await _getPrefs();
      final result = prefs.getString(_dailyAstrologyTimeKey) ?? '10:00'; // 預設上午10點
      logger.d('獲取每日星相推播時間: $result');
      return result;
    } catch (e) {
      logger.e('獲取每日星相推播時間失敗: $e');
      return '10:00'; // 預設上午10點
    }
  }

  /// 保存個人化推播啟用狀態
  static Future<bool> saveDailyAstrologyPersonalizedEnabled(bool enabled) async {
    try {
      final prefs = await _getPrefs();
      final result = await prefs.setBool(_dailyAstrologyPersonalizedKey, enabled);
      logger.d('保存個人化推播設定: $enabled');
      return result;
    } catch (e) {
      logger.e('保存個人化推播設定失敗: $e');
      return false;
    }
  }

  /// 獲取個人化推播啟用狀態
  static Future<bool> getDailyAstrologyPersonalizedEnabled() async {
    try {
      final prefs = await _getPrefs();
      final result = prefs.getBool(_dailyAstrologyPersonalizedKey) ?? true; // 預設啟用
      logger.d('獲取個人化推播設定: $result');
      return result;
    } catch (e) {
      logger.e('獲取個人化推播設定失敗: $e');
      return true; // 預設啟用
    }
  }

  // ==================== 事件偵測設定 ====================

  /// 保存最低事件分數設定
  static Future<bool> saveEventMinimumScore(double score) async {
    try {
      final prefs = await _getPrefs();
      final result = await prefs.setDouble(_eventMinimumScoreKey, score);
      logger.d('保存最低事件分數設定: $score');
      return result;
    } catch (e) {
      logger.e('保存最低事件分數設定失敗: $e');
      return false;
    }
  }

  /// 獲取最低事件分數設定
  static Future<double> getEventMinimumScore() async {
    try {
      final prefs = await _getPrefs();
      final result = prefs.getDouble(_eventMinimumScoreKey) ?? 20.0; // 預設20分
      logger.d('獲取最低事件分數設定: $result');
      return result;
    } catch (e) {
      logger.e('獲取最低事件分數設定失敗: $e');
      return 20.0; // 預設20分
    }
  }

  /// 保存啟用的事件類型設定
  static Future<bool> saveEventEnabledTypes(String enabledTypesJson) async {
    try {
      final prefs = await _getPrefs();
      final result = await prefs.setString(_eventEnabledTypesKey, enabledTypesJson);
      logger.d('保存啟用事件類型設定: $enabledTypesJson');
      return result;
    } catch (e) {
      logger.e('保存啟用事件類型設定失敗: $e');
      return false;
    }
  }

  /// 獲取啟用的事件類型設定
  static Future<String?> getEventEnabledTypes() async {
    try {
      final prefs = await _getPrefs();
      final result = prefs.getString(_eventEnabledTypesKey);
      logger.d('獲取啟用事件類型設定: $result');
      return result;
    } catch (e) {
      logger.e('獲取啟用事件類型設定失敗: $e');
      return null;
    }
  }

  /// 保存事件快取有效期設定
  static Future<bool> saveEventCacheExpiryDays(int days) async {
    try {
      final prefs = await _getPrefs();
      final result = await prefs.setInt(_eventCacheExpiryDaysKey, days);
      logger.d('保存事件快取有效期設定: $days');
      return result;
    } catch (e) {
      logger.e('保存事件快取有效期設定失敗: $e');
      return false;
    }
  }

  /// 獲取事件快取有效期設定
  static Future<int> getEventCacheExpiryDays() async {
    try {
      final prefs = await _getPrefs();
      final result = prefs.getInt(_eventCacheExpiryDaysKey) ?? 7; // 預設7天
      logger.d('獲取事件快取有效期設定: $result');
      return result;
    } catch (e) {
      logger.e('獲取事件快取有效期設定失敗: $e');
      return 7; // 預設7天
    }
  }

  // ==================== 工具方法 ====================
  
  /// 清除所有偏好設定
  static Future<bool> clearAllPreferences() async {
    try {
      final prefs = await _getPrefs();
      final result = await prefs.clear();
      logger.i('清除所有用戶偏好設定');
      return result;
    } catch (e) {
      logger.e('清除用戶偏好設定失敗: $e');
      return false;
    }
  }
  
  /// 清除特定鍵值的偏好設定
  static Future<bool> clearPreference(String key) async {
    try {
      final prefs = await _getPrefs();
      final result = await prefs.remove(key);
      logger.d('清除偏好設定: $key');
      return result;
    } catch (e) {
      logger.e('清除偏好設定失敗: $key, $e');
      return false;
    }
  }
  
  /// 檢查是否存在特定鍵值的偏好設定
  static Future<bool> hasPreference(String key) async {
    try {
      final prefs = await _getPrefs();
      final result = prefs.containsKey(key);
      logger.d('檢查偏好設定存在: $key = $result');
      return result;
    } catch (e) {
      logger.e('檢查偏好設定存在失敗: $key, $e');
      return false;
    }
  }
  
  /// 獲取所有偏好設定鍵值
  static Future<Set<String>> getAllKeys() async {
    try {
      final prefs = await _getPrefs();
      final keys = prefs.getKeys();
      logger.d('獲取所有偏好設定鍵值: ${keys.length} 個');
      return keys;
    } catch (e) {
      logger.e('獲取所有偏好設定鍵值失敗: $e');
      return <String>{};
    }
  }
}
