import 'package:clipboard/clipboard.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import '../../core/constants/astrology_constants.dart';
import '../../core/constants/zodiac_symbols.dart';
import '../../data/models/astrology/aspect_info.dart';
import '../../data/models/astrology/planet_position.dart';
import '../../presentation/themes/app_theme.dart';
import '../../presentation/viewmodels/chart_viewmodel.dart';

/// 顯示阿拉伯點（特殊點）資訊的 Widget
class ArabicPointsWidget extends StatefulWidget {
  final ChartViewModel viewModel;

  const ArabicPointsWidget({
    Key? key,
    required this.viewModel,
  }) : super(key: key);

  @override
  State<ArabicPointsWidget> createState() => _ArabicPointsWidgetState();
}

class _ArabicPointsWidgetState extends State<ArabicPointsWidget> {
  // 使用 Map 來跟蹤每個阿拉伯點的展開/收起狀態
  final Map<int, bool> _expandedState = {};

  @override
  Widget build(BuildContext context) {
    // 獲取阿拉伯點資料
    final List<PlanetPosition> arabicPoints =
        widget.viewModel.chartData.arabicPoints ?? [];

    if (arabicPoints.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 120,
              height: 120,
              decoration: const BoxDecoration(
                color: AppColors.softGray,
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.star_border,
                size: 60,
                color: AppColors.textMedium,
              ),
            ),
            const SizedBox(height: 24),
            const Text(
              '沒有可用的特殊點資料',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppColors.textDark,
              ),
            ),
            const SizedBox(height: 12),
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 32),
              child: Text(
                '請在設定中啟用特殊點計算，或者選擇不同的占星圖表。',
                style: TextStyle(
                  fontSize: 14,
                  color: AppColors.textMedium,
                  height: 1.5,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      );
    }

    return Container(
      decoration: BoxDecoration(
        color: AppColors.scaffoldBackground,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 標題區域
          Container(
            padding: const EdgeInsets.fromLTRB(16.0, 16.0, 16.0, 12.0),
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
              boxShadow: [
                BoxShadow(
                  color: Color(0x0D000000),
                  blurRadius: 4,
                  offset: Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: AppColors.royalIndigo.withOpacity(0.1),
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.star,
                            color: AppColors.royalIndigo,
                            size: 20,
                          ),
                        ),
                        const SizedBox(width: 12),
                        const Text(
                          '特殊點（阿拉伯點）',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: AppColors.textDark,
                          ),
                        ),
                      ],
                    ),
                    if (kDebugMode) ...[
                      // 複製按鈕
                      ElevatedButton.icon(
                        onPressed: () {
                          _showCopyOptionsDialog(context, arabicPoints);
                        },
                        icon: const Icon(Icons.copy, size: 18),
                        label: const Text('複製'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.royalIndigo,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(
                              horizontal: 12, vertical: 8),
                          shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8)),
                        ),
                      ),
                    ]
                  ],
                ),
                const SizedBox(height: 12),
                const Text(
                  '阿拉伯點是古典占星學中的特殊計算點，用於提供更深入的占星解讀。點擊卡片可展開查看詳細資訊。',
                  style: TextStyle(
                    fontSize: 14,
                    color: AppColors.textMedium,
                    height: 1.4,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 8),
          // 列表區域
          Expanded(
            child: ListView.builder(
              padding:
                  const EdgeInsets.symmetric(horizontal: 12.0, vertical: 6.0),
              itemCount: arabicPoints.length,
              itemBuilder: (context, index) {
                final point = arabicPoints[index];
                return _buildArabicPointCard(context, point);
              },
            ),
          ),
        ],
      ),
    );
  }

  /// 構建阿拉伯點卡片
  Widget _buildArabicPointCard(BuildContext context, PlanetPosition point) {
    // 獲取阿拉伯點的描述
    String description = '';
    for (final arabicPoint in AstrologyConstants.ARABIC_POINTS) {
      if (arabicPoint['id'] == point.id) {
        description = arabicPoint['description'] as String;
        break;
      }
    }

    // 獲取阿拉伯點的相位信息
    final aspects = _getArabicPointAspects(point);

    // 如果這個點位在 _expandedState 中沒有記錄，預設為收起狀態
    _expandedState.putIfAbsent(point.id, () => false);
    final bool isExpanded = _expandedState[point.id] ?? false;

    // 獲取宮主星信息
    final houseRulerInfo = _getHouseRulers(point);
    final rulerName = houseRulerInfo['name'] as String;
    final hasHouseRuler = rulerName != '未知';
    final house = houseRulerInfo['house'] as int? ?? 0;
    final sign = houseRulerInfo['sign'] as String? ?? '';
    final hasFullRulerInfo = hasHouseRuler && house != 0;

    return Card(
        color: Colors.white,
        elevation: 2.0,
        margin: const EdgeInsets.symmetric(vertical: 6.0),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: () {
            if (description.isNotEmpty || aspects.isNotEmpty) {
              setState(() {
                _expandedState[point.id] = !isExpanded;
              });
            }
          },
          child: Padding(
            padding: const EdgeInsets.all(12.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    // 左側圖標（縮小）
                    Container(
                      width: 36,
                      height: 36,
                      decoration: BoxDecoration(
                        color: point.color.withOpacity(0.15),
                        shape: BoxShape.circle,
                      ),
                      child: Center(
                        child: Text(
                          point.symbol,
                          style: TextStyle(
                            fontSize: 16,
                            color: point.color,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    // 主要信息區（兩行布局）
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // 第一行：名稱 + 度數
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Expanded(
                                child: Text(
                                  point.name,
                                  style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: AppColors.textDark,
                                  ),
                                ),
                              ),
                              Text(
                                widget.viewModel.formatDegree(point.longitude % 30),
                                style: const TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.textDark,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 4),
                          // 第二行：星座 + 宮位 + 宮主星（如果有）
                          Row(
                            children: [
                              // 星座
                              Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 6, vertical: 2),
                                decoration: BoxDecoration(
                                  color: ZodiacSymbols.getZodiacColor(point.sign)
                                      .withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Text(
                                      ZodiacSymbols.getZodiacSymbol(point.sign),
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: ZodiacSymbols.getZodiacColor(point.sign),
                                      ),
                                    ),
                                    const SizedBox(width: 2),
                                    Text(
                                      point.sign,
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: ZodiacSymbols.getZodiacColor(point.sign),
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              const SizedBox(width: 6),
                              // 宮位
                              Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 6, vertical: 2),
                                decoration: BoxDecoration(
                                  color: AppColors.royalIndigo.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Text(
                                  '第${point.house}宮',
                                  style: const TextStyle(
                                    fontSize: 12,
                                    color: AppColors.royalIndigo,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                              // 宮主星（如果有）
                              if (hasHouseRuler) ...[
                                const SizedBox(width: 6),
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 6, vertical: 2),
                                  decoration: BoxDecoration(
                                    color: AppColors.solarAmber.withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Text(
                                    hasFullRulerInfo
                                        ? '$rulerName@$sign${house}宮'
                                        : rulerName,
                                    style: const TextStyle(
                                      fontSize: 11,
                                      color: AppColors.solarAmber,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ),
                              ],
                            ],
                          ),
                        ],
                      ),
                    ),
                    // 右側展開按鈕（如果有內容）
                    if (description.isNotEmpty || aspects.isNotEmpty)
                      Container(
                        width: 24,
                        height: 24,
                        decoration: BoxDecoration(
                          color: AppColors.softGray,
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          isExpanded
                              ? Icons.keyboard_arrow_up
                              : Icons.keyboard_arrow_down,
                          color: AppColors.royalIndigo,
                          size: 16,
                        ),
                      ),
                  ],
                ),
                if ((description.isNotEmpty || aspects.isNotEmpty) &&
                    isExpanded) ...[
                  const SizedBox(height: 8),
                  const Divider(),
                  const SizedBox(height: 6),

                  // 顯示描述信息
                  if (description.isNotEmpty) ...[
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: AppColors.softGray.withOpacity(0.5),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Row(
                            children: [
                              Icon(
                                Icons.info_outline,
                                color: AppColors.royalIndigo,
                                size: 16,
                              ),
                              SizedBox(width: 6),
                              Text(
                                '點位描述',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.royalIndigo,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Text(
                            description,
                            style: const TextStyle(
                              fontSize: 14,
                              color: AppColors.textDark,
                              height: 1.5,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 12),
                  ],

                  // 顯示相位信息
                  if (aspects.isNotEmpty) ...[
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: AppColors.royalIndigo.withOpacity(0.05),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: AppColors.royalIndigo.withOpacity(0.1),
                          width: 1,
                        ),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Row(
                            children: [
                              Icon(
                                Icons.timeline,
                                color: AppColors.royalIndigo,
                                size: 16,
                              ),
                              SizedBox(width: 6),
                              Text(
                                '相位信息',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.royalIndigo,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 10),
                          Wrap(
                            spacing: 8,
                            runSpacing: 8,
                            children: aspects.map((aspect) {
                              // 確定另一個行星
                              final otherPlanet = aspect.planet1.id == point.id
                                  ? aspect.planet2
                                  : aspect.planet1;

                              // 確定相位顏色
                              final aspectColor =
                                  _getAspectColor(aspect.aspectType);

                              // 判斷相位方向
                              final String directionText =
                                  aspect.direction != null
                                      ? ' (${aspect.getDirectionText()})'
                                      : '';

                              // 格式化誤差值
                              final String orbText =
                                  aspect.orb.toStringAsFixed(2);

                              return Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 8, vertical: 6),
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(12),
                                  boxShadow: [
                                    BoxShadow(
                                      color: aspectColor.withOpacity(0.1),
                                      blurRadius: 4,
                                      offset: const Offset(0, 2),
                                    ),
                                  ],
                                  border: Border.all(
                                      color: aspectColor.withOpacity(0.3),
                                      width: 1),
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    // 相位符號
                                    Container(
                                      padding: const EdgeInsets.all(4),
                                      decoration: BoxDecoration(
                                        color: aspectColor.withOpacity(0.15),
                                        shape: BoxShape.circle,
                                      ),
                                      child: Text(
                                        aspect.symbol,
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: aspectColor,
                                          fontFamily: "astro_one_font",
                                        ),
                                      ),
                                    ),
                                    const SizedBox(width: 6),

                                    // 行星信息（一行顯示）
                                    RichText(
                                      text: TextSpan(
                                        children: [
                                          TextSpan(
                                            text: otherPlanet.name,
                                            style: TextStyle(
                                              fontSize: 13,
                                              color: otherPlanet.color,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                          TextSpan(
                                            text: ' ${aspect.shortZh}$directionText ($orbText°)',
                                            style: const TextStyle(
                                              fontSize: 12,
                                              color: AppColors.textMedium,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            }).toList(),
                          ),
                        ],
                      ),
                    ),
                  ],
                ],
              ],
            ),
          ),
        ));
  }

  /// 獲取阿拉伯點的相位信息
  List<AspectInfo> _getArabicPointAspects(PlanetPosition arabicPoint) {
    // 獲取所有相位
    final allAspects = widget.viewModel.chartData.aspects ?? [];

    // 先檢查現有的相位中是否已包含該阿拉伯點的相位
    final existingAspects = allAspects
        .where((aspect) =>
            aspect.planet1.id == arabicPoint.id ||
            aspect.planet2.id == arabicPoint.id)
        .toList();

    // 如果已經有相位，則直接返回
    if (existingAspects.isNotEmpty) {
      return existingAspects;
    }

    // 如果沒有現有相位，手動計算所有阿拉伯點的相位
    // 獲取所有行星
    final planets = widget.viewModel.chartData.planets ?? [];

    // 手動計算阿拉伯點與主要行星的相位
    List<AspectInfo> arabicPointAspects = [];

    // 過濾出主要行星（太陽到冥王星）
    final mainPlanets = planets.where((p) => p.id >= 0 && p.id <= 9).toList();

    for (final planet in mainPlanets) {
      // 計算阿拉伯點與行星之間的角度差
      double angleDiff = (planet.longitude - arabicPoint.longitude).abs();
      if (angleDiff > 180) angleDiff = 360 - angleDiff;

      // 檢查是否形成相位
      for (final aspectType in AstrologyConstants.ASPECTS) {
        final int angle = aspectType['angle'] as int;
        final double orb = aspectType['orb'] as double;

        if ((angleDiff - angle).abs() <= orb) {
          // 判斷入相或出相
          AspectDirection? direction;
          if (planet.longitudeSpeed != 0) {
            // 如果行星速度為負，則角度差減小為入相，增大為出相
            // 如果行星速度為正，則角度差增大為入相，減小為出相
            // 由於阿拉伯點沒有速度，我們只考慮行星的速度
            if ((angleDiff < angle && planet.longitudeSpeed > 0) ||
                (angleDiff > angle && planet.longitudeSpeed < 0)) {
              direction = AspectDirection.applying; // 入相
            } else {
              direction = AspectDirection.separating; // 出相
            }
          }

          // 形成相位
          arabicPointAspects.add(AspectInfo(
            planet1: arabicPoint,
            planet2: planet,
            aspectType: aspectType['name'] as String,
            shortZh: aspectType['shortZh'] as String,
            symbol: aspectType['symbol'] as String,
            angle: angle,
            orb: (angleDiff - angle).abs(),
            direction: direction,
          ));
        }
      }
    }

    return arabicPointAspects;
  }

  /// 根據相位類型返回對應的顏色
  Color _getAspectColor(String aspectType) {
    switch (aspectType) {
      case '合相':
        return const Color(0xFF224EA5); // 藍色
      case '六分相':
        return const Color(0xFF2999A4); // 淺藍色
      case '四分相':
        return Colors.red; // 紅色
      case '三分相':
        return Colors.green; // 綠色
      case '對分相':
        return const Color(0xFF051883); // 深藍色
      case '接納':
        return Colors.purple; // 紫色
      default:
        return Colors.grey; // 灰色
    }
  }

  /// 顯示複製選項對話框
  void _showCopyOptionsDialog(
      BuildContext context, List<PlanetPosition> arabicPoints) {
    // 用於跟蹤每個阿拉伯點的選中狀態
    Map<int, bool> selectedPoints = {};

    // 初始化所有點為選中狀態
    for (final point in arabicPoints) {
      selectedPoints[point.id] = true;
    }

    // 全選狀態
    bool selectAll = true;

    // 是否包含相位信息
    bool includeAspects = true;

    // 是否包含宮主星信息
    bool includeHouseRulers = true;

    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('選擇要複製的阿拉伯點'),
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16)),
              contentPadding: const EdgeInsets.fromLTRB(24, 20, 24, 0),
              content: SizedBox(
                width: double.maxFinite,
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 複製選項區域
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: AppColors.softGray.withOpacity(0.5),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              '複製選項',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: AppColors.textDark,
                              ),
                            ),
                            const SizedBox(height: 12),
                            // 包含相位信息選項
                            SwitchListTile(
                              title: const Text('包含相位信息'),
                              subtitle: const Text('顯示每個阿拉伯點的相位關係'),
                              value: includeAspects,
                              activeColor: AppColors.royalIndigo,
                              contentPadding: EdgeInsets.zero,
                              onChanged: (value) {
                                setState(() {
                                  includeAspects = value;
                                });
                              },
                            ),
                            // 包含宮主星信息選項
                            SwitchListTile(
                              title: const Text('包含主星信息'),
                              subtitle: const Text('顯示每個阿拉伯點所屬的主星及其宮位'),
                              value: includeHouseRulers,
                              activeColor: AppColors.royalIndigo,
                              contentPadding: EdgeInsets.zero,
                              onChanged: (value) {
                                setState(() {
                                  includeHouseRulers = value;
                                });
                              },
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 20),

                      // 點位選擇區域
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text(
                            '選擇點位',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: AppColors.textDark,
                            ),
                          ),
                          // 全選按鈕
                          TextButton.icon(
                            icon: Icon(
                              selectAll
                                  ? Icons.check_box
                                  : Icons.check_box_outline_blank,
                              color: AppColors.royalIndigo,
                              size: 20,
                            ),
                            label: Text(
                              selectAll ? '取消全選' : '全選',
                              style:
                                  const TextStyle(color: AppColors.royalIndigo),
                            ),
                            style: TextButton.styleFrom(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 12, vertical: 8),
                              shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8)),
                            ),
                            onPressed: () {
                              setState(() {
                                selectAll = !selectAll;
                                // 更新所有點的選中狀態
                                for (final point in arabicPoints) {
                                  selectedPoints[point.id] = selectAll;
                                }
                              });
                            },
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),

                      // 各個阿拉伯點的選項
                      ...arabicPoints.map((point) {
                        return Card(
                          margin: const EdgeInsets.only(bottom: 8),
                          shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12)),
                          elevation: 0,
                          color: selectedPoints[point.id] == true
                              ? AppColors.royalIndigo.withOpacity(0.1)
                              : AppColors.softGray,
                          child: InkWell(
                            borderRadius: BorderRadius.circular(12),
                            onTap: () {
                              setState(() {
                                selectedPoints[point.id] =
                                    !(selectedPoints[point.id] ?? false);
                                // 檢查是否所有點都被選中，更新全選狀態
                                selectAll = arabicPoints
                                    .every((p) => selectedPoints[p.id] == true);
                              });
                            },
                            child: Padding(
                              padding: const EdgeInsets.all(12),
                              child: Row(
                                children: [
                                  // 選擇框
                                  Container(
                                    width: 24,
                                    height: 24,
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      color: selectedPoints[point.id] == true
                                          ? AppColors.royalIndigo
                                          : Colors.white,
                                      border: Border.all(
                                        color: selectedPoints[point.id] == true
                                            ? AppColors.royalIndigo
                                            : AppColors.textLight,
                                        width: 2,
                                      ),
                                    ),
                                    child: selectedPoints[point.id] == true
                                        ? const Icon(
                                            Icons.check,
                                            size: 16,
                                            color: Colors.white,
                                          )
                                        : null,
                                  ),
                                  const SizedBox(width: 12),
                                  // 點位信息
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          point.name,
                                          style: const TextStyle(
                                            fontSize: 16,
                                            fontWeight: FontWeight.bold,
                                            color: AppColors.textDark,
                                          ),
                                        ),
                                        const SizedBox(height: 4),
                                        Text(
                                          '${point.sign} ${widget.viewModel.formatDegree(point.longitude % 30)}, ${point.getHouseText()}',
                                          style: const TextStyle(
                                            fontSize: 14,
                                            color: AppColors.textMedium,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        );
                      }).toList(),
                    ],
                  ),
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('取消'),
                  style: TextButton.styleFrom(
                      foregroundColor: AppColors.textMedium),
                ),
                ElevatedButton(
                  onPressed: () {
                    // 獲取選中的阿拉伯點
                    final selectedArabicPoints = arabicPoints
                        .where((point) => selectedPoints[point.id] == true)
                        .toList();

                    // 複製選中的阿拉伯點信息
                    _copyArabicPointsInfo(
                      context,
                      selectedArabicPoints,
                      includeAspects: includeAspects,
                      includeHouseRulers: includeHouseRulers,
                    );

                    Navigator.of(context).pop();
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.royalIndigo,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8)),
                  ),
                  child: const Text('複製'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  /// 複製選中的阿拉伯點信息
  void _copyArabicPointsInfo(
    BuildContext context,
    List<PlanetPosition> selectedPoints, {
    bool includeAspects = true,
    bool includeHouseRulers = true,
  }) async {
    if (selectedPoints.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('沒有選擇任何阿拉伯點')),
      );
      return;
    }

    try {
      // 生成專門的阿拉伯點文本
      final String arabicPointsText = _generateArabicPointsText(
        selectedPoints,
        includeAspects: includeAspects,
        includeHouseRulers: includeHouseRulers,
      );

      // 複製到剪貼板
      await FlutterClipboard.copy(arabicPointsText);

      // 顯示成功提示
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('阿拉伯點資訊已複製到剪貼板')),
        );
      }
    } catch (e) {
      // 顯示錯誤提示
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('複製失敗: $e')),
        );
      }
    }
  }

  /// 生成專門的阿拉伯點文本
  String _generateArabicPointsText(
    List<PlanetPosition> selectedPoints, {
    bool includeAspects = true,
    bool includeHouseRulers = true,
  }) {
    final StringBuffer text = StringBuffer();

    // 標題
    text.writeln('===============');
    text.writeln('特殊點（阿拉伯點）');
    text.writeln('===============');
    text.writeln();

    for (int i = 0; i < selectedPoints.length; i++) {
      final point = selectedPoints[i];

      // 基本信息
      text.writeln('${i + 1}. ${point.name}');
      text.writeln('   位置: ${point.sign} ${widget.viewModel.formatDegree(point.longitude % 30)}');
      text.writeln('   宮位: ${point.getHouseText()}');

      // 宮主星信息
      if (includeHouseRulers) {
        final houseRulerInfo = _getHouseRulers(point);
        final rulerName = houseRulerInfo['name'] as String;
        final house = houseRulerInfo['house'] as int? ?? 0;
        final sign = houseRulerInfo['sign'] as String? ?? '';

        if (rulerName != '未知' && house != 0) {
          text.writeln('   宮主星: $rulerName 在 $sign 第${house}宮');
        } else if (rulerName != '未知') {
          text.writeln('   宮主星: $rulerName');
        }
      }

      // 相位信息
      if (includeAspects) {
        final aspects = _getArabicPointAspects(point);
        if (aspects.isNotEmpty) {
          text.writeln('   相位:');
          for (final aspect in aspects) {
            final otherPlanet = aspect.planet1.id == point.id
                ? aspect.planet2
                : aspect.planet1;
            final directionText = aspect.direction != null
                ? ' ${aspect.getDirectionText()}'
                : '';
            text.writeln('     ${aspect.shortZh} ${otherPlanet.name} (容許度: ${aspect.orb.toStringAsFixed(2)}°$directionText)');
          }
        }
      }

      // 描述信息
      String description = '';
      for (final arabicPoint in AstrologyConstants.ARABIC_POINTS) {
        if (arabicPoint['id'] == point.id) {
          description = arabicPoint['description'] as String;
          break;
        }
      }

      if (description.isNotEmpty) {
        text.writeln('   描述: $description');
      }

      // 在每個點之間加上空行，除了最後一個
      if (i < selectedPoints.length - 1) {
        text.writeln();
      }
    }

    return text.toString();
  }

  /// 獲取阿拉伯點所屬星座的宮主星及其宮位信息
  Map<String, dynamic> _getHouseRulers(PlanetPosition point) {
    // 根據星座確定宮主星
    String rulerName;
    switch (point.sign) {
      case '牡羊座':
        rulerName = '火星';
        break; // 牡羊座
      case '金牛座':
        rulerName = '金星';
        break; // 金牛座
      case '雙子座':
        rulerName = '水星';
        break; // 雙子座
      case '巨蟹座':
        rulerName = '月亮';
        break; // 巨蟹座
      case '獅子座':
        rulerName = '太陽';
        break; // 獅子座
      case '處女座':
        rulerName = '水星';
        break; // 處女座
      case '天秤座':
        rulerName = '金星';
        break; // 天秤座
      case '天蠍座':
        rulerName = '火星';
        break; // 天蠍座
      case '射手座':
        rulerName = '木星';
        break; // 射手座
      case '摩羯座':
        rulerName = '土星';
        break; // 摩羯座
      case '水瓶座':
        rulerName = '土星';
        break; // 水瓶座
      case '雙魚座':
        rulerName = '木星';
        break; // 雙魚座
      default:
        rulerName = '未知';
        break;
    }

    // 如果沒有找到宮主星，返回空信息
    if (rulerName == '未知') {
      return {'name': '未知', 'house': 0};
    }

    // 在行星列表中尋找宮主星
    final planets = widget.viewModel.chartData.planets ?? [];
    final ruler = planets.where((p) => p.name == rulerName).toList();

    // 如果找到宮主星，返回其名稱和宮位
    if (ruler.isNotEmpty) {
      return {
        'name': rulerName,
        'house': ruler.first.house,
        'sign': ruler.first.sign,
      };
    } else {
      // 如果沒有找到宮主星，返回空信息
      return {'name': rulerName, 'house': 0};
    }
  }

  /// 將度數轉換為「星座度分秒格式」
  String formatZodiacDegree(double degree) {
    final List<String> zodiacSigns = [
      '牡羊座',
      '金牛座',
      '雙子座',
      '巨蟹座',
      '獅子座',
      '處女座',
      '天秤座',
      '天蠍座',
      '射手座',
      '摩羯座',
      '水瓶座',
      '雙魚座'
    ];

    degree = degree % 360; // 保證在 0~360 度內
    int zodiacIndex = (degree / 30).floor();
    double degreeInSign = degree - (zodiacIndex * 30);

    final int deg = degreeInSign.floor();
    final double minDouble = (degreeInSign - deg) * 60;
    final int min = minDouble.floor();
    final int sec = ((minDouble - min) * 60).round();

    String zodiac = zodiacSigns[zodiacIndex];
    return '$deg°$min\'$sec" $zodiac';
  }
}
