import 'package:flutter/material.dart';

import '../../core/utils/logger_utils.dart';
import '../../data/models/astrology/chart_type.dart';
import '../../data/models/user/birth_data.dart';
import '../../presentation/themes/app_theme.dart';
import '../../presentation/viewmodels/chart_viewmodel.dart';
import 'common/date_time_picker_bottom_sheet.dart';

class TimeAdjustmentWidget extends StatefulWidget {
  final ChartViewModel viewModel;

  const TimeAdjustmentWidget({
    Key? key,
    required this.viewModel,
  }) : super(key: key);

  @override
  State<TimeAdjustmentWidget> createState() => _TimeAdjustmentWidgetState();
}

class _TimeAdjustmentWidgetState extends State<TimeAdjustmentWidget> {
  // 調整單位選項
  final List<String> _adjustmentUnits = [
    '5分鐘',
    '30分鐘',
    '1小時',
    '1天',
    '1個月',
    '1年'
  ];
  String _selectedUnit = '5分鐘';

  // 記錄原始日期和調整後的日期 - 主要人物
  late DateTime _originalPrimaryDate;
  late DateTime _adjustedPrimaryDate;
  int _totalPrimaryAdjustment = 0;

  // 記錄原始日期和調整後的日期 - 次要人物（合盤用）
  late DateTime _originalSecondaryDate;
  late DateTime _adjustedSecondaryDate;
  int _totalSecondaryAdjustment = 0;

  // 記錄原始日期和調整後的日期 - 推運時間
  late DateTime _originalTransitDate;
  late DateTime _adjustedTransitDate;
  int _totalTransitAdjustment = 0;

  @override
  void initState() {
    super.initState();
    _resetAdjustment();
  }

  @override
  void didUpdateWidget(TimeAdjustmentWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    // 如果星盤數據變更，重置調整
    // 注意：我們一定要重置調整，因為在切換星盤時，即使屬性相同，也應該重置
    bool shouldReset = false;
    _resetAdjustment();
    // 檢查星盤數據是否變更
    if (oldWidget.viewModel.chartData != widget.viewModel.chartData) {
      print('星盤數據變更，重置調整');
      shouldReset = true;
    }

    // 檢查行星數據是否變更
    if (!shouldReset &&
        oldWidget.viewModel.chartData.planets !=
            widget.viewModel.chartData.planets) {
      print('行星數據變更，重置調整');
      shouldReset = true;
    }

    // 檢查宮位數據是否變更
    if (!shouldReset &&
        oldWidget.viewModel.chartData.houses !=
            widget.viewModel.chartData.houses) {
      print('宮位數據變更，重置調整');
      shouldReset = true;
    }

    // 檢查其他屬性是否變更
    if (!shouldReset &&
        (oldWidget.viewModel.chartType != widget.viewModel.chartType ||
            oldWidget.viewModel.primaryPerson !=
                widget.viewModel.primaryPerson ||
            oldWidget.viewModel.secondaryPerson !=
                widget.viewModel.secondaryPerson ||
            oldWidget.viewModel.specificDate !=
                widget.viewModel.specificDate)) {
      print('其他屬性變更，重置調整');
      shouldReset = true;
    }

    // 檢查星盤更新計數器是否變更
    if (!shouldReset &&
        oldWidget.viewModel.chartUpdateCounter !=
            widget.viewModel.chartUpdateCounter) {
      print(
          '星盤更新計數器變更，重置調整: ${oldWidget.viewModel.chartUpdateCounter} -> ${widget.viewModel.chartUpdateCounter}');
      shouldReset = true;
    }
  }

  // 重置調整
  void _resetAdjustment() {
    // 初始化主要人物的日期
    _originalPrimaryDate = widget.viewModel.primaryPerson.dateTime;
    _adjustedPrimaryDate = _originalPrimaryDate;
    _totalPrimaryAdjustment = 0;

    // 初始化次要人物的日期（如果有的話）
    if (widget.viewModel.secondaryPerson != null) {
      _originalSecondaryDate = widget.viewModel.secondaryPerson!.dateTime;
      _adjustedSecondaryDate = _originalSecondaryDate;
      _totalSecondaryAdjustment = 0;
    } else {
      _originalSecondaryDate = DateTime.now();
      _adjustedSecondaryDate = _originalSecondaryDate;
      _totalSecondaryAdjustment = 0;
    }

    // 初始化推運時間
    _originalTransitDate = widget.viewModel.specificDate ?? DateTime.now();
    _adjustedTransitDate = _originalTransitDate;
    _totalTransitAdjustment = 0;
  }

  // 重置為預設時間
  void _resetToDefaultTime() {
    setState(() {
      _resetAdjustment();
    });

    // 重要！更新星盤
    _updateChart();
    logger.d('重置為預設時間');
  }

  // 調整主要人物時間
  void _adjustPrimaryTime(bool increase) {
    _adjustTimeInternal(increase, 'primary');
  }

  // 調整次要人物時間
  void _adjustSecondaryTime(bool increase) {
    _adjustTimeInternal(increase, 'secondary');
  }

  // 調整推運時間
  void _adjustTransitTime(bool increase) {
    _adjustTimeInternal(increase, 'transit');
  }

  // 內部調整時間方法
  void _adjustTimeInternal(bool increase, String type) {
    setState(() {
      Duration adjustment;
      int minutesAdjustment = 0;

      // 根據選擇的單位確定調整量
      switch (_selectedUnit) {
        case '5分鐘':
          adjustment = const Duration(minutes: 5);
          minutesAdjustment = 5;
          break;
        case '30分鐘':
          adjustment = const Duration(minutes: 30);
          minutesAdjustment = 30;
          break;
        case '1小時':
          adjustment = const Duration(hours: 1);
          minutesAdjustment = 60;
          break;
        case '1天':
          adjustment = const Duration(days: 1);
          minutesAdjustment = 1440; // 1天 = 1440分鐘
          break;
        case '1個月':
          // 月份調整需要特殊處理
          _handleMonthAdjustment(increase, type);
          return;
        case '1年':
          // 年份調整需要特殊處理
          _handleYearAdjustment(increase, type);
          return;
        default:
          adjustment = const Duration(minutes: 5);
          minutesAdjustment = 5;
      }

      // 根據類型應用調整
      switch (type) {
        case 'primary':
          _adjustedPrimaryDate = increase
              ? _adjustedPrimaryDate.add(adjustment)
              : _adjustedPrimaryDate.subtract(adjustment);
          _totalPrimaryAdjustment +=
              increase ? minutesAdjustment : -minutesAdjustment;
          break;
        case 'secondary':
          _adjustedSecondaryDate = increase
              ? _adjustedSecondaryDate.add(adjustment)
              : _adjustedSecondaryDate.subtract(adjustment);
          _totalSecondaryAdjustment +=
              increase ? minutesAdjustment : -minutesAdjustment;
          break;
        case 'transit':
          _adjustedTransitDate = increase
              ? _adjustedTransitDate.add(adjustment)
              : _adjustedTransitDate.subtract(adjustment);
          _totalTransitAdjustment +=
              increase ? minutesAdjustment : -minutesAdjustment;
          break;
      }

      logger.d('調整時間: $type $_selectedUnit ${increase ? '增加' : '減少'}');

      // 更新星盤
      _updateChart();
    });
  }

  // 處理月份調整
  void _handleMonthAdjustment(bool increase, String type) {
    DateTime currentDate;
    switch (type) {
      case 'primary':
        currentDate = _adjustedPrimaryDate;
        break;
      case 'secondary':
        currentDate = _adjustedSecondaryDate;
        break;
      case 'transit':
        currentDate = _adjustedTransitDate;
        break;
      default:
        return;
    }

    final newDate = DateTime(
      currentDate.year,
      currentDate.month + (increase ? 1 : -1),
      currentDate.day,
      currentDate.hour,
      currentDate.minute,
    );

    switch (type) {
      case 'primary':
        _adjustedPrimaryDate = newDate;
        _totalPrimaryAdjustment += increase ? 43200 : -43200; // 約30天 = 43200分鐘
        break;
      case 'secondary':
        _adjustedSecondaryDate = newDate;
        _totalSecondaryAdjustment += increase ? 43200 : -43200;
        break;
      case 'transit':
        _adjustedTransitDate = newDate;
        _totalTransitAdjustment += increase ? 43200 : -43200;
        break;
    }

    logger.d('調整時間: $type 月份${increase ? '增加' : '減少'}, 結果: $newDate');
    _updateChart();
  }

  // 處理年份調整
  void _handleYearAdjustment(bool increase, String type) {
    DateTime currentDate;
    switch (type) {
      case 'primary':
        currentDate = _adjustedPrimaryDate;
        break;
      case 'secondary':
        currentDate = _adjustedSecondaryDate;
        break;
      case 'transit':
        currentDate = _adjustedTransitDate;
        break;
      default:
        return;
    }

    final newDate = DateTime(
      currentDate.year + (increase ? 1 : -1),
      currentDate.month,
      currentDate.day,
      currentDate.hour,
      currentDate.minute,
    );

    switch (type) {
      case 'primary':
        _adjustedPrimaryDate = newDate;
        _totalPrimaryAdjustment +=
            increase ? 525600 : -525600; // 約365天 = 525600分鐘
        break;
      case 'secondary':
        _adjustedSecondaryDate = newDate;
        _totalSecondaryAdjustment += increase ? 525600 : -525600;
        break;
      case 'transit':
        _adjustedTransitDate = newDate;
        _totalTransitAdjustment += increase ? 525600 : -525600;
        break;
    }

    logger.d('調整時間: $type 年份${increase ? '增加' : '減少'}, 結果: $newDate');
    _updateChart();
  }

  // 更新星盤
  void _updateChart() {
    // 更新主要人物
    final updatedPrimaryPerson = BirthData(
      id: widget.viewModel.primaryPerson.id,
      name: widget.viewModel.primaryPerson.name,
      dateTime: _adjustedPrimaryDate,
      birthPlace: widget.viewModel.primaryPerson.birthPlace,
      latitude: widget.viewModel.primaryPerson.latitude,
      longitude: widget.viewModel.primaryPerson.longitude,
      notes: widget.viewModel.primaryPerson.notes,
      createdAt: widget.viewModel.primaryPerson.createdAt,
    );
    widget.viewModel.setPrimaryPerson(updatedPrimaryPerson);

    // 更新次要人物（如果有的話）
    if (widget.viewModel.secondaryPerson != null) {
      final updatedSecondaryPerson = BirthData(
        id: widget.viewModel.secondaryPerson!.id,
        name: widget.viewModel.secondaryPerson!.name,
        dateTime: _adjustedSecondaryDate,
        birthPlace: widget.viewModel.secondaryPerson!.birthPlace,
        latitude: widget.viewModel.secondaryPerson!.latitude,
        longitude: widget.viewModel.secondaryPerson!.longitude,
        notes: widget.viewModel.secondaryPerson!.notes,
        createdAt: widget.viewModel.secondaryPerson!.createdAt,
      );
      widget.viewModel.setSecondaryPerson(updatedSecondaryPerson);
    }

    // 更新推運時間（如果需要的話）
    if (widget.viewModel.chartType.isPredictiveChart ||
        widget.viewModel.chartType.isRelationshipPredictiveChart ||
        widget.viewModel.chartType.isReturnChart) {
      widget.viewModel.setSpecificDate(_adjustedTransitDate);

      // 如果是法達盤，需要重新計算法達盤數據
      if (widget.viewModel.chartType == ChartType.firdaria) {
        widget.viewModel.calculateFirdaria();
      }
    }
  }

  // 格式化調整量顯示
  String _formatAdjustment(int totalAdjustment) {
    if (totalAdjustment == 0) return '';

    final sign = totalAdjustment > 0 ? '+' : '';

    if (totalAdjustment.abs() < 60) {
      // 小於1小時，顯示分鐘
      return ' ($sign$totalAdjustment分鐘)';
    } else if (totalAdjustment.abs() < 1440) {
      // 小於1天，顯示小時
      final hours = totalAdjustment / 60;
      return ' ($sign${hours.toStringAsFixed(1)}小時)';
    } else if (totalAdjustment.abs() < 43200) {
      // 小於30天，顯示天
      final days = totalAdjustment / 1440;
      return ' ($sign${days.toStringAsFixed(1)}天)';
    } else if (totalAdjustment.abs() < 525600) {
      // 小於365天，顯示月
      final months = totalAdjustment / 43200;
      return ' ($sign${months.toStringAsFixed(1)}個月)';
    } else {
      // 大於等於365天，顯示年
      final years = totalAdjustment / 525600;
      return ' ($sign${years.toStringAsFixed(1)}年)';
    }
  }

  @override
  Widget build(BuildContext context) {
    List<Widget> adjustmentWidgets = [];

    // 根據星盤類型決定顯示哪些調整區域
    if (widget.viewModel.chartType.isPredictiveChart ||
        widget.viewModel.chartType.isRelationshipPredictiveChart ||
        widget.viewModel.chartType.isReturnChart) {
      // 推運盤：顯示推運時間調整
      adjustmentWidgets.add(_buildTimeAdjustmentRow(
        '推運時間',
        _adjustedTransitDate,
        _totalTransitAdjustment,
        _adjustTransitTime,
        () => _showDateTimePicker('transit'),
      ));
    }

    if (widget.viewModel.chartType.requiresTwoPersons) {
      // 合盤：顯示兩個人的時間調整
      adjustmentWidgets.add(_buildTimeAdjustmentRow(
        '主要人物',
        _adjustedPrimaryDate,
        _totalPrimaryAdjustment,
        _adjustPrimaryTime,
        () => _showDateTimePicker('primary'),
      ));

      if (widget.viewModel.secondaryPerson != null) {
        adjustmentWidgets.add(_buildTimeAdjustmentRow(
          '次要人物',
          _adjustedSecondaryDate,
          _totalSecondaryAdjustment,
          _adjustSecondaryTime,
          () => _showDateTimePicker('secondary'),
        ));
      }
    } else {
      // 一般星盤：只顯示主要人物時間調整
      adjustmentWidgets.add(_buildTimeAdjustmentRow(
        '出生時間',
        _adjustedPrimaryDate,
        _totalPrimaryAdjustment,
        _adjustPrimaryTime,
        () => _showDateTimePicker('primary'),
      ));
    }

    return Column(
      children: adjustmentWidgets,
    );
  }

  // 構建時間調整行
  Widget _buildTimeAdjustmentRow(
    String label,
    DateTime adjustedDate,
    int totalAdjustment,
    Function(bool) adjustFunction,
    VoidCallback showPickerFunction,
  ) {
    final dateText =
        '${_formatDateTime(adjustedDate)}${_formatAdjustment(totalAdjustment)}';

    return Card(
      color: Colors.white,
      margin: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
      elevation: 2,
      shadowColor: AppColors.royalIndigo.withOpacity(0.2),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(6)),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 3),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 標籤
            SizedBox(
              width: 60,
              child: Text(
                label,
                style: const TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textDark,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            const SizedBox(width: 4),

            // 減少按鈕 (左側)
            SizedBox(
              width: 28,
              height: 28,
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red.shade50,
                  foregroundColor: Colors.red.shade700,
                  elevation: 0,
                  padding: EdgeInsets.zero,
                  minimumSize: Size.zero,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(4),
                    side: BorderSide(color: Colors.red.shade200),
                  ),
                ),
                onPressed: () => adjustFunction(false),
                child: const Icon(Icons.remove, size: 14),
              ),
            ),
            const SizedBox(width: 4),

            // 時間顯示 - 可點擊選擇
            Expanded(
              child: GestureDetector(
                onTap: showPickerFunction,
                child: Container(
                  height: 28,
                  padding:
                      const EdgeInsets.symmetric(vertical: 2, horizontal: 4),
                  decoration: BoxDecoration(
                    color: AppColors.royalIndigo.withOpacity(0.05),
                    borderRadius: BorderRadius.circular(4),
                    border: Border.all(
                      color: AppColors.royalIndigo.withOpacity(0.1),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Flexible(
                        child: Text(
                          dateText,
                          style: const TextStyle(
                            fontSize: 11,
                            fontWeight: FontWeight.w500,
                          ),
                          textAlign: TextAlign.center,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            const SizedBox(width: 4),

            // 調整單位選擇
            SizedBox(
              height: 28,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 2, vertical: 0),
                decoration: BoxDecoration(
                  color: Colors.grey.shade50,
                  borderRadius: BorderRadius.circular(4),
                  border: Border.all(color: Colors.grey.shade200),
                ),
                child: DropdownButtonHideUnderline(
                  child: DropdownButton<String>(
                    value: _selectedUnit,
                    isDense: true,
                    icon: const Icon(Icons.arrow_drop_down,
                        color: AppColors.royalIndigo, size: 14),
                    style: const TextStyle(
                      color: AppColors.textDark,
                      fontSize: 10,
                      fontWeight: FontWeight.w500,
                    ),
                    onChanged: (String? newValue) {
                      if (newValue != null) {
                        setState(() {
                          _selectedUnit = newValue;
                        });
                      }
                    },
                    items: _adjustmentUnits
                        .map<DropdownMenuItem<String>>((String value) {
                      return DropdownMenuItem<String>(
                        value: value,
                        child:
                            Text(value, style: const TextStyle(fontSize: 10)),
                      );
                    }).toList(),
                  ),
                ),
              ),
            ),
            const SizedBox(width: 4),

            // 增加按鈕 (右側)
            SizedBox(
              width: 28,
              height: 28,
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green.shade50,
                  foregroundColor: Colors.green.shade700,
                  elevation: 0,
                  padding: EdgeInsets.zero,
                  minimumSize: Size.zero,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(4),
                    side: BorderSide(color: Colors.green.shade200),
                  ),
                ),
                onPressed: () => adjustFunction(true),
                child: const Icon(Icons.add, size: 14),
              ),
            ),
            const SizedBox(width: 4),

            // 重置按鈕
            SizedBox(
              width: 28,
              height: 28,
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.royalIndigo.withOpacity(0.1),
                  foregroundColor: AppColors.royalIndigo,
                  elevation: 0,
                  padding: EdgeInsets.zero,
                  minimumSize: Size.zero,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(4),
                    side: BorderSide(
                        color: AppColors.royalIndigo.withOpacity(0.2)),
                  ),
                ),
                onPressed: _resetToDefaultTime,
                child: const Icon(Icons.restore, size: 14),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 顯示日期時間選擇器
  void _showDateTimePicker([String type = 'primary']) async {
    // 創建一個臨時日期變量，用於在用戶確認前存儲選擇
    DateTime tempDate;
    String title;

    switch (type) {
      case 'primary':
        tempDate = _adjustedPrimaryDate;
        title = '選擇出生時間';
        break;
      case 'secondary':
        tempDate = _adjustedSecondaryDate;
        title = '選擇次要人物時間';
        break;
      case 'transit':
        tempDate = _adjustedTransitDate;
        title = '選擇推運時間';
        break;
      default:
        tempDate = _adjustedPrimaryDate;
        title = '選擇時間';
    }

    // 使用共用的日期時間選擇器
    final selectedDateTime = await DateTimePickerBottomSheet.show(
        context: context,
        initialDateTime: tempDate,
        title: title,
        minDate: DateTime(1800, 1, 1),
        maxDate: DateTime(2100, 12, 31),
        defaultDateTime: tempDate);

    if (selectedDateTime != null) {
      setState(() {
        switch (type) {
          case 'primary':
            _adjustedPrimaryDate = selectedDateTime;
            _totalPrimaryAdjustment = _calculateTotalAdjustment(
                _originalPrimaryDate, _adjustedPrimaryDate);
            break;
          case 'secondary':
            _adjustedSecondaryDate = selectedDateTime;
            _totalSecondaryAdjustment = _calculateTotalAdjustment(
                _originalSecondaryDate, _adjustedSecondaryDate);
            break;
          case 'transit':
            _adjustedTransitDate = selectedDateTime;
            _totalTransitAdjustment = _calculateTotalAdjustment(
                _originalTransitDate, _adjustedTransitDate);
            break;
        }
      });
      _updateChart();
    }
  }

  // 計算總調整量（分鐘）
  int _calculateTotalAdjustment(DateTime original, DateTime adjusted) {
    return adjusted.difference(original).inMinutes;
  }

  // 格式化日期時間
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}/${dateTime.month.toString().padLeft(2, '0')}/${dateTime.day.toString().padLeft(2, '0')} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
