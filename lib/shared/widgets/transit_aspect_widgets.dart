import 'package:astreal/data/models/astrology/aspect_info.dart';
import 'package:astreal/presentation/themes/app_theme.dart';
import 'package:astreal/presentation/viewmodels/chart_viewmodel.dart';
import 'package:astreal/presentation/viewmodels/home_viewmodel.dart';
import 'package:astreal/shared/utils/astrology_calculator.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../presentation/pages/chart_page.dart';

/// 行運盤影響區域的容器元件
class TransitAspectsSection extends StatelessWidget {
  const TransitAspectsSection({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final viewModel = Provider.of<HomeViewModel>(context);

    if (viewModel.isLoadingTransitInfo) {
      return Center(
        child: Container(
          padding: const EdgeInsets.symmetric(
            horizontal: 8,
            vertical: 2,
          ),
          child: const Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(
                height: 24,
                width: 24,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor:
                      AlwaysStoppedAnimation<Color>(AppColors.royalIndigo),
                ),
              ),
              SizedBox(height: 8),
              Text(
                '載入行運盤資訊...',
                style: TextStyle(
                  fontSize: 12,
                  color: AppColors.textMedium,
                ),
              ),
            ],
          ),
        ),
      );
    }

    if (viewModel.transitAspects == null || viewModel.transitAspects!.isEmpty) {
      return const SizedBox.shrink();
    }

    return Card(
      elevation: 2,
      margin: const EdgeInsets.symmetric(vertical: 0, horizontal: 0),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          // gradient: const LinearGradient(
          //   begin: Alignment.topLeft,
          //   end: Alignment.bottomRight,
          //   colors: [
          //     Colors.white,
          //     AppColors.royalIndigo,
          //   ],
          // ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 標題和折疊/展開按鈕
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Row(
                  children: [
                    Icon(
                      Icons.auto_graph,
                      size: 18,
                      color: AppColors.royalIndigo,
                    ),
                    SizedBox(width: 8),
                    Text(
                      '行運盤影響',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: AppColors.royalIndigo,
                      ),
                    ),
                  ],
                ),
                // 折疊/展開按鈕
                Material(
                  color: Colors.transparent,
                  borderRadius: BorderRadius.circular(20),
                  child: InkWell(
                    borderRadius: BorderRadius.circular(20),
                    onTap: () {
                      viewModel.toggleTransitInfoExpanded();
                    },
                    child: Padding(
                      padding: const EdgeInsets.all(4.0),
                      child: Icon(
                        viewModel.isTransitInfoExpanded
                            ? Icons.keyboard_arrow_up
                            : Icons.keyboard_arrow_down,
                        size: 20,
                        color: AppColors.royalIndigo,
                      ),
                    ),
                  ),
                ),
              ],
            ),
            // 使用 AnimatedCrossFade 添加展開/折疊動畫效果
            AnimatedCrossFade(
              firstChild: const SizedBox(height: 0),
              secondChild: Column(
                children: [
                  const SizedBox(height: 12),
                  _buildAspectsList(context, viewModel),
                  const SizedBox(height: 12),
                  // 查看完整行運盤按鈕
                  Center(
                    child: ElevatedButton.icon(
                      icon: const Icon(Icons.visibility, size: 16),
                      label: const Text('查看完整行運盤'),
                      onPressed: () =>
                          _navigateToTransitChart(context, viewModel),
                      style: ElevatedButton.styleFrom(
                        foregroundColor: Colors.white,
                        backgroundColor: AppColors.royalIndigo,
                        elevation: 0,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 8),
                        textStyle: const TextStyle(fontSize: 13),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              crossFadeState: viewModel.isTransitInfoExpanded
                  ? CrossFadeState.showSecond
                  : CrossFadeState.showFirst,
              duration: const Duration(milliseconds: 300),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAspectsList(BuildContext context, HomeViewModel viewModel) {
    final aspectsWithImportance = viewModel.transitAspects!
        .map((aspect) => {
              'aspect': aspect,
              'importance': viewModel.getAspectImportance(
                  aspect.aspectType, aspect.planet1.name, aspect.planet2.name)
            })
        .toList();

    aspectsWithImportance.sort(
        (a, b) => (b['importance'] as int).compareTo(a['importance'] as int));

    return Column(
      children: [
        // 相位卡片列表
        ...aspectsWithImportance.take(5).map((item) {
          final aspect = item['aspect'] as AspectInfo;
          final importance = item['importance'] as int;
          return Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: TransitAspectCard(aspect: aspect, importance: importance),
          );
        }).toList(),

        // 顯示還有更多相位的提示
        if (viewModel.transitAspects!.length > 5)
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
            decoration: BoxDecoration(
              color: AppColors.royalIndigo.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: AppColors.royalIndigo,
                width: 1,
              ),
            ),
            child: const Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.info_outline,
                  size: 14,
                  color: AppColors.royalIndigo,
                ),
                SizedBox(width: 8),
                Text(
                  '還有相位未顯示',
                  style: TextStyle(
                    fontSize: 12,
                    fontStyle: FontStyle.normal,
                    color: AppColors.royalIndigo,
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  void _navigateToTransitChart(BuildContext context, HomeViewModel viewModel) {
    try {
      // 創建行運盤數據
      final transitChartData = viewModel.createTransitChartData();

      // 導航到星盤頁面
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => ChangeNotifierProvider(
            create: (_) => ChartViewModel.withChartData(
              initialChartData: transitChartData,
            ),
            child: ChartPage(chartData: transitChartData),
          ),
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('無法載入行運盤: $e')),
      );
    }
  }
}

/// 單個相位卡片元件
class TransitAspectCard extends StatelessWidget {
  final AspectInfo aspect;
  final int importance;

  const TransitAspectCard({
    Key? key,
    required this.aspect,
    required this.importance,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final viewModel = Provider.of<HomeViewModel>(context);
    final aspectColor = AstrologyCalculator.getAspectColor(aspect.aspectType);
    final meaning = viewModel.getAspectForecast(aspect);
    final directionText = aspect.direction != null
        ? ' (${aspect.getDirectionText()} ${aspect.orb.toStringAsFixed(2)}°)'
        : '';

    // 生成相位的唯一ID
    final aspectId = viewModel.getAspectId(aspect);
    final isExpanded = viewModel.isAspectExpanded(aspectId);

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: aspectColor.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(
          color: aspectColor.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      clipBehavior: Clip.antiAlias, // 確保內容不會超出圓角
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 標題行（始終顯示）
          Container(
            decoration: BoxDecoration(
              color: aspectColor.withValues(alpha: 0.08),
            ),
            child: TransitAspectHeader(
              aspect: aspect,
              aspectColor: aspectColor,
              directionText: directionText,
              importance: importance,
              aspectId: aspectId,
              isExpanded: isExpanded,
            ),
          ),
          // 相位內容（僅在展開時顯示）
          AnimatedSize(
            duration: const Duration(milliseconds: 200),
            curve: Curves.easeInOut,
            child: Container(
              height: isExpanded && meaning.isNotEmpty ? null : 0,
              padding: isExpanded && meaning.isNotEmpty
                  ? const EdgeInsets.fromLTRB(12, 8, 12, 12)
                  : EdgeInsets.zero,
              child: meaning.isNotEmpty
                  ? Text(
                      meaning,
                      style: const TextStyle(
                        fontSize: 13,
                        height: 1.4,
                        fontStyle: FontStyle.normal,
                        color: AppColors.textMedium,
                      ),
                    )
                  : const SizedBox.shrink(),
            ),
          ),
        ],
      ),
    );
  }
}

/// 相位卡片的標題行元件
class TransitAspectHeader extends StatelessWidget {
  final AspectInfo aspect;
  final Color aspectColor;
  final String directionText;
  final int importance;
  final String aspectId;
  final bool isExpanded;

  const TransitAspectHeader({
    Key? key,
    required this.aspect,
    required this.aspectColor,
    required this.directionText,
    required this.importance,
    required this.aspectId,
    required this.isExpanded,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final viewModel = Provider.of<HomeViewModel>(context);

    return InkWell(
      onTap: () {
        viewModel.toggleAspectExpanded(aspectId);
      },
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
        child: Row(
          children: [
            // 相位圖標
            Container(
              padding: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: aspectColor.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.compare_arrows,
                size: 14,
                color: aspectColor,
              ),
            ),
            const SizedBox(width: 10),
            // 相位文字
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '${aspect.planet1.name} ${aspect.shortZh} ${aspect.planet2.name}',
                    style: const TextStyle(
                      fontSize: 13,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textDark,
                    ),
                  ),
                  if (directionText.isNotEmpty) ...[
                    const SizedBox(height: 2),
                    Text(
                      directionText,
                      style: TextStyle(
                        fontSize: 11,
                        color: AppColors.textMedium.withValues(alpha: 0.8),
                      ),
                    ),
                  ],
                ],
              ),
            ),
            // 重要性標籤
            if (importance >= 3) const ImportanceBadge(),
            const SizedBox(width: 4),
            // 折疊/展開按鈕
            Icon(
              isExpanded ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
              size: 18,
              color: aspectColor.withValues(alpha: 0.8),
            ),
          ],
        ),
      ),
    );
  }
}

/// 顯示相位重要性的標籤元件
class ImportanceBadge extends StatelessWidget {
  const ImportanceBadge({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: AppColors.warning.withValues(alpha: 0.15),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.warning.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: const Text(
        '重要',
        style: TextStyle(
          fontSize: 10,
          fontWeight: FontWeight.bold,
          color: AppColors.warning,
        ),
      ),
    );
  }
}
