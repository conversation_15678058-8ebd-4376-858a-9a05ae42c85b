import 'dart:math';

import 'package:flutter/material.dart';
import 'package:sweph/sweph.dart';

import '../../../astreal.dart';
import '../../../data/services/api/astrology_service.dart';
import '../../utils/chart_text_styles.dart';
import 'base_chart_painter.dart';

PlanetPosition? _lastHitPlanet;

class ChartPainter extends BaseChartPainter {
  final ChartData chartData;
  final List<PlanetPosition> planets;
  final List<AspectInfo> aspects;
  final HouseCuspData housesData;
  final ChartType chartType; // 添加星盤類型參數
  final ChartSettings? chartSettings; // 添加星盤設定參數
  final MultiChartSettings? multiChartSettings; // 添加多星盤設定參數
  double deltaAngle = 0;

  // 定義各個圓環的半徑比例常數

  static double OUTER_CIRCLE_ZODIAC_TERM_RULERS_RADIUS_RATIO = 1.0; // 最外圈
  static double OUTER_CIRCLE_RADIUS_RATIO = 1.0; // 最外圈
  static const double TERM_RULER_RADIUS_RATIO = 0.9; // 界主星圈
  double ZODIAC_CIRCLE_RADIUS_RATIO = 0.9; // 星座符號圈（動態調整）
  static double INNER_CIRCLE_RADIUS_RATIO = 0.8; // 內圈
  static double HOUSE_DEGREE_RADIUS_RATIO = 0.85; // 宮位度數符號圈，位於內圈

  // 行星相關圓環（從外到內）- 優化間距避免遮擋
  static double PLANET_SYMBOL_RADIUS_RATIO = 0.8; // 行星符號圈（最外）
  static double PLANET_DEGREE_RADIUS_RATIO = 0.73; // 行星度數圈
  static double PLANET_ZODIAC_RADIUS_RATIO = 0.66; // 行星星座圈
  static double PLANET_MINUTE_RADIUS_RATIO = 0.59; // 行星度數分圈
  static double PLANET_RETROGRADE_RADIUS_RATIO = 0.52; // 逆行符號圈（最內）
  static double PLANET_DOT_RADIUS_RATIO = 0.30; // 行星點圈

  static const double HOUSE_CIRCLE_RADIUS_RATIO = 0.40; // 宮位圈
  static const double HOUSE_NUMBER_RADIUS_RATIO = 0.35; // 宮位數字圈
  static double ASPECT_LINE_RADIUS_RATIO = 0.30; // 相位線圈

  ChartPainter(
    this.chartData,
    this.planets,
    this.aspects, {
    required this.housesData,
    required this.chartType, // 添加星盤類型參數
    this.chartSettings, // 添加星盤設定參數
    this.multiChartSettings, // 添加多星盤設定參數
  }) : super(
          aspects: aspects,
          housesData: housesData,
          chartType: chartType,
        );

  // 獲取實際使用的上升點度數
  double get _effectiveAscendantDegree => housesData.ascmc[0];

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    // 確保最外圈填滿螢幕寬度，只留出最小邊距避免裁切
    final radius = (size.width < size.height ? size.width : size.height) / 2;
    deltaAngle = 180 - housesData.cusps[1];

    final paint = Paint()
      ..color = Colors.black
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;

    // 配置各圓環的半徑比例並處理星座界主星繪製
    _configureRadiusRatios(canvas, center, radius, paint);

    // 繪製基本圓圈
    _drawCircles(canvas, center, radius);

    // 繪製宮位度數（如果設定啟用）
    // 如果出生時間不確定，則不顯示阿拉伯點
    if (!chartData.primaryPerson.isTimeUncertain) {
      // 繪製宮位分隔線
      _drawHouseLines(canvas, center, radius);

      // 繪製宮位數字
      _drawHouseNumbers(canvas, center, radius);
    }

    bool showHouseDegrees = false;
    if (multiChartSettings != null) {
      final currentSettings =
      multiChartSettings!.getSettingsForChartType(chartType);
      showHouseDegrees = currentSettings.showHouseDegrees;
    } else if (chartSettings != null) {
      showHouseDegrees = chartSettings!.showHouseDegrees;
    }

    // 根據設定決定是否繪製宮位度數
    if (showHouseDegrees) {
      // 繪製宮位度數
      _drawHouseDegrees(canvas, center, radius);

      // 根據設置決定是否繪製星座背景色
      if (chartSettings?.showZodiacBackground ?? false) {
        _drawZodiacSymbolsBackground(canvas, center, radius);
      }
    } else {
      // 繪製星座分隔線
      _drawZodiacLines(canvas, center, radius);

      // 根據設置決定是否繪製星座背景色
      if (chartSettings?.showZodiacBackground ?? false) {
        _drawZodiacSymbolsBackground(canvas, center, radius);
      }

      // 繪製星座符號
      _drawZodiacSymbols(canvas, center, radius);
    }

    // 繪製相位線
    _drawAspectLines(canvas, center, radius);

    // 繪製行星符號（最外圈）
    drawPlanetSymbols(canvas, center, radius);
  }

  // 繪製基本圓圈
  @override
  void _drawCircles(Canvas canvas, Offset center, double radius) {
    final paint = Paint()
      ..color = Colors.black
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;

    // 繪製外圈
    canvas.drawCircle(
      center,
      radius * OUTER_CIRCLE_RADIUS_RATIO,
      paint,
    );

    paint.strokeWidth = 1.0;

    // 繪製內圈
    canvas.drawCircle(
      center,
      radius * INNER_CIRCLE_RADIUS_RATIO,
      paint,
    );

    // 繪製宮位圈
    canvas.drawCircle(
      center,
      radius * HOUSE_CIRCLE_RADIUS_RATIO,
      paint,
    );

    // 繪製行星點圈
    canvas.drawCircle(
      center,
      radius * PLANET_DOT_RADIUS_RATIO,
      paint,
    );
  }

  /// 配置各圓環的半徑比例並處理星座界主星繪製
  ///
  /// 根據星盤設定動態調整各個圓環的半徑比例：
  /// - 根據是否顯示宮位度數調整行星相關圓環位置
  /// - 根據是否顯示星座界主星調整整體佈局
  /// - 處理星座界主星的繪製邏輯
  void _configureRadiusRatios(
      Canvas canvas, Offset center, double radius, Paint paint) {
    // 獲取當前設置
    bool showHouseDegrees = false;
    bool showZodiacRulers = false;

    if (multiChartSettings != null) {
      final currentSettings =
          multiChartSettings!.getSettingsForChartType(chartType);
      showHouseDegrees = currentSettings.showHouseDegrees;
      showZodiacRulers = currentSettings.showZodiacRulers;
    } else if (chartSettings != null) {
      showHouseDegrees = chartSettings!.showHouseDegrees;
      showZodiacRulers = chartSettings!.showZodiacRulers;
    }

    // 根據是否顯示宮位度數調整行星相關圓環
    if (showHouseDegrees) {
      HOUSE_DEGREE_RADIUS_RATIO = 0.95; // 宮位度數符號圈
      PLANET_SYMBOL_RADIUS_RATIO = 0.82; // 行星符號圈
      PLANET_DEGREE_RADIUS_RATIO = 0.71; // 行星度數圈
      PLANET_ZODIAC_RADIUS_RATIO = 0.61; // 行星星座圈
      PLANET_MINUTE_RADIUS_RATIO = 0.52; // 行星度數分圈
      PLANET_RETROGRADE_RADIUS_RATIO = 0.44; // 逆行符號圈（最內）
      PLANET_DOT_RADIUS_RATIO = 0.30; // 行星點圈
      ASPECT_LINE_RADIUS_RATIO = 0.30; // 相位線圈
    } else {
      PLANET_SYMBOL_RADIUS_RATIO = 0.60; // 行星符號圈
      PLANET_DOT_RADIUS_RATIO = 0.30; // 行星點圈
      ASPECT_LINE_RADIUS_RATIO = 0.30; // 相位線圈
    }

    // 根據是否顯示星座界主星調整整體佈局
    if (showZodiacRulers) {
      // 啟用星座界主星時的佈局調整
      OUTER_CIRCLE_RADIUS_RATIO = 0.9; // 最外圈
      HOUSE_DEGREE_RADIUS_RATIO = 0.85; // 宮位度數符號圈
      ZODIAC_CIRCLE_RADIUS_RATIO = 0.85; // 星座符號圈
      INNER_CIRCLE_RADIUS_RATIO = 0.8; // 內圈

      // 進一步根據宮位度數顯示調整行星圓環
      if (showHouseDegrees) {
        PLANET_SYMBOL_RADIUS_RATIO = 0.75; // 行星符號圈（最外）
        PLANET_DEGREE_RADIUS_RATIO = 0.66; // 行星度數圈
        PLANET_ZODIAC_RADIUS_RATIO = 0.58; // 行星星座圈
        PLANET_MINUTE_RADIUS_RATIO = 0.50; // 行星度數分圈
        PLANET_RETROGRADE_RADIUS_RATIO = 0.44; // 逆行符號圈（最內）
      } else {
        PLANET_SYMBOL_RADIUS_RATIO = 0.60; // 行星符號圈（最外）
        PLANET_DEGREE_RADIUS_RATIO = 0.53; // 行星度數圈
        PLANET_ZODIAC_RADIUS_RATIO = 0.46; // 行星星座圈
        PLANET_MINUTE_RADIUS_RATIO = 0.39; // 行星度數分圈
      }

      // 繪製星座界主星的外圈
      canvas.drawCircle(
        center,
        radius * OUTER_CIRCLE_ZODIAC_TERM_RULERS_RADIUS_RATIO,
        paint,
      );

      // 繪製星座界主星
      _drawZodiacTermRulers(canvas, center, radius);
    } else {
      // 未啟用星座界主星時的標準佈局
      OUTER_CIRCLE_RADIUS_RATIO = 1.0; // 最外圈

      if (showHouseDegrees) {
        ZODIAC_CIRCLE_RADIUS_RATIO = 0.95; // 星座符號圈
        HOUSE_DEGREE_RADIUS_RATIO = 0.95; // 宮位度數符號圈
        INNER_CIRCLE_RADIUS_RATIO = 0.9; // 內圈
      } else {
        HOUSE_DEGREE_RADIUS_RATIO = 0.90; // 宮位度數符號圈
        ZODIAC_CIRCLE_RADIUS_RATIO = 0.90; // 星座符號圈
        INNER_CIRCLE_RADIUS_RATIO = 0.8; // 內圈
      }
    }
  }

  // 繪製星座分隔線
  void _drawZodiacLines(Canvas canvas, Offset center, double radius) {
    final zodiacLinePaint = Paint()
      ..color = Colors.grey
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;

    for (int i = 0; i < 12; i++) {
      final angle = (i * 30.0);
      final startPoint =
          getPointByAngle(center, radius * INNER_CIRCLE_RADIUS_RATIO, angle);
      final endPoint =
          getPointByAngle(center, radius * OUTER_CIRCLE_RADIUS_RATIO, angle);
      canvas.drawLine(startPoint, endPoint, zodiacLinePaint);
    }
  }

// 如果你的角度是「正上方為 0 度、順時針遞增」，那你要做這樣轉換：
  double convertToDrawArcAngle(double angleInDegree) {
    return (angleInDegree + deltaAngle) * pi / 180; // 減 90 度，轉換為從右邊開始
  }

  // 繪製星座背景色
  void _drawZodiacSymbolsBackground(
      Canvas canvas, Offset center, double radius) {
    final signs = [
      '牡羊座',
      '金牛座',
      '雙子座',
      '巨蟹座',
      '獅子座',
      '處女座',
      '天秤座',
      '天蠍座',
      '射手座',
      '摩羯座',
      '水瓶座',
      '雙魚座'
    ];

    for (int i = 0; i < 12; i++) {
      final startAngleDeg = i * 30.0 + 30;
      final paint = Paint()
        ..color = ZodiacSymbols.getZodiacColor(signs[i]).withOpacity(0.2)
        ..style = PaintingStyle.stroke
        ..strokeWidth = radius * OUTER_CIRCLE_RADIUS_RATIO -
            radius * INNER_CIRCLE_RADIUS_RATIO;

      _drawArc(canvas, center, radius * ZODIAC_CIRCLE_RADIUS_RATIO,
          startAngleDeg, 30, paint);
    }
  }

  void _drawArc(Canvas canvas, Offset center, double radius,
      double startAngleDeg, double sweepAngleDeg, Paint paint) {
    double startAngle = (startAngleDeg + deltaAngle) * pi / 180;
    // 定義圓弧的範圍（矩形）
    Rect arcRect = Rect.fromCircle(center: center, radius: radius);

    // 畫圓弧
    canvas.drawArc(
      arcRect,
      -startAngle, // 起始角度（弧度）
      30 * pi / 180, // 弧度長度（正值為順時針，負值為逆時針）
      false, // useCenter: 是否連到圓心
      paint,
    );
  }

  // 繪製宮位分隔線
  void _drawHouseLines(Canvas canvas, Offset center, double radius) {
    final houseLinePaint = Paint()
      ..color = Colors.black
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.5;

    // 如果有宮位數據，使用實際的宮位位置
    if (housesData.cusps.isNotEmpty) {
      for (int i = 1; i <= 12; i++) {
        double houseAngle = (housesData.cusps[i]);
        final startPoint = getPointByAngle(
            center, radius * ASPECT_LINE_RADIUS_RATIO, houseAngle);
        final endPoint = getPointByAngle(
            center, radius * INNER_CIRCLE_RADIUS_RATIO, houseAngle);
        canvas.drawLine(startPoint, endPoint, houseLinePaint);
      }
    } else {
      // 如果沒有宮位數據，使用等宮系統（每個宮位30度）
      for (int i = 0; i < 12; i++) {
        // 根據上升點位置調整宮位角度
        // 上升點位置是第一宮的起始位置
        final angle = ((i * 30.0) + _effectiveAscendantDegree);
        final startPoint =
            getPointByAngle(center, radius * HOUSE_CIRCLE_RADIUS_RATIO, angle);
        final endPoint =
            getPointByAngle(center, radius * INNER_CIRCLE_RADIUS_RATIO, angle);
        canvas.drawLine(startPoint, endPoint, houseLinePaint);
      }
    }
  }

  // 繪製宮位數字
  void _drawHouseNumbers(Canvas canvas, Offset center, double radius) {
    final chartSize = radius * 2; // 計算星盤大小
    final houseTextStyle = ChartTextStyles.getHouseNumberStyle(chartSize);
    final housePainter = TextPainter(
      textDirection: TextDirection.ltr,
    );

    if (housesData.cusps.isNotEmpty) {
      for (int i = 1; i <= 12; i++) {
        final nextHouse = i < 12 ? i + 1 : 1;
        final houseAngle = housesData.cusps[i];
        var nextHouseAngle = housesData.cusps[nextHouse];

        // 計算宮位中間點的角度
        if (nextHouseAngle < houseAngle) {
          nextHouseAngle += 360.0;
        }
        double midAngle = (houseAngle + nextHouseAngle) / 2;
        if (nextHouse == 1 && houseAngle > nextHouseAngle) {
          midAngle = (houseAngle + nextHouseAngle + 360) / 2;
          if (midAngle >= 360) midAngle -= 360;
        }

        final position = getPointByAngle(
            center, radius * HOUSE_NUMBER_RADIUS_RATIO, midAngle);

        housePainter.text = TextSpan(
          text: '$i',
          style: houseTextStyle,
        );
        housePainter.layout();
        housePainter.paint(
          canvas,
          position.translate(-housePainter.width / 2, -housePainter.height / 2),
        );
      }
    } else {
      for (int i = 0; i < 12; i++) {
        // 根據上升點位置調整宮位數字位置
        final angle = ((i * 30.0) + 15.0 + _effectiveAscendantDegree);
        final position =
            getPointByAngle(center, radius * HOUSE_NUMBER_RADIUS_RATIO, angle);

        housePainter.text = TextSpan(
          text: '${i + 1}',
          style: houseTextStyle,
        );
        housePainter.layout();
        housePainter.paint(
          canvas,
          position.translate(-housePainter.width / 2, -housePainter.height / 2),
        );
      }
    }
  }

  // 繪製星座符號
  void _drawZodiacSymbols(Canvas canvas, Offset center, double radius) {
    final zodiacPainter = TextPainter(
      textDirection: TextDirection.ltr,
    );

    final signs = [
      '牡羊座',
      '金牛座',
      '雙子座',
      '巨蟹座',
      '獅子座',
      '處女座',
      '天秤座',
      '天蠍座',
      '射手座',
      '摩羯座',
      '水瓶座',
      '雙魚座'
    ];

    for (int i = 0; i < 12; i++) {
      final angle = (i * 30.0 + 15.0);
      final position =
          getPointByAngle(center, radius * ZODIAC_CIRCLE_RADIUS_RATIO, angle);

      final chartSize = radius * 2; // 計算星盤大小
      final zodiacTextStyle = ChartTextStyles.getZodiacSymbolStyle(
          chartSize, ZodiacSymbols.getZodiacColor(signs[i]));

      zodiacPainter.text = TextSpan(
        text: ZodiacSymbols.ZODIAC_SYMBOLS[signs[i]] ?? '?',
        style: zodiacTextStyle,
      );
      zodiacPainter.layout();
      zodiacPainter.paint(
        canvas,
        position.translate(-zodiacPainter.width / 2, -zodiacPainter.height / 2),
      );
    }
  }

  // 繪製相位線
  void _drawAspectLines(Canvas canvas, Offset center, double radius) {
    // 獲取相位顯示設定
    bool showMajorAspects = true;
    bool showMinorAspects = false;

    if (multiChartSettings != null) {
      final currentSettings =
          multiChartSettings!.getSettingsForChartType(chartType);
      showMajorAspects = currentSettings.showMajorAspects;
      showMinorAspects = currentSettings.showMinorAspects;
    } else if (chartSettings != null) {
      showMajorAspects = chartSettings!.showMajorAspects;
      showMinorAspects = chartSettings!.showMinorAspects;
    }

    for (final aspect in aspects) {
      if (aspect.aspectType == '合相') {
        continue;
      }

      // 檢查是否應該顯示此相位
      bool shouldShowAspect = false;
      if (_isMajorAspect(aspect.aspectType)) {
        shouldShowAspect = showMajorAspects;
      } else {
        shouldShowAspect = showMinorAspects;
      }

      if (!shouldShowAspect) {
        continue; // 跳過不顯示的相位
      }
      final planet1 = aspect.planet1;
      final planet2 = aspect.planet2;

      final angle1 = (planet1.longitude);
      final angle2 = (planet2.longitude);

      // 使用新的 getPointByAngle 方法
      final position1 =
          getPointByAngle(center, radius * ASPECT_LINE_RADIUS_RATIO, angle1);
      final position2 =
          getPointByAngle(center, radius * ASPECT_LINE_RADIUS_RATIO, angle2);

      final aspectPaint = Paint()
        ..color = ZodiacSymbols.getAspectColor(aspect.symbol)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 1.0;

      // 繪製相位線
      canvas.drawLine(position1, position2, aspectPaint);

      // 在相位線中間繪製相位符號
      final midPoint = Offset(
        (position1.dx + position2.dx) / 2,
        (position1.dy + position2.dy) / 2,
      );

      final chartSize = radius * 2; // 計算星盤大小
      final aspectTextStyle = ChartTextStyles.getAspectSymbolStyle(
          chartSize, ZodiacSymbols.getAspectColor(aspect.symbol));

      final aspectPainter = TextPainter(
        text: TextSpan(
          text: aspect.symbol,
          style: aspectTextStyle,
        ),
        textDirection: TextDirection.ltr,
      );
      aspectPainter.layout();
      aspectPainter.paint(
        canvas,
        midPoint.translate(-aspectPainter.width / 2, -aspectPainter.height / 2),
      );
    }
  }

  /// 判斷是否為主要相位
  bool _isMajorAspect(String aspectName) {
    const majorAspects = {'合相', '六分相', '四分相', '三分相', '對分相'};
    return majorAspects.contains(aspectName);
  }

  List<Offset> existingPositions = [];

  // 存儲行星的位置和大小信息，用於點擊檢測
  final List<Map<String, dynamic>> _planetPositions = [];

  /// 獲取最後點擊的行星
  PlanetPosition? getLastHitPlanet() {
    return _lastHitPlanet;
  }

  /// 檢測點擊位置是否在行星上
  PlanetPosition? hitTestPlanet(Offset tapPosition) {
    // 如果沒有行星位置記錄，返回 null
    if (_planetPositions.isEmpty) {
      return null;
    }

    // 尋找最接近點擊位置的行星
    PlanetPosition? closestPlanet;
    double minDistance = double.infinity;
    const double maxClickDistance = 30.0; // 最大點擊距離，超過此距離不響應點擊

    // 檢查每個行星
    for (final planetInfo in _planetPositions) {
      final position = planetInfo['position'] as Offset;
      final width = planetInfo['width'] as double;
      final height = planetInfo['height'] as double;
      final planet = planetInfo['planet'] as PlanetPosition;

      // 計算點擊位置與行星位置的距離
      final double distance = (tapPosition - position).distance;

      // 計算點擊位置是否在行星符號範圍內
      // 增加點擊區域的大小，使行星更容易被點擊
      const double hitAreaExpansion = 1.0; // 增加點擊區域
      final bool isHit =
          tapPosition.dx >= position.dx - width / 2 - hitAreaExpansion &&
              tapPosition.dx <= position.dx + width / 2 + hitAreaExpansion &&
              tapPosition.dy >= position.dy - height / 2 - hitAreaExpansion &&
              tapPosition.dy <= position.dy + height / 2 + hitAreaExpansion;

      // 只有在合理距離內且在點擊範圍內才響應
      if (isHit && distance <= maxClickDistance && distance < minDistance) {
        minDistance = distance;
        closestPlanet = planet;
      }
    }

    if (closestPlanet != null) {
      _lastHitPlanet = closestPlanet;
      // 只在找到行星時輸出日誌
      logger.d('找到點擊的行星: ${closestPlanet.name}');
      return closestPlanet;
    }

    _lastHitPlanet = null;
    return null;
  }

  /// 實現 CustomPainter 的 hitTest 方法
  @override
  bool? hitTest(Offset position) {
    // 先嘗試直接檢測點擊行星
    if (_planetPositions.isNotEmpty) {
      // 使用我們的自定義方法檢測點擊
      _lastHitPlanet = hitTestPlanet(position);

      // 如果點擊到行星，返回 true
      if (_lastHitPlanet != null) {
        return true;
      } else {
        return false;
      }
    } else {
      return false;
    }
  }

  bool isOverlapping(
      Offset position1, double size1, Offset position2, double size2) {
    return (position1.dx - size1 / 2 < position2.dx + size2 / 2 &&
        position1.dx + size1 / 2 > position2.dx - size2 / 2 &&
        position1.dy - size1 / 2 < position2.dy + size2 / 2 &&
        position1.dy + size1 / 2 > position2.dy - size2 / 2);
  }

  // 識別行星群組，將相近的行星分組
  List<List<PlanetPosition>> _identifyPlanetClusters(
      List<PlanetPosition> planets, double clusterThreshold) {
    final List<List<PlanetPosition>> clusters = [];
    final List<PlanetPosition> sortedPlanets = planets.toList()
      ..sort((a, b) => a.longitude.compareTo(b.longitude));

    if (sortedPlanets.isEmpty) return clusters;

    List<PlanetPosition> currentCluster = [sortedPlanets.first];

    for (int i = 1; i < sortedPlanets.length; i++) {
      final current = sortedPlanets[i];
      final previous = sortedPlanets[i - 1];

      // 計算兩個行星之間的角度差
      double diff = current.longitude - previous.longitude;
      if (diff < 0) diff += 360; // 處理跨越0度的情況

      // 如果角度差小於閾值，將當前行星加入當前群組
      if (diff <= clusterThreshold) {
        currentCluster.add(current);
      } else {
        // 否則，結束當前群組並開始新群組
        clusters.add(List.from(currentCluster));
        currentCluster = [current];
      }
    }

    // 處理最後一個群組
    if (currentCluster.isNotEmpty) {
      clusters.add(currentCluster);
    }

    // 檢查第一個和最後一個群組是否應該合併（處理環形結構）
    if (clusters.length > 1) {
      final firstCluster = clusters.first;
      final lastCluster = clusters.last;

      final firstPlanet = firstCluster.first;
      final lastPlanet = lastCluster.last;

      double diff = firstPlanet.longitude + 360 - lastPlanet.longitude;
      if (diff <= clusterThreshold) {
        // 合併第一個和最後一個群組
        clusters.first = [...lastCluster, ...firstCluster];
        clusters.removeLast();
      }
    }

    return clusters;
  }

  // 計算群組內行星的最佳間距
  Map<String, double> _calculateOptimalPositions(
      List<List<PlanetPosition>> clusters) {
    final Map<String, double> adjustedPositions = {};

    // 處理每個群組
    for (final cluster in clusters) {
      if (cluster.length <= 1) {
        // 單個行星不需要調整
        adjustedPositions[cluster.first.name] = cluster.first.longitude;
        continue;
      }

      // 計算群組的角度範圍
      final firstPlanet = cluster.first;
      final lastPlanet = cluster.last;
      double startAngle = firstPlanet.longitude;
      double endAngle = lastPlanet.longitude;

      // 處理跨越0度的情況
      if (endAngle < startAngle) {
        endAngle += 360;
      }

      // 計算群組的角度範圍
      double rangeAngle = endAngle - startAngle;

      // 計算每個行星之間的最小間距（至少3度）
      double minSpacing = 9.0;
      double requiredSpace = (cluster.length - 1) * minSpacing;

      // 如果群組的角度範圍小於所需空間，擴展範圍
      if (rangeAngle < requiredSpace) {
        // 擴展範圍，使每個行星之間有足夠的空間
        double expansion = requiredSpace - rangeAngle;
        startAngle -= expansion / 2;
        endAngle += expansion / 2;
        rangeAngle = requiredSpace;
      }

      // 計算每個行星之間的間距
      double spacing = rangeAngle / (cluster.length - 1);

      // 分配行星位置
      for (int i = 0; i < cluster.length; i++) {
        double newPosition = startAngle + i * spacing;
        // 確保角度在0-360範圍內
        while (newPosition >= 360) {
          newPosition -= 360;
        }
        while (newPosition < 0) {
          newPosition += 360;
        }

        adjustedPositions[cluster[i].name] = newPosition;
      }
    }

    return adjustedPositions;
  }

  // 繪製行星符號
  @override
  void drawPlanetSymbols(Canvas canvas, Offset center, double radius) {
    final Paint paint = Paint()
      ..style = PaintingStyle.fill
      ..color = Colors.black;

    if (planets.isEmpty) return;

    // 清空行星位置記錄，準備重新記錄
    _planetPositions.clear();

    // 識別行星群組（相距10度以內的行星視為一組）
    final clusters = _identifyPlanetClusters(planets, 15.0);

    // 計算每個行星的最佳位置
    final adjustedPositions = _calculateOptimalPositions(clusters);

    // 按照角度排序行星
    existingPositions.clear();
    final sortedPlanets = planets.toList()
      ..sort((a, b) => a.longitude.compareTo(b.longitude));

    // 注意：planets 列表已經在 PlanetCalculator 中根據 planetVisibility 設定過濾過了
    // 所以這裡不需要再次檢查行星顯示設定
    for (final planet in sortedPlanets) {
      // 使用調整後的角度（如果有）
      var angle = adjustedPositions[planet.name] ?? planet.longitude;

      // 繪製行星度數相關信息（如果設定啟用）
      if (chartSettings?.showPlanetDegrees == true) {
        _drawPlanetDegreeNumbers(canvas, center, radius, planet, angle); // 行星度數
        _drawPlanetZodiacSigns(canvas, center, radius, planet, angle); // 行星星座
        _drawPlanetMinutes(canvas, center, radius, planet, angle); // 行星度數分
        _drawPlanetRetrogrades(
            canvas, center, radius, planet, angle); // 逆行符號（最內圈）
      } else {
        // 繪製行星，半徑為 2 的圓形
        paint.color = planet.color;
        var positionCircle = getPointByAngle(
            center, radius * HOUSE_CIRCLE_RADIUS_RATIO, planet.longitude);
        canvas.drawCircle(positionCircle, 2, paint);
        // 移除詳細的位置記錄日誌以提升性能
        var positionLine = getPointByAngle(
            center, radius * PLANET_SYMBOL_RADIUS_RATIO * 0.92, angle);
        // 獲取當前設置
        bool currentShowHouseDegrees = false;
        if (multiChartSettings != null) {
          final currentSettings =
              multiChartSettings!.getSettingsForChartType(chartType);
          currentShowHouseDegrees = currentSettings.showHouseDegrees;
        } else if (chartSettings != null) {
          currentShowHouseDegrees = chartSettings!.showHouseDegrees;
        }

        if (!currentShowHouseDegrees) {
          // 繪製連接線
          canvas.drawLine(
            positionCircle,
            positionLine,
            Paint()
              ..color = planet.color
              ..strokeWidth = 0.5,
          );
        }
      }

      // 使用調整後的角度計算行星符號位置
      var position =
          getPointByAngle(center, radius * PLANET_SYMBOL_RADIUS_RATIO, angle);

      // 繪製行星符號
      final chartSize = radius * 2; // 計算星盤大小
      final planetTextStyle =
          ChartTextStyles.getPlanetSymbolStyle(chartSize, planet.color);
      final planetPainter = TextPainter(
        text: TextSpan(
          text: planet.symbol,
          style: planetTextStyle,
        ),
        textDirection: TextDirection.ltr,
      );
      planetPainter.layout();

      // 計算行星符號的實際位置
      final symbolPosition = position.translate(
          -planetPainter.width / 2, -planetPainter.height / 2);

      planetPainter.paint(
        canvas,
        symbolPosition,
      );

      // 記錄行星的位置和大小信息，用於點擊檢測
      _planetPositions.add({
        'planet': planet,
        'position': position,
        'width': planetPainter.width * 1.5, // 增加點擊區域
        'height': planetPainter.height * 1.5, // 增加點擊區域
        'angle': angle, // 記錄行星角度
        'longitude': planet.longitude, // 記錄行星經度
      });
    }
  }

  // 根據角度計算點的位置
  @override
  Offset getPointByAngle(Offset pt, double r, double angle) {
    double angleTemp = angle + deltaAngle; // 添加偏移角度
    double x = pt.dx + r * cos(angleTemp * pi / 180); // 轉換為弧度並計算 x 坐標
    double y = pt.dy - r * sin(angleTemp * pi / 180); // 轉換為弧度並計算 y 坐標
    return Offset(x, y);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }

  @override
  double getAspectAngle1(AspectInfo aspect) {
    final planet = planets.firstWhere((p) => p.name == aspect.planet1.name);
    return planet.longitude;
  }

  @override
  double getAspectAngle2(AspectInfo aspect) {
    final planet = planets.firstWhere((p) => p.name == aspect.planet2.name);
    return planet.longitude;
  }

  // 繪製星座界主星
  void _drawZodiacTermRulers(Canvas canvas, Offset center, double radius) {
    // 定義界主星圈的半徑比例（在星座符號圈外側）
    const double TERM_RULER_RADIUS_RATIO = 0.95;

    // 獲取占星服務實例
    final astrologyService = AstrologyService();

    // 獲取星座界主星分佈
    final termsDistribution = astrologyService.getZodiacTermsDistribution();

    final termPainter = TextPainter(
      textDirection: TextDirection.ltr,
    );

    // 遍歷每個星座
    for (int signIndex = 0; signIndex < 12; signIndex++) {
      final String signName = [
        AstrologyConstants.ARIES,
        AstrologyConstants.TAURUS,
        AstrologyConstants.GEMINI,
        AstrologyConstants.CANCER,
        AstrologyConstants.LEO,
        AstrologyConstants.VIRGO,
        AstrologyConstants.LIBRA,
        AstrologyConstants.SCORPIO,
        AstrologyConstants.SAGITTARIUS,
        AstrologyConstants.CAPRICORN,
        AstrologyConstants.AQUARIUS,
        AstrologyConstants.PISCES
      ][signIndex];

      final List<Map<String, dynamic>>? terms = termsDistribution[signName];
      if (terms == null) continue;

      // 遍歷該星座的每個界
      for (final term in terms) {
        final double startDegree = term['startDegree'] as double;
        final double endDegree = term['endDegree'] as double;
        final String planetSymbol = term['planetSymbol'] as String;
        final Color planetColor = term['planetColor'] as Color;

        // 計算界的中點角度（相對於整個黃道）
        final double signStartAngle = signIndex * 30.0;
        final double termMidDegree = (startDegree + endDegree) / 2;
        final double termAngle = signStartAngle + termMidDegree;

        // 計算界主星符號的位置
        final position = getPointByAngle(
            center, radius * TERM_RULER_RADIUS_RATIO, termAngle);

        // 設定界主星符號的樣式
        final chartSize = radius * 2; // 計算星盤大小
        final termTextStyle =
            ChartTextStyles.getTermRulerStyle(chartSize, planetColor);

        // 繪製界主星符號
        termPainter.text = TextSpan(
          text: planetSymbol,
          style: termTextStyle,
        );
        termPainter.layout();
        termPainter.paint(
          canvas,
          position.translate(-termPainter.width / 2, -termPainter.height / 2),
        );

        // 繪製界的分隔線（從界主星圈到星座符號圈）
        final double separatorAngle = signStartAngle + startDegree;
        final startPoint = getPointByAngle(
            center,
            radius * OUTER_CIRCLE_ZODIAC_TERM_RULERS_RADIUS_RATIO,
            separatorAngle);
        final endPoint = getPointByAngle(
            center, radius * OUTER_CIRCLE_RADIUS_RATIO, separatorAngle);

        final separatorPaint = Paint()
          ..color = Colors.black
          ..style = PaintingStyle.stroke
          ..strokeWidth = 0.5;

        canvas.drawLine(startPoint, endPoint, separatorPaint);
      }
    }
  }

  // 繪製宮位度數 - 度在上兩度分在下兩度，宮位線介於中間
  void _drawHouseDegrees(Canvas canvas, Offset center, double radius) {
    final chartSize = radius * 2; // 計算星盤大小
    final degreeTextStyle = ChartTextStyles.getHouseDegreeStyle(chartSize);
    final minuteTextStyle = ChartTextStyles.getHouseMinuteStyle(chartSize);

    final degreePainter = TextPainter(textDirection: TextDirection.ltr);
    final minutePainter = TextPainter(textDirection: TextDirection.ltr);

    if (housesData.cusps.isNotEmpty) {
      for (int i = 1; i <= 12; i++) {
        final houseAngle = housesData.cusps[i];

        // 計算宮位度數在星座中的位置
        final signDegree = houseAngle % 30;
        final degreeOnly = signDegree.floor();
        final minDouble = (signDegree - degreeOnly) * 60;
        final min = minDouble.floor();

        // 準備度數和分數文字
        final degreeText = '$degreeOnly°';
        final minuteText = '${min.toString().padLeft(2, '0')}\'';

        // 計算宮位線的基準位置（介於度數和分數之間）
        final houseLineRadius = radius * HOUSE_DEGREE_RADIUS_RATIO;

        // 度數位置（在宮位線上方）
        final degreePosition =
            getPointByAngle(center, houseLineRadius, houseAngle - 5);

        // 分數位置（在宮位線下方）
        final minutePosition =
            getPointByAngle(center, houseLineRadius, houseAngle + 5);

        final zodiacPainter = TextPainter(
          textDirection: TextDirection.ltr,
        );

        final signs = [
          '牡羊座',
          '金牛座',
          '雙子座',
          '巨蟹座',
          '獅子座',
          '處女座',
          '天秤座',
          '天蠍座',
          '射手座',
          '摩羯座',
          '水瓶座',
          '雙魚座'
        ];
        final chartSize = radius * 2; // 計算星盤大小
        int index = (houseAngle / 30).floor();
        final zodiacTextStyle = ChartTextStyles.getZodiacSymbolStyle(
            chartSize, ZodiacSymbols.getZodiacColor(signs[index]));
        final zodiacPosition =
            getPointByAngle(center, houseLineRadius, houseAngle);
        zodiacPainter.text = TextSpan(
          text: ZodiacSymbols.ZODIAC_SYMBOLS[signs[index]] ?? '?',
          style: zodiacTextStyle,
        );
        zodiacPainter.layout();
        zodiacPainter.paint(
          canvas,
          zodiacPosition.translate(
              -zodiacPainter.width / 2, -zodiacPainter.height / 2),
        );

        // 繪製度數
        degreePainter.text = TextSpan(text: degreeText, style: degreeTextStyle);
        degreePainter.layout();

        degreePainter.paint(
          canvas,
          degreePosition.translate(
              -degreePainter.width / 2, -degreePainter.height / 2),
        );

        // 繪製分數
        minutePainter.text = TextSpan(text: minuteText, style: minuteTextStyle);
        minutePainter.layout();

        minutePainter.paint(
          canvas,
          minutePosition.translate(
              -minutePainter.width / 2, -minutePainter.height / 2),
        );
      }
    }
  }

  // 繪製行星度數（第二圈）
  void _drawPlanetDegreeNumbers(Canvas canvas, Offset center, double radius,
      PlanetPosition planet, double angle) {
    final chartSize = radius * 2; // 計算星盤大小
    final degreeTextStyle = ChartTextStyles.getPlanetDegreeStyle(chartSize);

    final degreePainter = TextPainter(
      textDirection: TextDirection.ltr,
    );

    // 計算行星在星座中的度數（只顯示度數部分）
    final signDegree = planet.longitude % 30;
    final degreeOnly = signDegree.floor();
    final degreeText = '$degreeOnly°';

    degreePainter.text = TextSpan(
      text: degreeText,
      style: degreeTextStyle,
    );
    degreePainter.layout();

    // 使用傳入的角度計算位置
    final planetPosition =
        getPointByAngle(center, radius * PLANET_DEGREE_RADIUS_RATIO, angle);

    // 繪製度數文字
    degreePainter.paint(
      canvas,
      planetPosition.translate(
          -degreePainter.width / 2, -degreePainter.height / 2),
    );
  }

  // 繪製行星星座符號（第三圈）
  void _drawPlanetZodiacSigns(Canvas canvas, Offset center, double radius,
      PlanetPosition planet, double angle) {
    final chartSize = radius * 2; // 計算星盤大小

    final zodiacPainter = TextPainter(
      textDirection: TextDirection.ltr,
    );

    // 獲取行星所在星座
    final zodiacSign = _getZodiacSign(planet.longitude);
    final zodiacSymbol = ZodiacSymbols.getZodiacSymbol(zodiacSign);

    final zodiacTextStyle = ChartTextStyles.getPlanetZodiacStyle(
        chartSize, ZodiacSymbols.getZodiacColor(zodiacSign));

    zodiacPainter.text = TextSpan(
      text: zodiacSymbol,
      style: zodiacTextStyle,
    );
    zodiacPainter.layout();

    // 使用傳入的角度計算位置
    final planetPosition =
        getPointByAngle(center, radius * PLANET_ZODIAC_RADIUS_RATIO, angle);

    // 繪製星座符號
    zodiacPainter.paint(
      canvas,
      planetPosition.translate(
          -zodiacPainter.width / 2, -zodiacPainter.height / 2),
    );
  }

  // 繪製行星度數分（第四圈，最內圈）
  void _drawPlanetMinutes(Canvas canvas, Offset center, double radius,
      PlanetPosition planet, double angle) {
    final chartSize = radius * 2; // 計算星盤大小
    final minuteTextStyle = ChartTextStyles.getPlanetMinuteStyle(chartSize);

    final minutePainter = TextPainter(
      textDirection: TextDirection.ltr,
    );

    // 計算行星的分數
    final signDegree = planet.longitude % 30;
    final minDouble = (signDegree - signDegree.floor()) * 60;
    final min = minDouble.floor();
    final minuteText = '${min.toString().padLeft(2, '0')}\'';

    minutePainter.text = TextSpan(
      text: minuteText,
      style: minuteTextStyle,
    );
    minutePainter.layout();

    // 使用傳入的角度計算位置
    final planetPosition =
        getPointByAngle(center, radius * PLANET_MINUTE_RADIUS_RATIO, angle);

    // 繪製分數文字
    minutePainter.paint(
      canvas,
      planetPosition.translate(
          -minutePainter.width / 2, -minutePainter.height / 2),
    );
  }

  // 根據經度獲取星座名稱
  String _getZodiacSign(double longitude) {
    final int signIndex = (longitude / 30).floor() % 12;
    return AstrologyConstants.ZODIAC_SIGNS[signIndex];
  }

  // 繪製逆行符號（第五圈，最內圈）
  void _drawPlanetRetrogrades(Canvas canvas, Offset center, double radius,
      PlanetPosition planet, double angle) {
    final chartSize = radius * 2; // 計算星盤大小
    final retrogradeTextStyle = ChartTextStyles.getRetrogradeStyle(chartSize);

    final retrogradePainter = TextPainter(
      textDirection: TextDirection.ltr,
    );

    // 只有逆行的行星才顯示逆行符號
    if (planet.longitudeSpeed >= 0) return;

    retrogradePainter.text = TextSpan(
      text: '℞',
      style: retrogradeTextStyle,
    );
    retrogradePainter.layout();

    // 使用傳入的角度計算位置
    final planetPosition =
        getPointByAngle(center, radius * PLANET_RETROGRADE_RADIUS_RATIO, angle);

    // 繪製逆行符號
    retrogradePainter.paint(
      canvas,
      planetPosition.translate(
          -retrogradePainter.width / 2, -retrogradePainter.height / 2),
    );
  }
}
