import 'package:astreal/data/models/astrology/aspect_info.dart';
import 'package:astreal/data/models/astrology/chart_data.dart';
import 'package:astreal/data/models/astrology/planet_position.dart';
import 'package:astreal/presentation/pages/ai_interpretation_result_page.dart';
import 'package:astreal/presentation/themes/app_theme.dart';
import 'package:astreal/presentation/viewmodels/chart_viewmodel.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../core/constants/astrology_constants.dart';
import '../utils/chart_info_generators/chart_info_formatter.dart';

/// 行星詳情卡片，顯示在行星上方
class PlanetDetailCard extends StatelessWidget {
  final PlanetPosition planet;
  final List<AspectInfo> aspects;
  final List<AspectInfo> receptions;
  final ChartViewModel viewModel;
  final VoidCallback onClose;

  const PlanetDetailCard({
    Key? key,
    required this.planet,
    required this.aspects,
    required this.receptions,
    required this.viewModel,
    required this.onClose,
  }) : super(key: key);

  /// 格式化行星資訊和相位為可複製的文本
  String _formatPlanetInfoText() {
    // 基本資訊
    final StringBuffer buffer = StringBuffer();

    buffer.writeln(ChartInfoFormatter.sectionTitle('星盤類型'));
    buffer.writeln('${viewModel.chartType.displayName}\n');

    buffer.writeln('【${planet.name}】基本資訊');
    buffer.writeln(
        '位置: ${planet.sign} ${viewModel.formatDegree(planet.longitude % 30)}');
    buffer
        .writeln('象限: ${planet.getHouseText()} (${planet.getHouseTypeText()})');

    if (planet.wholeSignHouseType != null && planet.wholeSignHouse != null) {
      buffer.writeln(
          '整宮: ${planet.getWholeSignHouseText()} (${planet.getWholeSignHouseTypeText()})');
    }

    // 添加行星是哪些宮位的宮主星
    final housesRuledMap = _getHousesRuledByPlanet(planet);
    final houseRulerAgreements = _checkHouseRulerAgreement(planet);

    // 象限制宮主星信息
    final quadrantHouses = housesRuledMap['quadrant']!;
    if (quadrantHouses.isNotEmpty) {
      buffer.writeln('象限制宮主星:');
      for (final houseNum in quadrantHouses) {
        final agreement = houseRulerAgreements[houseNum];
        final String agreementStatus = agreement?['status'] ?? '中性';
        buffer.writeln('  第$houseNum宮的宮主 (合意狀態: $agreementStatus)');
      }
    }

    // 整宮制宮主星信息
    final wholeSignHouses = housesRuledMap['wholeSigns']!;
    if (wholeSignHouses.isNotEmpty) {
      buffer.writeln('整宮制宮主星:');
      for (final houseNum in wholeSignHouses) {
        final agreement = houseRulerAgreements[-houseNum];
        final String agreementStatus = agreement?['status'] ?? '中性';
        buffer.writeln('  第$houseNum宮的宮主 (合意狀態: $agreementStatus)');
      }
    }

    if (planet.id <= AstrologyConstants.PLUTO) {
      buffer.writeln('尊貴力量: ${planet.getDignityText()}');
      // 運行狀態
      if (planet.longitudeSpeed < 0) {
        buffer.writeln(
            '運行狀態: 逆行 (${planet.longitudeSpeed.toStringAsFixed(4)}°/日)');
      } else {
        buffer.writeln(
            '運行狀態: 順行 (${planet.longitudeSpeed.toStringAsFixed(4)}°/日)');
      }

      // 日夜區分相關資訊
      buffer.writeln('日夜盤: ${planet.getDayNightText()}');
      buffer.writeln('行星屬性: ${planet.getPlanetSectNatureText()}');
      buffer.writeln('星座屬性: ${planet.getSignPolarityText()}');
      buffer.writeln('區分狀態: ${planet.getSectStatusText()}');

      // 行星與太陽距離（如果不是太陽本身）
      if (planet.id != 0 && SolarCondition.free == planet.solarCondition) {
        buffer.writeln('太陽距離: ${planet.getSolarConditionText()}');
      }
    }

    // 相位資訊
    if (planet.aspects.isNotEmpty) {
      buffer.writeln('\n【相位】');
      for (final aspect in planet.aspects) {
        final otherPlanet = aspect.planet1.name == planet.name
            ? aspect.planet2
            : aspect.planet1;
        buffer.writeln(
            '${aspect.shortZh} ${otherPlanet.name} ${aspect.direction != null ? aspect.getDirectionText() : ''} (容許度: ${aspect.orb.toStringAsFixed(2)}°)');
      }
    }

    // 互容接納資訊
    if (receptions.isNotEmpty) {
      buffer.writeln('\n【互容接納】');
      for (final reception in receptions) {
        final otherPlanet = reception.planet1.name == planet.name
            ? reception.planet2
            : reception.planet1;
        buffer.writeln('${otherPlanet.name} (${reception.getReceptionText()})');
      }
    }

    return buffer.toString();
  }

  /// 獲取行星作為宮主星的宮位列表
  Map<String, List<int>> _getHousesRuledByPlanet(PlanetPosition planet) {
    final Map<String, List<int>> result = {
      'quadrant': <int>[],
      'wholeSigns': <int>[],
    };

    // 獲取象限制宮位信息
    if (viewModel.chartData.houses != null) {
      for (int i = 1; i <= 12; i++) {
        final cuspLongitude = viewModel.chartData.houses!.cusps[i];
        final signIndex = (cuspLongitude / 30).floor() % 12;
        final houseRulerName = _getHouseRulerBySignIndex(signIndex);

        if (houseRulerName == planet.name) {
          result['quadrant']!.add(i);
        }
      }
    }

    // 獲取整宮制宮位信息
    if (viewModel.chartData.planets != null) {
      for (int i = 1; i <= 12; i++) {
        final signIndex =
            (i - 1 + (viewModel.chartData.houses!.cusps[1] / 30).floor()) % 12;
        final houseRulerName = _getHouseRulerBySignIndex(signIndex);

        if (houseRulerName == planet.name) {
          result['wholeSigns']!.add(i);
        }
      }
    }

    return result;
  }

  /// 根據星座索引獲取宮主星名稱
  String _getHouseRulerBySignIndex(int signIndex) {
    switch (signIndex) {
      case 0: // 牡羊座
        return '火星';
      case 1: // 金牛座
        return '金星';
      case 2: // 雙子座
        return '水星';
      case 3: // 巨蟹座
        return '月亮';
      case 4: // 獅子座
        return '太陽';
      case 5: // 處女座
        return '水星';
      case 6: // 天秤座
        return '金星';
      case 7: // 天蝎座
        return '火星';
      case 8: // 射手座
        return '木星';
      case 9: // 摩羽座
        return '土星';
      case 10: // 水瓶座
        return '土星';
      case 11: // 雙魚座
        return '木星';
      default:
        return '';
    }
  }

  /// 檢查宮主星與其主管宮位是否合意
  Map<int, Map<String, dynamic>> _checkHouseRulerAgreement(
      PlanetPosition planet) {
    final Map<int, Map<String, dynamic>> result = {};
    final housesRuledMap = _getHousesRuledByPlanet(planet);

    // 象限制宮位
    final quadrantHouses = housesRuledMap['quadrant']!;
    for (final i in quadrantHouses) {
      // 檢查宮主星是否在其主管的宮位中
      final bool isInOwnHouse = planet.house == i;
      // 檢查宮主星是否在其主管宮位的三分相宮位中
      final bool isInTrine =
          (i + 4) % 12 == planet.house || (i + 8) % 12 == planet.house;
      // 檢查宮主星是否在其主管宮位的六分相宮位中
      final bool isInSextile =
          (i + 2) % 12 == planet.house || (i + 10) % 12 == planet.house;
      // 檢查宮主星是否在其主管宮位的對分相宮位中
      final bool isInOpposition = (i + 6) % 12 == planet.house;
      // 檢查宮主星是否在其主管宮位的四分相宮位中
      final bool isInSquare =
          (i + 3) % 12 == planet.house || (i + 9) % 12 == planet.house;
      String status = getStatus(i, planet.house);

      result[i] = {
        'status': status,
        'isInOwnHouse': isInOwnHouse,
        'isInTrine': isInTrine,
        'isInSextile': isInSextile,
        'isInOpposition': isInOpposition,
        'isInSquare': isInSquare,
      };
    }

    // 整宮制宮位
    final wholeSignHouses = housesRuledMap['wholeSigns']!;
    for (final i in wholeSignHouses) {
      // 檢查宮主星是否在其主管的宮位中
      final bool isInOwnHouse = planet.wholeSignHouse == i;
      // 檢查宮主星是否在其主管宮位的三分相宮位中
      final bool isInTrine = (i + 4) % 12 == planet.wholeSignHouse ||
          (i + 8) % 12 == planet.wholeSignHouse;
      // 檢查宮主星是否在其主管宮位的六分相宮位中
      final bool isInSextile = (i + 2) % 12 == planet.wholeSignHouse ||
          (i + 10) % 12 == planet.wholeSignHouse;
      // 檢查宮主星是否在其主管宮位的對分相宮位中
      final bool isInOpposition = (i + 6) % 12 == planet.wholeSignHouse;
      // 檢查宮主星是否在其主管宮位的四分相宮位中
      final bool isInSquare = (i + 3) % 12 == planet.wholeSignHouse ||
          (i + 9) % 12 == planet.wholeSignHouse;

      String status = getStatus(i, planet.wholeSignHouse!);

      result[-i] = {
        'status': status,
        'isInOwnHouse': isInOwnHouse,
        'isInTrine': isInTrine,
        'isInSextile': isInSextile,
        'isInOpposition': isInOpposition,
        'isInSquare': isInSquare,
      };
    }

    return result;
  }

  String getStatus(int indexA, int indexB) {
    final int distance = (indexA - indexB).abs() % 12;
    String status;
    if ([4].contains(distance)) {
      status = '拱';
    } else if ([2].contains(distance)) {
      status = '六合';
    } else if ([3, 9].contains(distance)) {
      status = '刑';
    } else if ([6].contains(distance)) {
      status = '衝';
    } else if ([0].contains(distance)) {
      status = '合';
    } else {
      status = '不合意';
    }
    return status;
  }

  /// 複製行星信息到剪貼板
  void _copyPlanetInfo(BuildContext context) {
    final text = _formatPlanetInfoText();
    Clipboard.setData(ClipboardData(text: text)).then((_) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('行星資訊已複製到剪貼板'),
          duration: Duration(seconds: 2),
        ),
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    // 計算卡片的最大高度，確保它不會超出螢幕
    final screenHeight = MediaQuery.of(context).size.height;
    final maxHeight = screenHeight * 0.7; // 最大高度為螢幕高度的 70%

    return Card(
      elevation: 12,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      clipBehavior: Clip.antiAlias,
      // 確保內容不會超出圓角
      color: Colors.white,
      // 半透明背景
      child: Container(
        width: 280,
        constraints: BoxConstraints(maxHeight: maxHeight),
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.white,
              Color(0xFFF8F8FF),
            ],
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 10,
              spreadRadius: 1,
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 標題欄 (不可滾動的部分)
            Container(
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    planet.color.withOpacity(0.1),
                    Colors.white.withOpacity(0.9),
                  ],
                ),
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(15),
                  bottomRight: Radius.circular(15),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 5,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                children: [
                  // 行星圓形圖標
                  Container(
                    padding: const EdgeInsets.all(2),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: RadialGradient(
                        colors: [
                          planet.color.withOpacity(0.7),
                          planet.color.withOpacity(0.3),
                        ],
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: planet.color.withOpacity(0.3),
                          blurRadius: 8,
                          spreadRadius: 1,
                        ),
                      ],
                    ),
                    child: CircleAvatar(
                      backgroundColor: Colors.white,
                      radius: 18,
                      child: Text(
                        planet.symbol,
                        style: TextStyle(
                          fontFamily: "astro_one_font",
                          color: planet.color,
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),

                  const SizedBox(width: 12),

                  // 行星名稱與位置
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          planet.name,
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '${planet.sign} ${viewModel.formatDegree(planet.longitude % 30)}',
                          style: TextStyle(
                            fontSize: 13,
                            color: Colors.grey[700],
                          ),
                        ),
                      ],
                    ),
                  ),

                  // 複製按鈕（僅在 Debug 模式下顯示）
                  if (kDebugMode) ...[
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.grey[100],
                        shape: BoxShape.circle,
                      ),
                      child: IconButton(
                        icon: const Icon(Icons.content_copy, size: 20),
                        onPressed: () => _copyPlanetInfo(context),
                        tooltip: '複製行星資訊',
                        color: AppColors.royalIndigo,
                      ),
                    ),
                    const SizedBox(width: 8),
                  ],

                  // 關閉按鈕
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      shape: BoxShape.circle,
                    ),
                    child: IconButton(
                      icon: const Icon(Icons.close, size: 20),
                      onPressed: onClose,
                      color: Colors.grey[700],
                    ),
                  ),
                ],
              ),
            ),
            // 使用自定義分隔線替代標準分隔線
            Container(
              height: 4,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Colors.transparent,
                    planet.color.withOpacity(0.3),
                    Colors.transparent,
                  ],
                ),
              ),
            ),

            // 可滾動的內容部分
            Expanded(
              child: SingleChildScrollView(
                physics: const BouncingScrollPhysics(),
                clipBehavior: Clip.antiAlias, // 確保滾動內容不會超出容器
                child: Padding(
                  padding: const EdgeInsets.fromLTRB(12.0, 12.0, 12.0, 16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 基本信息
                      _buildInfoSection(
                        title: '位置',
                        content:
                            '${planet.sign} ${viewModel.formatDegree(planet.longitude % 30)}',
                        icon: Icons.place,
                        color: planet.color,
                      ),

                      // 宮位
                      _buildInfoSection(
                        title: '象限制宮位',
                        content:
                            '${planet.house}宮 (${planet.getHouseTypeText()})',
                        icon: Icons.home,
                        color: planet.color,
                      ),

                      // 整宮制宮位
                      if (planet.wholeSignHouseType != null &&
                          planet.wholeSignHouse != null)
                        _buildInfoSection(
                          title: '整宮制宮位',
                          content:
                              '${planet.wholeSignHouse}宮 (${planet.getWholeSignHouseTypeText()})',
                          icon: Icons.home_outlined,
                          color: planet.color,
                        ),

                      // 尊貴力量
                      if (planet.id <= AstrologyConstants.PLUTO)
                        _buildInfoSection(
                          title: '尊貴力量',
                          content: _getDignityText(planet.dignity),
                          icon: Icons.star,
                          color: _getDignityColor(planet.dignity),
                        ),

                      // 運行狀態
                      _buildInfoSection(
                        title: '運行狀態',
                        content: planet.longitudeSpeed < 0
                            ? '逆行 (${planet.longitudeSpeed.toStringAsFixed(4)}°/日)'
                            : '順行 (${planet.longitudeSpeed.toStringAsFixed(4)}°/日)',
                        icon: Icons.speed,
                        color: planet.longitudeSpeed < 0
                            ? Colors.red
                            : Colors.green,
                      ),

                      // 日夜區分
                      _buildInfoSection(
                        title: '日夜盤',
                        content: planet.getDayNightText(),
                        icon: Icons.brightness_4,
                        color: planet.isDaytime ? Colors.orange : Colors.indigo,
                      ),

                      // 行星屬性
                      _buildInfoSection(
                        title: '行星屬性',
                        content: planet.getPlanetSectNatureText(),
                        icon: Icons.nature,
                        color: planet.color,
                      ),

                      // 星座屬性
                      _buildInfoSection(
                        title: '星座屬性',
                        content: planet.getSignPolarityText(),
                        icon: Icons.wb_sunny_outlined,
                        color: planet.color,
                      ),

                      // 區分狀態
                      _buildInfoSection(
                        title: '區分狀態',
                        content: planet.getSectStatusText(),
                        icon: Icons.compare,
                        color: planet.color,
                      ),

                      // 太陽距離
                      if (planet.solarCondition !=
                          SolarCondition.free) // 如果不是太陽本身
                        _buildInfoSection(
                          title: '太陽距離',
                          content: planet.getSolarConditionText(),
                          icon: Icons.wb_sunny,
                          color: planet.color,
                        ),

                      // 宮主星信息
                      _buildHouseRulerSection(),

                      // 相位信息
                      if (planet.aspects.isNotEmpty) ...[
                        const SizedBox(height: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(
                              vertical: 4, horizontal: 8),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                AppColors.royalIndigo.withOpacity(0.1),
                                Colors.white.withOpacity(0.5),
                              ],
                              begin: Alignment.centerLeft,
                              end: Alignment.centerRight,
                            ),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            children: [
                              const Icon(
                                Icons.timeline,
                                color: AppColors.royalIndigo,
                                size: 14,
                              ),
                              const SizedBox(width: 6),
                              const Text(
                                '相位',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.royalIndigo,
                                ),
                              ),
                              const SizedBox(width: 8),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 6, vertical: 2),
                                decoration: BoxDecoration(
                                  color: AppColors.royalIndigo.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Text(
                                  '${planet.aspects.length}',
                                  style: const TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.bold,
                                    color: AppColors.royalIndigo,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 6),
                        // 直接使用 Column 顯示所有相位，不再使用固定高度的 SizedBox
                        Column(
                          children: planet.aspects
                              .map((aspect) => _buildAspectItem(aspect))
                              .toList(),
                        ),
                      ],

                      // 互容接納關係
                      if (receptions.isNotEmpty) ...[
                        const SizedBox(height: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(
                              vertical: 4, horizontal: 8),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                Colors.purple.withOpacity(0.1),
                                Colors.white.withOpacity(0.5),
                              ],
                              begin: Alignment.centerLeft,
                              end: Alignment.centerRight,
                            ),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            children: [
                              const Icon(
                                Icons.swap_horiz,
                                color: Colors.purple,
                                size: 14,
                              ),
                              const SizedBox(width: 6),
                              const Text(
                                '互容接納',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.purple,
                                ),
                              ),
                              const SizedBox(width: 8),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 6, vertical: 2),
                                decoration: BoxDecoration(
                                  color: Colors.purple.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Text(
                                  '${receptions.length}',
                                  style: const TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.purple,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 6),
                        // 直接使用 Column 顯示所有互容接納關係，不再使用固定高度的 SizedBox
                        Column(
                          children: receptions
                              .map(
                                  (reception) => _buildReceptionItem(reception))
                              .toList(),
                        ),
                      ],

                      // 分析按鈕
                      const SizedBox(height: 16),
                      _buildAnalysisButton(context),
                    ],
                  ),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }

  /// 構建分析行星詳情按鈕
  Widget _buildAnalysisButton(BuildContext context) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(horizontal: 4),
      child: ElevatedButton.icon(
        onPressed: () => _navigateToInterpretation(context),
        icon: const Icon(
          Icons.psychology,
          size: 18,
          color: Colors.white,
        ),
        label: const Text(
          '分析行星詳情',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: planet.color,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 3,
          shadowColor: planet.color.withOpacity(0.3),
        ),
      ),
    );
  }

  /// 導航到 AI 解讀頁面
  void _navigateToInterpretation(BuildContext context) {
    // 創建一個基於原始星盤的 ChartData
    final chartData = ChartData(
      chartType: viewModel.chartData.chartType,
      primaryPerson: viewModel.chartData.primaryPerson,
      secondaryPerson: viewModel.chartData.secondaryPerson,
      specificDate: viewModel.chartData.specificDate,
      planets: viewModel.chartData.planets,
      houses: viewModel.chartData.houses,
      aspects: viewModel.chartData.aspects,
    );

    // 先關閉行星詳情卡片
    onClose();

    // 導航到 AI 解讀結果頁面
    try {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) {
            return AIInterpretationResultPage(
              chartData: chartData,
              interpretationTitle: '行星詳情解讀',
              subtitle: '${planet.name} - ${planet.sign} ${viewModel.formatDegree(planet.longitude % 30)}',
              suggestedQuestions: [
                _buildPlanetInterpretationPrompt(),
              ],
              autoExecuteFirstQuestion: true,
            );
          },
        ),
      );
    } catch (e) {
      // 如果導航失敗，可以在這裡處理錯誤
      debugPrint('導航到解讀頁面失敗: $e');
    }
  }

  /// 構建行星解讀提示詞
  String _buildPlanetInterpretationPrompt() {
    final StringBuffer prompt = StringBuffer();

    prompt.writeln('請詳細分析以下行星的占星學意義：');
    prompt.writeln();
    prompt.writeln('【行星基本信息】');
    prompt.writeln('行星：${planet.name}');
    prompt.writeln('位置：${planet.sign} ${viewModel.formatDegree(planet.longitude % 30)}');
    prompt.writeln('宮位：第${planet.house}宮 (${planet.getHouseTypeText()})');

    if (planet.wholeSignHouse != null) {
      prompt.writeln('整宮制：第${planet.wholeSignHouse}宮 (${planet.getWholeSignHouseTypeText()})');
    }

    if (planet.id <= AstrologyConstants.PLUTO) {
      prompt.writeln('尊貴力量：${_getDignityText(planet.dignity)}');
      prompt.writeln('運行狀態：${planet.longitudeSpeed < 0 ? '逆行' : '順行'}');
      prompt.writeln('日夜盤：${planet.getDayNightText()}');
      prompt.writeln('區分狀態：${planet.getSectStatusText()}');
    }

    // 添加宮主星信息
    final housesRuledMap = _getHousesRuledByPlanet(planet);
    final quadrantHouses = housesRuledMap['quadrant']!;
    final wholeSignHouses = housesRuledMap['wholeSigns']!;

    if (quadrantHouses.isNotEmpty) {
      prompt.writeln('象限制宮主星：第${quadrantHouses.join('、')}宮');
    }

    if (wholeSignHouses.isNotEmpty) {
      prompt.writeln('整宮制宮主星：第${wholeSignHouses.join('、')}宮');
    }

    // 添加相位信息
    if (planet.aspects.isNotEmpty) {
      prompt.writeln();
      prompt.writeln('【主要相位】');
      for (final aspect in planet.aspects.take(5)) { // 只取前5個相位
        final otherPlanet = aspect.planet1.name == planet.name
            ? aspect.planet2
            : aspect.planet1;
        prompt.writeln('${aspect.shortZh} ${otherPlanet.name} (容許度: ${aspect.orb.toStringAsFixed(1)}°)');
      }
    }

    // 添加互容接納信息
    if (receptions.isNotEmpty) {
      prompt.writeln();
      prompt.writeln('【互容接納關係】');
      for (final reception in receptions.take(3)) { // 只取前3個
        final otherPlanet = reception.planet1.name == planet.name
            ? reception.planet2
            : reception.planet1;
        prompt.writeln('與 ${otherPlanet.name} ${reception.getReceptionText()}');
      }
    }

    prompt.writeln();
    prompt.writeln('【分析要求】');
    prompt.writeln('請從以下角度進行詳細分析：');
    prompt.writeln('1. 行星在此星座的基本特質和表現方式');
    prompt.writeln('2. 行星在此宮位的生活領域影響');
    prompt.writeln('3. 尊貴力量對行星能量的影響');
    prompt.writeln('4. 運行狀態（順行/逆行）的意義');
    prompt.writeln('5. 主要相位對行星能量的調節作用');
    prompt.writeln('6. 作為宮主星的責任和影響範圍');
    prompt.writeln('7. 在當前星盤配置下的整體評估');
    prompt.writeln('8. 實際生活中的具體建議和注意事項');

    return prompt.toString();
  }

  // 構建信息部分
  Widget _buildInfoSection({
    required String title,
    required String content,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 4),
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // 圖標容器
          Container(
            padding: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, size: 14, color: color),
          ),
          const SizedBox(width: 8),
          // 標題和內容
          Expanded(
            child: RichText(
              text: TextSpan(
                children: [
                  TextSpan(
                    text: '$title: ',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 13,
                      color: Colors.grey[700],
                    ),
                  ),
                  TextSpan(
                    text: content,
                    style: const TextStyle(
                      fontSize: 13,
                      color: Colors.black87,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 構建相位項目
  Widget _buildAspectItem(AspectInfo aspect) {
    final color = viewModel.getAspectColor(aspect.aspectType);
    final otherPlanet =
        aspect.planet1.name == planet.name ? aspect.planet2 : aspect.planet1;

    return Container(
      margin: const EdgeInsets.only(bottom: 4),
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 3),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.03),
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
        border: Border.all(
          color: color.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // 相位符號
          Container(
            padding: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              color: color.withOpacity(0.15),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              aspect.symbol,
              style: TextStyle(
                fontFamily: "astro_one_font",
                color: color,
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          const SizedBox(width: 8),
          // 行星名稱和相位資訊
          Expanded(
            child: RichText(
              text: TextSpan(
                children: [
                  TextSpan(
                    text: otherPlanet.name,
                    style: TextStyle(
                      fontSize: 13,
                      fontWeight: FontWeight.bold,
                      color: otherPlanet.color,
                    ),
                  ),
                  TextSpan(
                    text:
                        ' ${aspect.shortZh} (容許度: ${aspect.orb.toStringAsFixed(1)}°)',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
          ),
          // 方向指示器
          if (aspect.direction != null)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: aspect.direction == AspectDirection.applying
                    ? Colors.green.withOpacity(0.1)
                    : Colors.orange.withOpacity(0.1),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                aspect.direction == AspectDirection.applying ? '入相' : '出相',
                style: TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                  color: aspect.direction == AspectDirection.applying
                      ? Colors.green
                      : Colors.orange,
                ),
              ),
            ),
        ],
      ),
    );
  }

  // 構建互容接納項目
  Widget _buildReceptionItem(AspectInfo reception) {
    final otherPlanet = reception.planet1.name == planet.name
        ? reception.planet2
        : reception.planet1;
    final receptionType = reception.getReceptionText();
    // 根據互容接納類型設置顏色
    final typeColor = reception.receptionType == ReceptionType.reception
        ? Colors.purple
        : Colors.teal;

    return Container(
      margin: const EdgeInsets.only(bottom: 4),
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 3),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.03),
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
        border: Border.all(
          color: typeColor.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // 相位符號
          Container(
            padding: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              color: typeColor.withOpacity(0.15),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              reception.symbol,
              style: TextStyle(
                fontFamily: "astro_one_font",
                color: typeColor,
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          const SizedBox(width: 8),
          // 行星名稱和互容接納資訊
          Expanded(
            child: RichText(
              text: TextSpan(
                children: [
                  TextSpan(
                    text: otherPlanet.name,
                    style: TextStyle(
                      fontSize: 13,
                      fontWeight: FontWeight.bold,
                      color: otherPlanet.color,
                    ),
                  ),
                  TextSpan(
                    text: ' [$receptionType] ${reception.shortZh}',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 根據行星尊貴力量狀態返回對應的文字
  String _getDignityText(PlanetDignity dignity) {
    switch (dignity) {
      case PlanetDignity.domicile:
        return '廟';
      case PlanetDignity.exaltation:
        return '旺';
      case PlanetDignity.triplicity:
        return '三分';
      case PlanetDignity.terms:
        return '界';
      case PlanetDignity.decan:
        return '十';
      case PlanetDignity.detriment:
        return '陷';
      case PlanetDignity.fall:
        return '弱';
      case PlanetDignity.peregrine:
        return '外來的';
    }
  }

  // 根據行星尊貴力量狀態返回對應的顏色
  Color _getDignityColor(PlanetDignity dignity) {
    switch (dignity) {
      case PlanetDignity.domicile:
        return Colors.purple; // 廟狀態用紫色
      case PlanetDignity.exaltation:
        return Colors.green; // 旺狀態用綠色
      case PlanetDignity.triplicity:
        return Colors.blue; // 三分主星用藍色
      case PlanetDignity.terms:
        return Colors.teal; // 界主星用青色
      case PlanetDignity.decan:
        return Colors.cyan; // 十度主星用青藍色
      case PlanetDignity.detriment:
        return Colors.red; // 陷狀態用紅色
      case PlanetDignity.fall:
        return Colors.orange; // 弱狀態用橙色
      case PlanetDignity.peregrine:
        return Colors.grey; // 普通狀態用灰色
    }
  }

  // 構建宮主星信息部分
  Widget _buildHouseRulerSection() {
    final housesRuledMap = _getHousesRuledByPlanet(planet);
    final quadrantHouses = housesRuledMap['quadrant']!;
    final wholeSignHouses = housesRuledMap['wholeSigns']!;

    if (quadrantHouses.isEmpty && wholeSignHouses.isEmpty) {
      return const SizedBox.shrink();
    }

    final houseRulerAgreements = _checkHouseRulerAgreement(planet);

    return Container(
      margin: const EdgeInsets.only(top: 6, bottom: 6),
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(
          color: planet.color.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 標題
          const Row(
            children: [
              Icon(
                Icons.home_work,
                color: AppColors.royalIndigo,
                size: 18,
              ),
              SizedBox(width: 8),
              Text(
                '宮主星信息',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: AppColors.royalIndigo,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),

          // 象限制宮主星信息
          if (quadrantHouses.isNotEmpty) ...[
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.blue.withOpacity(0.1),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                '象限制',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue[700],
                ),
              ),
            ),
            const SizedBox(height: 8),
            for (final houseNum in quadrantHouses) ...[
              _buildHouseRulerItem(
                houseNum: houseNum,
                isWholeSign: false,
                agreement: houseRulerAgreements[houseNum],
              ),
            ],
          ],

          // 整宮制宮主星信息
          if (wholeSignHouses.isNotEmpty) ...[
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.purple.withOpacity(0.1),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                '整宮制',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: Colors.purple[700],
                ),
              ),
            ),
            const SizedBox(height: 8),
            for (final houseNum in wholeSignHouses) ...[
              _buildHouseRulerItem(
                houseNum: houseNum,
                isWholeSign: true,
                agreement: houseRulerAgreements[-houseNum],
              ),
            ],
          ],
        ],
      ),
    );
  }

  // 構建宮主星項目
  Widget _buildHouseRulerItem({
    required int houseNum,
    required bool isWholeSign,
    Map<String, dynamic>? agreement,
  }) {
    final String agreementStatus = agreement?['status'] ?? '中性';
    Color statusColor;
    IconData statusIcon;

    switch (agreementStatus) {
      case '拱':
        statusColor = Colors.green;
        statusIcon = Icons.sentiment_very_satisfied;
        break;
      case '六合':
        statusColor = Colors.blue;
        statusIcon = Icons.sentiment_satisfied;
        break;
      case '刑':
        statusColor = Colors.red;
        statusIcon = Icons.sentiment_dissatisfied;
        break;
      case '衝':
        statusColor = Colors.red;
        statusIcon = Icons.sentiment_dissatisfied;
        break;
      default:
        statusColor = Colors.grey;
        statusIcon = Icons.sentiment_neutral;
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 4),
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 3),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.03),
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
        border: Border.all(
          color: isWholeSign
              ? Colors.purple.withOpacity(0.2)
              : Colors.blue.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // 宮位圓形圖標
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: isWholeSign
                  ? Colors.purple.withOpacity(0.1)
                  : Colors.blue.withOpacity(0.1),
            ),
            child: Center(
              child: Text(
                '$houseNum',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: isWholeSign ? Colors.purple : Colors.blue,
                ),
              ),
            ),
          ),
          const SizedBox(width: 8),
          // 宮位信息
          Expanded(
            child: RichText(
              text: TextSpan(
                children: [
                  TextSpan(
                    text: '第$houseNum宮的宮主星',
                    style: const TextStyle(
                      fontSize: 13,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                  TextSpan(
                    text: ' (${isWholeSign ? '整宮制' : '象限制'})',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
          ),
          // 合意狀態
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              color: statusColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Text(
              agreementStatus,
              style: TextStyle(
                fontSize: 11,
                color: statusColor,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
