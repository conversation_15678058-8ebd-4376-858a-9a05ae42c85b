import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:intl/intl.dart';

/// 回饋類型枚舉
enum FeedbackType {
  bugReport('錯誤回報'),
  featureRequest('功能建議'),
  other('其他問題');

  const FeedbackType(this.displayName);
  final String displayName;

  static FeedbackType fromString(String value) {
    return FeedbackType.values.firstWhere(
      (type) => type.name == value,
      orElse: () => FeedbackType.other,
    );
  }
}

/// 意見回饋模型
class FeedbackModel {
  final String id;
  final String content;
  final String? email;
  final String? userId;
  final String deviceId;
  final DateTime createdAt;
  final FeedbackType type;
  final bool isResolved;
  final List<String> attachmentUrls;
  final String? logFileUrl;
  final Map<String, dynamic>? deviceInfo;
  final String? appVersion;

  FeedbackModel({
    required this.id,
    required this.content,
    this.email,
    this.userId,
    required this.deviceId,
    DateTime? createdAt,
    this.type = FeedbackType.other,
    this.isResolved = false,
    this.attachmentUrls = const [],
    this.logFileUrl,
    this.deviceInfo,
    this.appVersion,
  }) : createdAt = createdAt ?? DateTime.now();

  /// 從 JSON 創建 FeedbackModel
  factory FeedbackModel.fromJson(Map<String, dynamic> json) {
    return FeedbackModel(
      id: json['id'] as String,
      content: json['content'] as String,
      email: json['email'] as String?,
      userId: json['userId'] as String?,
      deviceId: json['deviceId'] as String,
      createdAt: json['createdAt'] is Timestamp
          ? (json['createdAt'] as Timestamp).toDate()
          : DateTime.parse(json['createdAt'] as String),
      type: FeedbackType.fromString(json['type'] as String? ?? 'other'),
      isResolved: json['isResolved'] as bool? ?? false,
      attachmentUrls: List<String>.from(json['attachmentUrls'] ?? []),
      logFileUrl: json['logFileUrl'] as String?,
      deviceInfo: json['deviceInfo'] as Map<String, dynamic>?,
      appVersion: json['appVersion'] as String?,
    );
  }

  /// 轉換為 JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'content': content,
      'email': email,
      'userId': userId,
      'deviceId': deviceId,
      'createdAt': createdAt.toIso8601String(),
      'type': type.name,
      'isResolved': isResolved,
      'attachmentUrls': attachmentUrls,
      'logFileUrl': logFileUrl,
      'deviceInfo': deviceInfo,
      'appVersion': appVersion,
    };
  }

  /// 轉換為 Firestore JSON
  Map<String, dynamic> toFirestoreJson() {
    return {
      'id': id,
      'content': content,
      'email': email,
      'userId': userId,
      'deviceId': deviceId,
      'createdAt': FieldValue.serverTimestamp(),
      'type': type.name,
      'isResolved': isResolved,
      'attachmentUrls': attachmentUrls,
      'logFileUrl': logFileUrl,
      'deviceInfo': deviceInfo,
      'appVersion': appVersion,
    };
  }

  /// 格式化創建時間
  String get formattedCreatedAt {
    return DateFormat('yyyy-MM-dd HH:mm').format(createdAt);
  }

  /// 獲取回饋類型顯示名稱
  String get typeDisplayName => type.displayName;

  /// 是否有附件
  bool get hasAttachments => attachmentUrls.isNotEmpty;

  /// 是否有日誌文件
  bool get hasLogFile => logFileUrl != null && logFileUrl!.isNotEmpty;

  /// 複製並修改
  FeedbackModel copyWith({
    String? id,
    String? content,
    String? email,
    String? userId,
    String? deviceId,
    DateTime? createdAt,
    FeedbackType? type,
    bool? isResolved,
    List<String>? attachmentUrls,
    String? logFileUrl,
    Map<String, dynamic>? deviceInfo,
    String? appVersion,
  }) {
    return FeedbackModel(
      id: id ?? this.id,
      content: content ?? this.content,
      email: email ?? this.email,
      userId: userId ?? this.userId,
      deviceId: deviceId ?? this.deviceId,
      createdAt: createdAt ?? this.createdAt,
      type: type ?? this.type,
      isResolved: isResolved ?? this.isResolved,
      attachmentUrls: attachmentUrls ?? this.attachmentUrls,
      logFileUrl: logFileUrl ?? this.logFileUrl,
      deviceInfo: deviceInfo ?? this.deviceInfo,
      appVersion: appVersion ?? this.appVersion,
    );
  }
}
