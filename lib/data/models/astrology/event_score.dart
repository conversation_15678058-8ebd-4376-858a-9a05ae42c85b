/// 事件評分模型
/// 
/// 用於計算和存儲占星事件的評分資訊
class EventScore {
  /// 總分數 (0-100)
  final double totalScore;
  
  /// 行星權重分數
  final double planetWeight;
  
  /// 相位強度分數
  final double aspectStrength;
  
  /// 宮位重要性分數
  final double houseImportance;
  
  /// 時間精確度分數
  final double timeAccuracy;
  
  /// 個人化影響分數
  final double personalImpact;
  
  /// 評分詳細說明
  final String explanation;
  
  /// 評分計算時間
  final DateTime calculatedAt;

  const EventScore({
    required this.totalScore,
    required this.planetWeight,
    required this.aspectStrength,
    required this.houseImportance,
    required this.timeAccuracy,
    required this.personalImpact,
    required this.explanation,
    required this.calculatedAt,
  });

  /// 從 Map 創建 EventScore
  factory EventScore.fromMap(Map<String, dynamic> map) {
    return EventScore(
      totalScore: (map['totalScore'] as num).toDouble(),
      planetWeight: (map['planetWeight'] as num).toDouble(),
      aspectStrength: (map['aspectStrength'] as num).toDouble(),
      houseImportance: (map['houseImportance'] as num).toDouble(),
      timeAccuracy: (map['timeAccuracy'] as num).toDouble(),
      personalImpact: (map['personalImpact'] as num).toDouble(),
      explanation: map['explanation'] as String,
      calculatedAt: DateTime.parse(map['calculatedAt'] as String),
    );
  }

  /// 轉換為 Map
  Map<String, dynamic> toMap() {
    return {
      'totalScore': totalScore,
      'planetWeight': planetWeight,
      'aspectStrength': aspectStrength,
      'houseImportance': houseImportance,
      'timeAccuracy': timeAccuracy,
      'personalImpact': personalImpact,
      'explanation': explanation,
      'calculatedAt': calculatedAt.toIso8601String(),
    };
  }

  /// 複製並修改
  EventScore copyWith({
    double? totalScore,
    double? planetWeight,
    double? aspectStrength,
    double? houseImportance,
    double? timeAccuracy,
    double? personalImpact,
    String? explanation,
    DateTime? calculatedAt,
  }) {
    return EventScore(
      totalScore: totalScore ?? this.totalScore,
      planetWeight: planetWeight ?? this.planetWeight,
      aspectStrength: aspectStrength ?? this.aspectStrength,
      houseImportance: houseImportance ?? this.houseImportance,
      timeAccuracy: timeAccuracy ?? this.timeAccuracy,
      personalImpact: personalImpact ?? this.personalImpact,
      explanation: explanation ?? this.explanation,
      calculatedAt: calculatedAt ?? this.calculatedAt,
    );
  }

  @override
  String toString() {
    return 'EventScore(totalScore: $totalScore, explanation: $explanation)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is EventScore &&
        other.totalScore == totalScore &&
        other.planetWeight == planetWeight &&
        other.aspectStrength == aspectStrength &&
        other.houseImportance == houseImportance &&
        other.timeAccuracy == timeAccuracy &&
        other.personalImpact == personalImpact;
  }

  @override
  int get hashCode {
    return Object.hash(
      totalScore,
      planetWeight,
      aspectStrength,
      houseImportance,
      timeAccuracy,
      personalImpact,
    );
  }
}

/// 每日事件評分模型
class DailyEventScore {
  /// 日期
  final DateTime date;
  
  /// 該日總分數
  final double totalScore;
  
  /// 該日所有事件
  final List<EventScore> events;
  
  /// 最高分事件
  final EventScore? highestEvent;
  
  /// 事件數量
  final int eventCount;

  const DailyEventScore({
    required this.date,
    required this.totalScore,
    required this.events,
    this.highestEvent,
    required this.eventCount,
  });

  /// 從事件列表計算每日評分
  factory DailyEventScore.fromEvents(DateTime date, List<EventScore> events) {
    final totalScore = events.fold<double>(0, (sum, event) => sum + event.totalScore);
    final highestEvent = events.isNotEmpty 
        ? events.reduce((a, b) => a.totalScore > b.totalScore ? a : b)
        : null;
    
    return DailyEventScore(
      date: date,
      totalScore: totalScore,
      events: events,
      highestEvent: highestEvent,
      eventCount: events.length,
    );
  }

  /// 從 Map 創建 DailyEventScore
  factory DailyEventScore.fromMap(Map<String, dynamic> map) {
    return DailyEventScore(
      date: DateTime.parse(map['date'] as String),
      totalScore: (map['totalScore'] as num).toDouble(),
      events: (map['events'] as List)
          .map((e) => EventScore.fromMap(e as Map<String, dynamic>))
          .toList(),
      highestEvent: map['highestEvent'] != null
          ? EventScore.fromMap(map['highestEvent'] as Map<String, dynamic>)
          : null,
      eventCount: map['eventCount'] as int,
    );
  }

  /// 轉換為 Map
  Map<String, dynamic> toMap() {
    return {
      'date': date.toIso8601String(),
      'totalScore': totalScore,
      'events': events.map((e) => e.toMap()).toList(),
      'highestEvent': highestEvent?.toMap(),
      'eventCount': eventCount,
    };
  }

  @override
  String toString() {
    return 'DailyEventScore(date: $date, totalScore: $totalScore, eventCount: $eventCount)';
  }
}

/// 事件評分配置
class EventScoreConfig {
  /// 行星權重配置
  final Map<String, double> planetWeights;
  
  /// 相位強度配置
  final Map<String, double> aspectStrengths;
  
  /// 宮位重要性配置
  final Map<int, double> houseImportances;
  
  /// 時間精確度衰減函數參數
  final double timeDecayFactor;
  
  /// 個人化影響權重
  final double personalImpactWeight;
  
  /// 最小有效分數閾值
  final double minimumScoreThreshold;

  const EventScoreConfig({
    required this.planetWeights,
    required this.aspectStrengths,
    required this.houseImportances,
    this.timeDecayFactor = 0.1,
    this.personalImpactWeight = 1.2,
    this.minimumScoreThreshold = 10.0,
  });

  /// 預設配置
  factory EventScoreConfig.defaultConfig() {
    return EventScoreConfig(
      planetWeights: {
        '太陽': 1.0,
        '月亮': 1.0,
        '水星': 0.8,
        '金星': 0.8,
        '火星': 0.8,
        '木星': 0.6,
        '土星': 0.6,
        '天王星': 0.4,
        '海王星': 0.4,
        '冥王星': 0.4,
      },
      aspectStrengths: {
        '合相': 1.0,
        '對沖': 1.0,
        '四分相': 0.8,
        '三分相': 0.8,
        '六分相': 0.6,
        '半四分相': 0.4,
        '半三分相': 0.4,
        '五分相': 0.3,
      },
      houseImportances: {
        1: 1.0,  // 第一宮 - 始宮
        4: 1.0,  // 第四宮 - 始宮
        7: 1.0,  // 第七宮 - 始宮
        10: 1.0, // 第十宮 - 始宮
        2: 0.8,  // 第二宮 - 續宮
        5: 0.8,  // 第五宮 - 續宮
        8: 0.8,  // 第八宮 - 續宮
        11: 0.8, // 第十一宮 - 續宮
        3: 0.6,  // 第三宮 - 果宮
        6: 0.6,  // 第六宮 - 果宮
        9: 0.6,  // 第九宮 - 果宮
        12: 0.6, // 第十二宮 - 果宮
      },
    );
  }

  /// 從 Map 創建配置
  factory EventScoreConfig.fromMap(Map<String, dynamic> map) {
    return EventScoreConfig(
      planetWeights: Map<String, double>.from(map['planetWeights'] as Map),
      aspectStrengths: Map<String, double>.from(map['aspectStrengths'] as Map),
      houseImportances: Map<int, double>.from(
        (map['houseImportances'] as Map).map((k, v) => MapEntry(int.parse(k.toString()), v.toDouble()))
      ),
      timeDecayFactor: (map['timeDecayFactor'] as num?)?.toDouble() ?? 0.1,
      personalImpactWeight: (map['personalImpactWeight'] as num?)?.toDouble() ?? 1.2,
      minimumScoreThreshold: (map['minimumScoreThreshold'] as num?)?.toDouble() ?? 10.0,
    );
  }

  /// 轉換為 Map
  Map<String, dynamic> toMap() {
    return {
      'planetWeights': planetWeights,
      'aspectStrengths': aspectStrengths,
      'houseImportances': houseImportances.map((k, v) => MapEntry(k.toString(), v)),
      'timeDecayFactor': timeDecayFactor,
      'personalImpactWeight': personalImpactWeight,
      'minimumScoreThreshold': minimumScoreThreshold,
    };
  }
}
