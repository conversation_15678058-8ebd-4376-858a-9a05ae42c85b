import 'package:flutter/material.dart';

import 'astro_event.dart';
import 'event_score.dart';

/// 事件類型枚舉
enum EventCategory {
  relationship, // 感情
  career,      // 事業
  health,      // 健康
  finance,     // 財務
  education,   // 學習
  family,      // 家庭
  spiritual,   // 心靈
}

/// 事件類型擴展
extension EventCategoryExtension on EventCategory {
  /// 獲取中文名稱
  String get displayName {
    switch (this) {
      case EventCategory.relationship:
        return '感情';
      case EventCategory.career:
        return '事業';
      case EventCategory.health:
        return '健康';
      case EventCategory.finance:
        return '財務';
      case EventCategory.education:
        return '學習';
      case EventCategory.family:
        return '家庭';
      case EventCategory.spiritual:
        return '心靈';
    }
  }

  /// 獲取顏色
  Color get color {
    switch (this) {
      case EventCategory.relationship:
        return const Color(0xFFE91E63); // 粉紅色
      case EventCategory.career:
        return const Color(0xFF2196F3); // 藍色
      case EventCategory.health:
        return const Color(0xFF4CAF50); // 綠色
      case EventCategory.finance:
        return const Color(0xFFFF9800); // 橙色
      case EventCategory.education:
        return const Color(0xFF9C27B0); // 紫色
      case EventCategory.family:
        return const Color(0xFF795548); // 棕色
      case EventCategory.spiritual:
        return const Color(0xFF607D8B); // 藍灰色
    }
  }

  /// 獲取圖標
  IconData get icon {
    switch (this) {
      case EventCategory.relationship:
        return Icons.favorite;
      case EventCategory.career:
        return Icons.work;
      case EventCategory.health:
        return Icons.health_and_safety;
      case EventCategory.finance:
        return Icons.attach_money;
      case EventCategory.education:
        return Icons.school;
      case EventCategory.family:
        return Icons.family_restroom;
      case EventCategory.spiritual:
        return Icons.self_improvement;
    }
  }
}

/// 分類事件評分模型
class CategorizedEventScore {
  /// 日期
  final DateTime date;
  
  /// 各類別的分數
  final Map<EventCategory, double> categoryScores;
  
  /// 各類別的事件數量
  final Map<EventCategory, int> categoryCounts;
  
  /// 各類別的事件列表
  final Map<EventCategory, List<EventScore>> categoryEvents;
  
  /// 總分數
  final double totalScore;
  
  /// 總事件數量
  final int totalEventCount;

  const CategorizedEventScore({
    required this.date,
    required this.categoryScores,
    required this.categoryCounts,
    required this.categoryEvents,
    required this.totalScore,
    required this.totalEventCount,
  });

  /// 從事件列表創建分類評分
  factory CategorizedEventScore.fromEvents(
    DateTime date, 
    List<EventScore> events,
    List<AstroEvent> astroEvents,
  ) {
    final categoryScores = <EventCategory, double>{};
    final categoryCounts = <EventCategory, int>{};
    final categoryEvents = <EventCategory, List<EventScore>>{};
    
    // 初始化所有類別
    for (final category in EventCategory.values) {
      categoryScores[category] = 0.0;
      categoryCounts[category] = 0;
      categoryEvents[category] = [];
    }
    
    // 分類事件
    for (int i = 0; i < events.length && i < astroEvents.length; i++) {
      final event = events[i];
      final astroEvent = astroEvents[i];
      
      // 根據事件的 additionalData 或其他屬性判斷類別
      final category = _determineEventCategory(astroEvent);
      
      categoryScores[category] = (categoryScores[category] ?? 0.0) + event.totalScore;
      categoryCounts[category] = (categoryCounts[category] ?? 0) + 1;
      categoryEvents[category]?.add(event);
    }
    
    final totalScore = events.fold<double>(0, (sum, event) => sum + event.totalScore);
    
    return CategorizedEventScore(
      date: date,
      categoryScores: categoryScores,
      categoryCounts: categoryCounts,
      categoryEvents: categoryEvents,
      totalScore: totalScore,
      totalEventCount: events.length,
    );
  }

  /// 判斷事件類別
  static EventCategory _determineEventCategory(AstroEvent astroEvent) {
    // 檢查 additionalData 中的事件類型（多個可能的鍵名）
    String? eventType;

    // 嘗試不同的鍵名
    eventType = astroEvent.additionalData?['primary_event_type'] as String?;
    eventType ??= astroEvent.additionalData?['event_category'] as String?;
    eventType ??= astroEvent.additionalData?['category'] as String?;

    // 如果找到事件類型，進行分類
    if (eventType != null) {
      switch (eventType) {
        case 'relationship':
          return EventCategory.relationship;
        case 'career':
          return EventCategory.career;
        case 'health':
          return EventCategory.health;
        case 'finance':
        case 'financial': // 支援兩種拼寫
          return EventCategory.finance;
        case 'education':
        case 'learning': // 支援兩種拼寫
          return EventCategory.education;
        case 'family':
          return EventCategory.family;
        case 'spiritual':
          return EventCategory.spiritual;
        case 'general':
          // 如果是 general，繼續使用預設分類
          break;
        default:
          // 未知的事件類型，記錄並使用預設分類
          print('未知的事件類型: $eventType，使用預設分類');
          break;
      }
    }

    // 根據占星事件類型進行預設分類
    return _getDefaultCategoryByEventType(astroEvent.type);
  }

  /// 根據事件類型獲取預設類別
  static EventCategory _getDefaultCategoryByEventType(AstroEventType eventType) {
    switch (eventType) {
      case AstroEventType.transitAspect:
      case AstroEventType.progressionAspect:
      case AstroEventType.solarArcAspect:
        return EventCategory.spiritual; // 預設為心靈類別
      case AstroEventType.planetSignChange:
      case AstroEventType.planetHouseChange:
        return EventCategory.career; // 預設為事業類別
      case AstroEventType.moonPhase:
        return EventCategory.relationship; // 預設為感情類別
      case AstroEventType.eclipse:
        return EventCategory.spiritual; // 預設為心靈類別
      case AstroEventType.solarReturn:
      case AstroEventType.lunarReturn:
        return EventCategory.family; // 預設為家庭類別
      default:
        return EventCategory.spiritual; // 其他預設為心靈類別
    }
  }

  /// 獲取指定類別的分數
  double getScoreForCategory(EventCategory category) {
    return categoryScores[category] ?? 0.0;
  }

  /// 獲取指定類別的事件數量
  int getCountForCategory(EventCategory category) {
    return categoryCounts[category] ?? 0;
  }

  /// 獲取指定類別的事件列表
  List<EventScore> getEventsForCategory(EventCategory category) {
    return categoryEvents[category] ?? [];
  }

  /// 獲取最高分的類別
  EventCategory? getHighestScoreCategory() {
    if (categoryScores.isEmpty) return null;
    
    EventCategory? highest;
    double maxScore = 0.0;
    
    for (final entry in categoryScores.entries) {
      if (entry.value > maxScore) {
        maxScore = entry.value;
        highest = entry.key;
      }
    }
    
    return highest;
  }

  /// 轉換為 Map
  Map<String, dynamic> toMap() {
    return {
      'date': date.toIso8601String(),
      'categoryScores': categoryScores.map((key, value) => MapEntry(key.name, value)),
      'categoryCounts': categoryCounts.map((key, value) => MapEntry(key.name, value)),
      'categoryEvents': categoryEvents.map((key, value) => 
        MapEntry(key.name, value.map((e) => e.toMap()).toList())),
      'totalScore': totalScore,
      'totalEventCount': totalEventCount,
    };
  }

  /// 從 Map 創建
  factory CategorizedEventScore.fromMap(Map<String, dynamic> map) {
    final categoryScores = <EventCategory, double>{};
    final categoryCounts = <EventCategory, int>{};
    final categoryEvents = <EventCategory, List<EventScore>>{};
    
    // 解析 categoryScores
    final scoresMap = map['categoryScores'] as Map<String, dynamic>;
    for (final entry in scoresMap.entries) {
      final category = EventCategory.values.firstWhere((c) => c.name == entry.key);
      categoryScores[category] = (entry.value as num).toDouble();
    }
    
    // 解析 categoryCounts
    final countsMap = map['categoryCounts'] as Map<String, dynamic>;
    for (final entry in countsMap.entries) {
      final category = EventCategory.values.firstWhere((c) => c.name == entry.key);
      categoryCounts[category] = entry.value as int;
    }
    
    // 解析 categoryEvents
    final eventsMap = map['categoryEvents'] as Map<String, dynamic>;
    for (final entry in eventsMap.entries) {
      final category = EventCategory.values.firstWhere((c) => c.name == entry.key);
      final eventsList = (entry.value as List)
          .map((e) => EventScore.fromMap(e as Map<String, dynamic>))
          .toList();
      categoryEvents[category] = eventsList;
    }
    
    return CategorizedEventScore(
      date: DateTime.parse(map['date'] as String),
      categoryScores: categoryScores,
      categoryCounts: categoryCounts,
      categoryEvents: categoryEvents,
      totalScore: (map['totalScore'] as num).toDouble(),
      totalEventCount: map['totalEventCount'] as int,
    );
  }

  @override
  String toString() {
    return 'CategorizedEventScore(date: $date, totalScore: $totalScore, totalEventCount: $totalEventCount)';
  }
}
