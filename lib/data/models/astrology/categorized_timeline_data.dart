import 'astro_event.dart';
import 'categorized_event_score.dart';

/// 分類事件時間軸資料模型
class CategorizedTimelineData {
  /// 時間軸開始日期
  final DateTime startDate;
  
  /// 時間軸結束日期
  final DateTime endDate;
  
  /// 每日分類事件評分列表
  final List<CategorizedEventScore> dailyScores;
  
  /// 所有事件列表
  final List<AstroEvent> allEvents;
  
  /// 各類別的最高分數
  final Map<EventCategory, double> maxScores;
  
  /// 各類別的最低分數
  final Map<EventCategory, double> minScores;
  
  /// 各類別的平均分數
  final Map<EventCategory, double> averageScores;
  
  /// 各類別的總事件數量
  final Map<EventCategory, int> totalEventCounts;
  
  /// 總分數
  final double totalScore;
  
  /// 總事件數量
  final int totalEventCount;

  const CategorizedTimelineData({
    required this.startDate,
    required this.endDate,
    required this.dailyScores,
    required this.allEvents,
    required this.maxScores,
    required this.minScores,
    required this.averageScores,
    required this.totalEventCounts,
    required this.totalScore,
    required this.totalEventCount,
  });

  /// 從每日評分列表創建分類時間軸資料
  factory CategorizedTimelineData.fromDailyScores(
    DateTime startDate,
    DateTime endDate,
    List<CategorizedEventScore> dailyScores, [
    List<AstroEvent>? allEvents,
  ]) {
    final events = allEvents ?? <AstroEvent>[];
    
    // 初始化各類別統計
    final maxScores = <EventCategory, double>{};
    final minScores = <EventCategory, double>{};
    final totalScores = <EventCategory, double>{};
    final totalEventCounts = <EventCategory, int>{};
    
    for (final category in EventCategory.values) {
      maxScores[category] = 0.0;
      minScores[category] = double.infinity;
      totalScores[category] = 0.0;
      totalEventCounts[category] = 0;
    }
    
    double overallTotalScore = 0.0;
    int overallTotalCount = 0;
    
    // 計算統計數據
    for (final dailyScore in dailyScores) {
      overallTotalScore += dailyScore.totalScore;
      overallTotalCount += dailyScore.totalEventCount;
      
      for (final category in EventCategory.values) {
        final categoryScore = dailyScore.getScoreForCategory(category);
        final categoryCount = dailyScore.getCountForCategory(category);
        
        // 更新最高分
        if (categoryScore > (maxScores[category] ?? 0.0)) {
          maxScores[category] = categoryScore;
        }
        
        // 更新最低分（只考慮大於0的分數）
        if (categoryScore > 0 && categoryScore < (minScores[category] ?? double.infinity)) {
          minScores[category] = categoryScore;
        }
        
        // 累計總分和總數量
        totalScores[category] = (totalScores[category] ?? 0.0) + categoryScore;
        totalEventCounts[category] = (totalEventCounts[category] ?? 0) + categoryCount;
      }
    }
    
    // 計算平均分數
    final averageScores = <EventCategory, double>{};
    for (final category in EventCategory.values) {
      if (dailyScores.isNotEmpty) {
        averageScores[category] = (totalScores[category] ?? 0.0) / dailyScores.length;
      } else {
        averageScores[category] = 0.0;
      }
      
      // 處理無限小值
      if (minScores[category] == double.infinity) {
        minScores[category] = 0.0;
      }
    }
    
    return CategorizedTimelineData(
      startDate: startDate,
      endDate: endDate,
      dailyScores: dailyScores,
      allEvents: events,
      maxScores: maxScores,
      minScores: minScores,
      averageScores: averageScores,
      totalEventCounts: totalEventCounts,
      totalScore: overallTotalScore,
      totalEventCount: overallTotalCount,
    );
  }

  /// 獲取指定日期的分類評分
  CategorizedEventScore? getScoreForDate(DateTime date) {
    try {
      return dailyScores.firstWhere(
        (score) => _isSameDay(score.date, date),
      );
    } catch (e) {
      return null;
    }
  }

  /// 獲取指定日期範圍的事件
  List<AstroEvent> getEventsInRange(DateTime start, DateTime end) {
    return allEvents.where((event) {
      return event.dateTime.isAfter(start.subtract(const Duration(microseconds: 1))) &&
             event.dateTime.isBefore(end);
    }).toList();
  }

  /// 獲取指定日期的事件（精確到天）
  List<AstroEvent> getEventsForDate(DateTime date) {
    return allEvents.where((event) {
      return _isSameDay(event.dateTime, date);
    }).toList();
  }

  /// 獲取指定類別的時間序列資料
  List<double> getTimeSeriesForCategory(EventCategory category) {
    return dailyScores.map((score) => score.getScoreForCategory(category)).toList();
  }

  /// 獲取指定類別的最高分數
  double getMaxScoreForCategory(EventCategory category) {
    return maxScores[category] ?? 0.0;
  }

  /// 獲取指定類別的平均分數
  double getAverageScoreForCategory(EventCategory category) {
    return averageScores[category] ?? 0.0;
  }

  /// 獲取指定類別的總事件數量
  int getTotalEventCountForCategory(EventCategory category) {
    return totalEventCounts[category] ?? 0;
  }

  /// 獲取所有啟用的類別（有事件的類別）
  List<EventCategory> getActiveCategories() {
    return EventCategory.values.where((category) {
      return (totalEventCounts[category] ?? 0) > 0;
    }).toList();
  }

  /// 檢查兩個日期是否為同一天
  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
           date1.month == date2.month &&
           date1.day == date2.day;
  }

  /// 轉換為 Map
  Map<String, dynamic> toMap() {
    return {
      'startDate': startDate.toIso8601String(),
      'endDate': endDate.toIso8601String(),
      'dailyScores': dailyScores.map((score) => score.toMap()).toList(),
      'allEvents': allEvents.map((event) => event.toMap()).toList(),
      'maxScores': maxScores.map((key, value) => MapEntry(key.name, value)),
      'minScores': minScores.map((key, value) => MapEntry(key.name, value)),
      'averageScores': averageScores.map((key, value) => MapEntry(key.name, value)),
      'totalEventCounts': totalEventCounts.map((key, value) => MapEntry(key.name, value)),
      'totalScore': totalScore,
      'totalEventCount': totalEventCount,
    };
  }

  /// 從 Map 創建
  factory CategorizedTimelineData.fromMap(Map<String, dynamic> map) {
    // 解析 dailyScores
    final dailyScores = (map['dailyScores'] as List)
        .map((score) => CategorizedEventScore.fromMap(score as Map<String, dynamic>))
        .toList();
    
    // 解析 allEvents
    final allEvents = (map['allEvents'] as List)
        .map((event) => AstroEvent.fromMap(event as Map<String, dynamic>))
        .toList();
    
    // 解析各類別統計
    final maxScores = <EventCategory, double>{};
    final minScores = <EventCategory, double>{};
    final averageScores = <EventCategory, double>{};
    final totalEventCounts = <EventCategory, int>{};
    
    final maxScoresMap = map['maxScores'] as Map<String, dynamic>;
    final minScoresMap = map['minScores'] as Map<String, dynamic>;
    final averageScoresMap = map['averageScores'] as Map<String, dynamic>;
    final totalEventCountsMap = map['totalEventCounts'] as Map<String, dynamic>;
    
    for (final category in EventCategory.values) {
      maxScores[category] = (maxScoresMap[category.name] as num?)?.toDouble() ?? 0.0;
      minScores[category] = (minScoresMap[category.name] as num?)?.toDouble() ?? 0.0;
      averageScores[category] = (averageScoresMap[category.name] as num?)?.toDouble() ?? 0.0;
      totalEventCounts[category] = totalEventCountsMap[category.name] as int? ?? 0;
    }
    
    return CategorizedTimelineData(
      startDate: DateTime.parse(map['startDate'] as String),
      endDate: DateTime.parse(map['endDate'] as String),
      dailyScores: dailyScores,
      allEvents: allEvents,
      maxScores: maxScores,
      minScores: minScores,
      averageScores: averageScores,
      totalEventCounts: totalEventCounts,
      totalScore: (map['totalScore'] as num).toDouble(),
      totalEventCount: map['totalEventCount'] as int,
    );
  }

  @override
  String toString() {
    return 'CategorizedTimelineData(startDate: $startDate, endDate: $endDate, totalScore: $totalScore, totalEventCount: $totalEventCount)';
  }
}
