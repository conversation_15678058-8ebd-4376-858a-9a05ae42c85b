import '../../../shared/utils/logger_utils.dart';
import 'ai_api_service.dart';

/// API 費用計算服務
///
/// 負責計算不同 AI 提供商的 API 使用費用
class APICostCalculatorService {
  /// OpenAI 模型定價表（每 1M tokens 的費用，單位：美元）
  /// 更新日期：2025-08-11
  static const Map<String, APIPricing> _openaiModelPricing = {
    'gpt-5': APIPricing(
      inputCostPerM: 1.25, // $1.25 / 1M tokens => $0.001250 / 1K tokens
      outputCostPerM: 10.00, // $10.00 / 1M tokens => $0.010000 / 1K tokens
      modelName: 'GPT-5',
      cachedInputCostPerM: 0.125, // $0.125 / 1M => $0.000125 / 1K
    ),
    'gpt-5-mini': APIPricing(
      inputCostPerM: 0.25, // $0.25 / 1M => $0.000250 / 1K
      outputCostPerM: 2.00, // $2.00 / 1M => $0.002000 / 1K
      modelName: 'GPT-5-mini',
      cachedInputCostPerM: 0.025, // $0.025 / 1M => $0.000025 / 1K
    ),
    'gpt-5-nano': APIPricing(
      inputCostPerM: 0.05, // $0.05 / 1M => $0.000050 / 1K
      outputCostPerM: 0.40, // $0.40 / 1M => $0.000400 / 1K
      modelName: 'GPT-5-nano',
      cachedInputCostPerM: 0.005, // $0.005 / 1M => $0.000005 / 1K
    ),
    'gpt-4.5': APIPricing(
      inputCostPerM: 75.00, // $75.00 / 1M => $0.075000 / 1K
      outputCostPerM: 150.00, // $150.00 / 1M => $0.150000 / 1K
      modelName: 'GPT-4.5',
    ),
    'gpt-4.1': APIPricing(
      inputCostPerM: 2.00, // $2.00 / 1M => $0.002000 / 1K
      outputCostPerM: 8.00, // $8.00 / 1M => $0.008000 / 1K
      modelName: 'GPT-4.1',
    ),
    'gpt-4.1-mini': APIPricing(
      inputCostPerM: 0.40, // $0.40 / 1M => $0.000400 / 1K
      outputCostPerM: 1.60, // $1.60 / 1M => $0.001600 / 1K
      modelName: 'GPT-4.1-mini',
    ),
    'gpt-4.1-nano': APIPricing(
      inputCostPerM: 0.10, // $0.10 / 1M => $0.000100 / 1K
      outputCostPerM: 0.40, // $0.40 / 1M => $0.000400 / 1K
      modelName: 'GPT-4.1-nano',
      cachedInputCostPerM: 0.025, // $0.025 / 1M => $0.000025 / 1K
    ),
    'gpt-4o': APIPricing(
      inputCostPerM: 2.50, // $2.50 / 1M => $0.002500 / 1K
      outputCostPerM: 10.00, // $10.00 / 1M => $0.010000 / 1K
      modelName: 'GPT-4o',
      cachedInputCostPerM: 1.25, // $1.25 / 1M => $0.001250 / 1K
    ),
    'gpt-4o-2024-05-13': APIPricing(
      inputCostPerM: 5.00, // $5.00 / 1M => $0.005000 / 1K
      outputCostPerM: 15.00, // $15.00 / 1M => $0.015000 / 1K
      modelName: 'GPT-4o-2024-05-13',
    ),
    'gpt-4o-mini': APIPricing(
      inputCostPerM: 0.15, // $0.15 / 1M => $0.000150 / 1K
      outputCostPerM: 0.60, // $0.60 / 1M => $0.000600 / 1K
      modelName: 'GPT-4o-mini',
      cachedInputCostPerM: 0.075, // $0.075 / 1M => $0.000075 / 1K
    ),
    'o1-pro': APIPricing(
      inputCostPerM: 150.00, // $150.00 / 1M => $0.150000 / 1K
      outputCostPerM: 600.00, // $600.00 / 1M => $0.600000 / 1K
      modelName: 'o1-pro',
    ),
  };

  /// 各 AI 提供商的預設定價模型（每 1000 tokens 的費用，單位：美元）
  /// 更新日期：2025-01-09
  static const Map<AIProvider, APIPricing> _pricingModels = {
    AIProvider.openai: APIPricing(
      inputCostPerM: 1.25, // GPT-4o input: $1.25/1K tokens (預設使用 GPT-4o)
      outputCostPerM: 5.00, // GPT-4o output: $5.00/1K tokens
      modelName: 'GPT-4o',
    ),
    AIProvider.anthropic: APIPricing(
      inputCostPerM: 0.003, // Claude-3.5-Sonnet input: $0.003/1K tokens
      outputCostPerM: 0.015, // Claude-3.5-Sonnet output: $0.015/1K tokens
      modelName: 'Claude-3.5-Sonnet',
    ),
    AIProvider.groq: APIPricing(
      inputCostPerM: 0.0, // Groq 目前免費
      outputCostPerM: 0.0, // Groq 目前免費
      modelName: 'Llama-3.1-70B',
    ),
    AIProvider.gemini: APIPricing(
      inputCostPerM: 0.00125, // Gemini-1.5-Pro input: $0.00125/1K tokens
      outputCostPerM: 0.005, // Gemini-1.5-Pro output: $0.005/1K tokens
      modelName: 'Gemini-1.5-Pro',
    ),
  };

  /// 美元到台幣的匯率（可以從 remote config 或 API 獲取）
  static const double _usdToTwdRate = 30.0; // 預設匯率

  /// 計算單次 API 調用的費用
  ///
  /// [provider] AI 提供商
  /// [promptTokens] 輸入 tokens 數量
  /// [completionTokens] 輸出 tokens 數量
  /// [modelName] 具體的模型名稱（可選，用於精確計費）
  /// [cachedTokens] 快取輸入 tokens 數量（可選）
  /// [currency] 貨幣類型 ('USD' 或 'TWD')
  ///
  /// 返回費用金額
  static double calculateAPICost({
    required AIProvider provider,
    required int promptTokens,
    required int completionTokens,
    String? modelName,
    int cachedTokens = 0,
    String currency = 'TWD',
  }) {
    try {
      // 優先使用具體模型的定價資訊
      final pricing = getPricing(provider, modelName: modelName);
      if (pricing == null) {
        logger.w(
            '未找到 ${provider.displayName}${modelName != null ? ' 模型 $modelName' : ''} 的定價資訊');
        return 0.0;
      }

      // 計算輸入費用（區分快取和非快取）
      double inputCostUSD = 0.0;
      if (cachedTokens > 0 && pricing.supportsCachedInput) {
        // 有快取輸入且支援快取定價
        final nonCachedTokens = promptTokens - cachedTokens;
        inputCostUSD = (nonCachedTokens / 1000000.0) * pricing.inputCostPerM +
            (cachedTokens / 1000000.0) * pricing.cachedInputCostPerM!;
      } else {
        // 沒有快取或不支援快取定價
        inputCostUSD = (promptTokens / 1000000.0) * pricing.inputCostPerM;
      }

      // 計算輸出費用
      final outputCostUSD =
          (completionTokens / 1000000.0) * pricing.outputCostPerM;
      final totalCostUSD = inputCostUSD + outputCostUSD;

      // 根據貨幣類型返回
      if (currency.toUpperCase() == 'USD') {
        return totalCostUSD;
      } else {
        return totalCostUSD * _usdToTwdRate;
      }
    } catch (e) {
      logger.e('計算 API 費用失敗: $e');
      return 0.0;
    }
  }

  /// 計算估算費用（基於文本長度）
  ///
  /// [provider] AI 提供商
  /// [promptText] 輸入文本
  /// [responseText] 回應文本
  /// [modelName] 具體的模型名稱（可選）
  /// [currency] 貨幣類型
  ///
  /// 返回估算費用
  static double calculateEstimatedCost({
    required AIProvider provider,
    required String promptText,
    required String responseText,
    String? modelName,
    String currency = 'TWD',
  }) {
    try {
      // 估算 token 數量
      final promptTokens = estimateTokens(promptText);
      final completionTokens = estimateTokens(responseText);

      return calculateAPICost(
        provider: provider,
        promptTokens: promptTokens,
        completionTokens: completionTokens,
        modelName: modelName,
        currency: currency,
      );
    } catch (e) {
      logger.e('計算估算費用失敗: $e');
      return 0.0;
    }
  }

  /// 格式化費用顯示
  ///
  /// [cost] 費用金額
  /// [currency] 貨幣類型
  /// [showSymbol] 是否顯示貨幣符號
  ///
  /// 返回格式化的費用字串
  static String formatCost(
    double cost, {
    String currency = 'TWD',
    bool showSymbol = true,
  }) {
    try {
      if (cost == 0.0) {
        return currency.toUpperCase() == 'USD' ? 'Free' : '免費';
      }

      String symbol = '';
      if (showSymbol) {
        symbol = currency.toUpperCase() == 'USD' ? '\$' : 'NT\$';
      }

      if (currency.toUpperCase() == 'USD') {
        // 美元顯示到小數點後4位
        if (cost < 0.001) {
          return '$symbol${(cost * 1000).toStringAsFixed(2)}‰'; // 千分之幾
        } else {
          return '$symbol${cost.toStringAsFixed(4)}';
        }
      } else {
        // 台幣顯示到小數點後2位
        if (cost < 0.01) {
          return '$symbol${cost.toStringAsFixed(4)}';
        } else {
          return '$symbol${cost.toStringAsFixed(2)}';
        }
      }
    } catch (e) {
      logger.e('格式化費用失敗: $e');
      return '計算錯誤';
    }
  }

  /// 獲取提供商的定價資訊
  ///
  /// [provider] AI 提供商
  /// [modelName] 具體的模型名稱（可選，用於 OpenAI）
  ///
  /// 返回定價資訊
  static APIPricing? getPricing(AIProvider provider, {String? modelName}) {
    // 如果是 OpenAI 且指定了模型名稱，優先使用具體模型的定價
    if (provider == AIProvider.openai && modelName != null) {
      final openaiPricing = _openaiModelPricing[modelName.toLowerCase()];
      if (openaiPricing != null) {
        return openaiPricing;
      }
    }

    // 使用預設定價
    return _pricingModels[provider];
  }

  /// 獲取 OpenAI 所有可用模型的定價
  ///
  /// 返回模型名稱到定價的映射
  static Map<String, APIPricing> getOpenAIModelPricing() {
    return Map.unmodifiable(_openaiModelPricing);
  }

  /// 計算具體模型的 API 費用（已整合到 calculateAPICost 中）
  ///
  /// @deprecated 請使用 calculateAPICost 方法，並傳入 modelName 參數
  static double calculateModelAPICost({
    required AIProvider provider,
    required String modelName,
    required int promptTokens,
    required int completionTokens,
    int cachedTokens = 0,
    String currency = 'TWD',
  }) {
    return calculateAPICost(
      provider: provider,
      promptTokens: promptTokens,
      completionTokens: completionTokens,
      modelName: modelName,
      cachedTokens: cachedTokens,
      currency: currency,
    );
  }

  /// 計算每日費用統計
  ///
  /// [usageRecords] 使用記錄列表
  /// [currency] 貨幣類型
  ///
  /// 返回每日費用統計
  static Map<String, double> calculateDailyCosts(
    List<APIUsageRecord> usageRecords, {
    String currency = 'TWD',
  }) {
    try {
      final dailyCosts = <String, double>{};

      for (final record in usageRecords) {
        final cost = calculateAPICost(
          provider: record.provider,
          promptTokens: record.promptTokens,
          completionTokens: record.completionTokens,
          modelName: record.modelName,
          // 使用記錄中的模型名稱
          cachedTokens: record.cachedTokens ?? 0,
          // 使用記錄中的快取 tokens
          currency: currency,
        );

        final dateKey =
            record.date.toIso8601String().split('T')[0]; // YYYY-MM-DD
        dailyCosts[dateKey] = (dailyCosts[dateKey] ?? 0.0) + cost;
      }

      return dailyCosts;
    } catch (e) {
      logger.e('計算每日費用統計失敗: $e');
      return {};
    }
  }

  /// 計算提供商費用統計
  ///
  /// [usageRecords] 使用記錄列表
  /// [currency] 貨幣類型
  ///
  /// 返回提供商費用統計
  static Map<AIProvider, double> calculateProviderCosts(
    List<APIUsageRecord> usageRecords, {
    String currency = 'TWD',
  }) {
    try {
      final providerCosts = <AIProvider, double>{};

      for (final record in usageRecords) {
        final cost = calculateAPICost(
          provider: record.provider,
          promptTokens: record.promptTokens,
          completionTokens: record.completionTokens,
          modelName: record.modelName,
          // 使用記錄中的模型名稱
          cachedTokens: record.cachedTokens ?? 0,
          // 使用記錄中的快取 tokens
          currency: currency,
        );

        providerCosts[record.provider] =
            (providerCosts[record.provider] ?? 0.0) + cost;
      }

      return providerCosts;
    } catch (e) {
      logger.e('計算提供商費用統計失敗: $e');
      return {};
    }
  }

  /// 估算 token 數量（基於文本長度）
  static int estimateTokens(String text) {
    // 簡單估算：英文約 4 字符 = 1 token，中文約 1.5 字符 = 1 token
    final chineseCharCount = text.runes.where((rune) {
      return rune >= 0x4E00 && rune <= 0x9FFF; // 中文字符範圍
    }).length;

    final otherCharCount = text.length - chineseCharCount;

    // 中文字符：1.5 字符 ≈ 1 token
    // 其他字符：4 字符 ≈ 1 token
    final estimatedTokens =
        (chineseCharCount / 1.5 + otherCharCount / 4).ceil();

    return estimatedTokens;
  }

  /// 獲取費用節省建議
  ///
  /// [provider] 當前使用的提供商
  /// [promptTokens] 輸入 tokens
  /// [completionTokens] 輸出 tokens
  /// [currentModelName] 當前使用的模型名稱（可選）
  ///
  /// 返回節省建議
  static String getCostSavingTip(
    AIProvider provider,
    int promptTokens,
    int completionTokens, {
    String? currentModelName,
  }) {
    try {
      final currentCost = calculateAPICost(
        provider: provider,
        promptTokens: promptTokens,
        completionTokens: completionTokens,
        modelName: currentModelName,
        currency: 'TWD',
      );

      // 找出最便宜的選項
      String? bestOption;
      double lowestCost = double.infinity;

      // 檢查所有提供商的預設模型
      for (final p in AIProvider.values) {
        final cost = calculateAPICost(
          provider: p,
          promptTokens: promptTokens,
          completionTokens: completionTokens,
          currency: 'TWD',
        );

        if (cost < lowestCost) {
          lowestCost = cost;
          bestOption = p.displayName;
        }
      }

      // 檢查所有具體模型（使用統一的 calculateAPICost 方法）
      for (final provider in AIProvider.values) {
        if (provider == AIProvider.openai) {
          // 檢查 OpenAI 的所有模型
          for (final modelName in _openaiModelPricing.keys) {
            final cost = calculateAPICost(
              provider: provider,
              promptTokens: promptTokens,
              completionTokens: completionTokens,
              modelName: modelName,
              currency: 'TWD',
            );

            if (cost < lowestCost) {
              lowestCost = cost;
              bestOption = modelName;
            }
          }
        }
        // 可以在這裡添加其他提供商的具體模型檢查
      }

      if (bestOption != null && lowestCost < currentCost) {
        final savings = currentCost - lowestCost;
        final savingsPercent = ((savings / currentCost) * 100).toInt();
        return '💡 使用 $bestOption 可節省 ${formatCost(savings)} ($savingsPercent%)';
      }

      return '';
    } catch (e) {
      logger.e('獲取費用節省建議失敗: $e');
      return '';
    }
  }

  /// 獲取模型比較資訊
  ///
  /// [promptTokens] 輸入 tokens
  /// [completionTokens] 輸出 tokens
  /// [currency] 貨幣類型
  ///
  /// 返回所有模型的費用比較
  static Map<String, double> getModelCostComparison(
    int promptTokens,
    int completionTokens, {
    String currency = 'TWD',
  }) {
    final comparison = <String, double>{};

    try {
      // 添加所有提供商的費用
      for (final provider in AIProvider.values) {
        final cost = calculateAPICost(
          provider: provider,
          promptTokens: promptTokens,
          completionTokens: completionTokens,
          currency: currency,
        );
        comparison[provider.displayName] = cost;
      }

      // 添加 OpenAI 各模型的費用
      for (final entry in _openaiModelPricing.entries) {
        final pricing = entry.value;
        final inputCostUSD = (promptTokens / 1000000.0) * pricing.inputCostPerM;
        final outputCostUSD =
            (completionTokens / 1000000.0) * pricing.outputCostPerM;
        final totalCost = currency.toUpperCase() == 'USD'
            ? inputCostUSD + outputCostUSD
            : (inputCostUSD + outputCostUSD) * _usdToTwdRate;

        comparison[pricing.modelName] = totalCost;
      }
    } catch (e) {
      logger.e('獲取模型費用比較失敗: $e');
    }

    return comparison;
  }
}

/// API 定價資訊
class APIPricing {
  final double inputCostPerM; // 每 1000 輸入 tokens 的費用（美元）
  final double outputCostPerM; // 每 1000 輸出 tokens 的費用（美元）
  final String modelName; // 模型名稱
  final double? cachedInputCostPerM; // 每 1000 快取輸入 tokens 的費用（美元，可選）

  const APIPricing({
    required this.inputCostPerM,
    required this.outputCostPerM,
    required this.modelName,
    this.cachedInputCostPerM,
  });

  /// 是否支援快取輸入定價
  bool get supportsCachedInput => cachedInputCostPerM != null;

  @override
  String toString() {
    final cacheInfo =
        supportsCachedInput ? ', cached: \$$cachedInputCostPerM/1K' : '';
    return 'APIPricing(model: $modelName, input: \$$inputCostPerM/1K, output: \$$outputCostPerM/1K$cacheInfo)';
  }
}

/// API 使用記錄
class APIUsageRecord {
  final DateTime date;
  final AIProvider provider;
  final int promptTokens;
  final int completionTokens;
  final String? requestId;
  final String? modelName; // 新增：具體模組名稱
  final int? cachedTokens; // 新增：快取 tokens 數量

  const APIUsageRecord({
    required this.date,
    required this.provider,
    required this.promptTokens,
    required this.completionTokens,
    this.requestId,
    this.modelName,
    this.cachedTokens,
  });

  /// 獲取總 tokens
  int get totalTokens => promptTokens + completionTokens;

  /// 獲取非快取 tokens
  int get nonCachedTokens => promptTokens - (cachedTokens ?? 0);

  /// 是否有快取 tokens
  bool get hasCachedTokens => cachedTokens != null && cachedTokens! > 0;

  /// 計算此記錄的費用
  double calculateCost({String currency = 'TWD'}) {
    return APICostCalculatorService.calculateAPICost(
      provider: provider,
      promptTokens: promptTokens,
      completionTokens: completionTokens,
      modelName: modelName,
      cachedTokens: cachedTokens ?? 0,
      currency: currency,
    );
  }

  @override
  String toString() {
    final modelInfo = modelName != null ? ' ($modelName)' : '';
    final cacheInfo = hasCachedTokens ? ' [cached: $cachedTokens]' : '';
    return 'APIUsageRecord(date: $date, provider: ${provider.displayName}$modelInfo, tokens: $totalTokens$cacheInfo)';
  }

  /// 轉換為 JSON
  Map<String, dynamic> toJson() {
    return {
      'date': date.toIso8601String(),
      'provider': provider.name,
      'promptTokens': promptTokens,
      'completionTokens': completionTokens,
      'requestId': requestId,
      'modelName': modelName,
      'cachedTokens': cachedTokens,
    };
  }

  /// 從 JSON 創建
  factory APIUsageRecord.fromJson(Map<String, dynamic> json) {
    return APIUsageRecord(
      date: DateTime.parse(json['date']),
      provider: AIProvider.values.firstWhere(
        (p) => p.name == json['provider'],
        orElse: () => AIProvider.openai,
      ),
      promptTokens: json['promptTokens'] ?? 0,
      completionTokens: json['completionTokens'] ?? 0,
      requestId: json['requestId'],
      modelName: json['modelName'],
      cachedTokens: json['cachedTokens'],
    );
  }
}
