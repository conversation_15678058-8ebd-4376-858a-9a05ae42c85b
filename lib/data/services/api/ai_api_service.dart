import 'dart:convert';

import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

import '../../../core/utils/logger_utils.dart';
import '../../../shared/utils/chart_info_generator.dart';
import '../../../shared/utils/copy_options.dart';
import '../../models/astrology/chart_data.dart';
import '../../models/astrology/chart_settings.dart';
import 'ai_usage_stats_service.dart';
import 'astrology_service.dart';
import 'firebase_ai_usage_service.dart';
import 'interpretation_guidance_service.dart';
import 'remote_config_service.dart';

/**
 *  | 用途               | 推薦模型                | 原因             |
    | ---------------- | ------------------- | -------------- |
    | 本命盤與流年深度分析       | GPT-4.1             | 成本可控又精準        |
    | 每日自動星象卡 / 簡易對話助理 | GPT-4.1-mini 或 nano | 便宜、效率高         |
    | 多模態占星助理（圖像 + 聲音） | GPT-4o              | 可讀星盤圖、支援語音     |
    | 專業客製占星報告、自動寫文章   | GPT-4.5-preview     | 僅限高價客戶或高品質輸出場景 |
 */

/// AI API 服務類，支援 OpenAI 和 Anthropic API
class AIApiService {
  static const String _selectedModelKey = 'selected_ai_model';
  static const String _openaiApiKeyKey = 'openai_api_key';
  static const String _anthropicApiKeyKey = 'anthropic_api_key';
  static const String _groqApiKeyKey = 'groq_api_key';
  static const String _geminiApiKeyKey = 'gemini_api_key';
  static const int maxTokenLength =
      16000; // 最好設為 8000~16000. 用 GPT-4-turbo 處理大型文檔	可設到 128000
  static const int tokenLength = 4096; // GPT-3 最大 token 長度
  static const int tokenLengthGroq = 8192; // GPT-3 最大 token 長度
  // 支援的 AI 模型配置（靜態預設值，作為後備）
  static const Map<String, AIModelConfig> _defaultModelConfigs = {
    'gpt-5': AIModelConfig(
      id: 'gpt-5',
      name: 'gpt-5',
      provider: AIProvider.openai,
      endpoint: 'https://api.openai.com/v1/chat/completions',
      maxTokens: maxTokenLength,
      temperature: 1.0,
    ),
    'gpt-4.5-preview': AIModelConfig(
      id: 'gpt-4.5-preview',
      name: 'gpt-4.5-preview',
      provider: AIProvider.openai,
      endpoint: 'https://api.openai.com/v1/chat/completions',
      maxTokens: maxTokenLength,
      temperature: 1,
    ),
    'gpt-4.1': AIModelConfig(
      id: 'gpt-4.1',
      name: 'gpt-4.1',
      provider: AIProvider.openai,
      endpoint: 'https://api.openai.com/v1/chat/completions',
      maxTokens: 128000,
      temperature: 2.0,
    ),
    'gpt-4.1-mini': AIModelConfig(
      id: 'gpt-4.1-mini',
      name: 'gpt-4.1-mini',
      provider: AIProvider.openai,
      endpoint: 'https://api.openai.com/v1/chat/completions',
      maxTokens: maxTokenLength,
      temperature: 0.7,
    ),
    'gpt-4.1-nano': AIModelConfig(
      id: 'gpt-4.1-nano',
      name: 'gpt-4.1-nano',
      provider: AIProvider.openai,
      endpoint: 'https://api.openai.com/v1/chat/completions',
      maxTokens: maxTokenLength,
      temperature: 0.7,
    ),
    'gpt-4o': AIModelConfig(
      id: 'gpt-4o',
      name: 'gpt-4o',
      provider: AIProvider.openai,
      endpoint: 'https://api.openai.com/v1/chat/completions',
      maxTokens: maxTokenLength,
      temperature: 0.7,
    ),
    'gpt-4o-mini': AIModelConfig(
      id: 'gpt-4o-mini',
      name: 'gpt-4o-mini',
      provider: AIProvider.openai,
      endpoint: 'https://api.openai.com/v1/chat/completions',
      maxTokens: maxTokenLength,
      temperature: 0.7,
    ),
    'gpt-4': AIModelConfig(
      id: 'gpt-4',
      name: 'gpt-4',
      provider: AIProvider.openai,
      endpoint: 'https://api.openai.com/v1/chat/completions',
      maxTokens: maxTokenLength,
      temperature: 0.7,
    ),
    'gpt-4-1106-preview': AIModelConfig(
      id: 'gpt-4-1106-preview',
      name: 'gpt-4-1106-preview',
      provider: AIProvider.openai,
      endpoint: 'https://api.openai.com/v1/chat/completions',
      maxTokens: maxTokenLength,
      temperature: 1.0,
    ),
    'gpt-4-turbo': AIModelConfig(
      id: 'gpt-4-turbo',
      name: 'GPT-4 Turbo',
      provider: AIProvider.openai,
      endpoint: 'https://api.openai.com/v1/chat/completions',
      maxTokens: maxTokenLength,
      temperature: 0.7,
    ),
    'gpt-3.5-turbo': AIModelConfig(
      id: 'gpt-3.5-turbo',
      name: 'GPT-3.5 Turbo',
      provider: AIProvider.openai,
      endpoint: 'https://api.openai.com/v1/chat/completions',
      maxTokens: tokenLength,
      temperature: 0.7,
    ),
    'claude-3-opus': AIModelConfig(
      id: 'claude-3-opus-20240229',
      name: 'Claude 3 Opus',
      provider: AIProvider.anthropic,
      endpoint: 'https://api.anthropic.com/v1/messages',
      maxTokens: maxTokenLength,
      temperature: 0.7,
    ),
    'claude-3-sonnet': AIModelConfig(
      id: 'claude-3-sonnet-20240229',
      name: 'Claude 3 Sonnet',
      provider: AIProvider.anthropic,
      endpoint: 'https://api.anthropic.com/v1/messages',
      maxTokens: maxTokenLength,
      temperature: 0.7,
    ),
    'claude-3-haiku': AIModelConfig(
      id: 'claude-3-haiku-20240307',
      name: 'Claude 3 Haiku',
      provider: AIProvider.anthropic,
      endpoint: 'https://api.anthropic.com/v1/messages',
      maxTokens: maxTokenLength,
      temperature: 0.7,
    ),
    // Groq 生產模型
    'llama-3.3-70b-versatile': AIModelConfig(
      id: 'llama-3.3-70b-versatile',
      name: 'Llama 3.3 70B Versatile',
      provider: AIProvider.groq,
      endpoint: 'https://api.groq.com/openai/v1/chat/completions',
      maxTokens: 32768,
      temperature: 0.7,
    ),
    'llama-3.1-8b-instant': AIModelConfig(
      id: 'llama-3.1-8b-instant',
      name: 'Llama 3.1 8B Instant',
      provider: AIProvider.groq,
      endpoint: 'https://api.groq.com/openai/v1/chat/completions',
      maxTokens: tokenLengthGroq,
      temperature: 0.7,
    ),
    'llama3-8b-8192': AIModelConfig(
      id: 'llama3-8b-8192',
      name: 'Llama 3 8B',
      provider: AIProvider.groq,
      endpoint: 'https://api.groq.com/openai/v1/chat/completions',
      maxTokens: tokenLengthGroq,
      temperature: 0.7,
    ),
    'gemma2-9b-it': AIModelConfig(
      id: 'gemma2-9b-it',
      name: 'Gemma 2 9B',
      provider: AIProvider.groq,
      endpoint: 'https://api.groq.com/openai/v1/chat/completions',
      maxTokens: tokenLengthGroq,
      temperature: 0.7,
    ),
    // Google Gemini 模型
    'gemini-2.5-pro': AIModelConfig(
      id: 'gemini-2.5-pro',
      name: 'gemini-2.5-pro',
      provider: AIProvider.gemini,
      endpoint:
          'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent',
      maxTokens: maxTokenLength,
      temperature: 0.7,
    ),
    'gemini-2.5-flash': AIModelConfig(
      id: 'gemini-2.5-flash',
      name: 'gemini-2.5-flash',
      provider: AIProvider.gemini,
      endpoint:
          'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent',
      maxTokens: maxTokenLength,
      temperature: 0.7,
    ),
    'gemini-2.0-flash': AIModelConfig(
      id: 'gemini-2.0-flash',
      name: 'Gemini 2.0 Flash',
      provider: AIProvider.gemini,
      endpoint:
          'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent',
      maxTokens: maxTokenLength,
      temperature: 0.7,
    ),
    'gemini-1.5-pro': AIModelConfig(
      id: 'gemini-1.5-pro',
      name: 'Gemini 1.5 Pro',
      provider: AIProvider.gemini,
      endpoint:
          'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro:generateContent',
      maxTokens: maxTokenLength,
      temperature: 0.7,
    ),
    'gemini-1.5-flash': AIModelConfig(
      id: 'gemini-1.5-flash',
      name: 'Gemini 1.5 Flash',
      provider: AIProvider.gemini,
      endpoint:
          'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent',
      maxTokens: maxTokenLength,
      temperature: 0.7,
    ),
  };

  // 動態模型配置緩存
  static Map<String, AIModelConfig>? _dynamicModelConfigs;
  static DateTime? _lastConfigUpdate;
  static const Duration _configCacheTimeout = Duration(minutes: 30);

  /// 從 Remote Config 載入模型配置
  static Map<String, AIModelConfig> _loadModelConfigsFromRemote() {
    try {
      logger.d('開始從 Remote Config 載入模型配置...');
      final remoteModels = RemoteConfigService.getEnabledAIModels();
      final Map<String, AIModelConfig> configs = {};

      for (final modelData in remoteModels) {
        try {
          final config = _parseModelConfig(modelData);
          if (config != null) {
            configs[config.id] = config;
            logger.d('成功載入模型配置: ${config.id}');
          }
        } catch (e) {
          logger.e('解析模型配置失敗: $e, 模型數據: $modelData');
        }
      }

      logger.i('從 Remote Config 載入了 ${configs.length} 個模型配置');
      return configs;
    } catch (e) {
      logger.e('從 Remote Config 載入模型配置失敗: $e');
      return {};
    }
  }

  /// 解析單個模型配置
  static AIModelConfig? _parseModelConfig(Map<String, dynamic> modelData) {
    try {
      final id = modelData['id'] as String?;
      final name = modelData['name'] as String?;
      final providerStr = modelData['provider'] as String?;
      final endpoint = modelData['endpoint'] as String?;
      final maxTokens = modelData['maxTokens'] as int?;
      final temperature = (modelData['temperature'] as num?)?.toDouble();

      if (id == null ||
          name == null ||
          providerStr == null ||
          endpoint == null) {
        logger.w('模型配置缺少必要字段: $modelData');
        return null;
      }

      // 解析 provider
      AIProvider? provider;
      switch (providerStr.toLowerCase()) {
        case 'openai':
          provider = AIProvider.openai;
          break;
        case 'anthropic':
          provider = AIProvider.anthropic;
          break;
        case 'groq':
          provider = AIProvider.groq;
          break;
        case 'gemini':
          provider = AIProvider.gemini;
          break;
        default:
          logger.w('不支援的 AI 提供商: $providerStr');
          return null;
      }

      return AIModelConfig(
        id: id,
        name: name,
        provider: provider,
        endpoint: endpoint,
        maxTokens: maxTokens ?? maxTokenLength,
        temperature: temperature ?? 0.7,
      );
    } catch (e) {
      logger.e('解析模型配置時發生錯誤: $e');
      return null;
    }
  }

  /// 獲取所有可用的模型配置
  static Map<String, AIModelConfig> _getModelConfigs() {
    // 檢查緩存是否有效
    final now = DateTime.now();
    if (_dynamicModelConfigs != null &&
        _lastConfigUpdate != null &&
        now.difference(_lastConfigUpdate!) < _configCacheTimeout) {
      logger.d('使用緩存的模型配置');
      return _dynamicModelConfigs!;
    }

    // 嘗試從 Remote Config 載入
    final remoteConfigs = _loadModelConfigsFromRemote();

    if (remoteConfigs.isNotEmpty) {
      logger.i('使用 Remote Config 的模型配置，共 ${remoteConfigs.length} 個模型');
      _dynamicModelConfigs = remoteConfigs;
      _lastConfigUpdate = now;
      return remoteConfigs;
    } else {
      logger.w('Remote Config 模型配置為空，使用預設配置');
      _dynamicModelConfigs = Map.from(_defaultModelConfigs);
      _lastConfigUpdate = now;
      return _defaultModelConfigs;
    }
  }

  /// 獲取所有可用的模型
  static List<AIModelConfig> get availableModels =>
      _getModelConfigs().values.toList();

  /// 強制刷新模型配置
  static void refreshModelConfigs() {
    logger.i('強制刷新模型配置...');
    _dynamicModelConfigs = null;
    _lastConfigUpdate = null;
    // 觸發重新載入
    _getModelConfigs();
  }

  /// 獲取當前選中的模型
  static Future<String> getSelectedModel() async {
    try {
      // 首先嘗試從 SharedPreferences 獲取用戶設定
      final prefs = await SharedPreferences.getInstance();
      final userSelectedModel = prefs.getString(_selectedModelKey);

      if (userSelectedModel != null && userSelectedModel.isNotEmpty) {
        // 檢查用戶選擇的模型是否仍然可用
        final modelConfigs = _getModelConfigs();
        if (modelConfigs.containsKey(userSelectedModel)) {
          logger.d('使用用戶選擇的模型: $userSelectedModel');
          return userSelectedModel;
        } else {
          logger.w('用戶選擇的模型 $userSelectedModel 不再可用，將使用 Remote Config 預設值');
        }
      }

      // 嘗試從 Remote Config 獲取預設選中的模型
      final remoteSelectedModel = RemoteConfigService.getSelectedModelId();
      if (remoteSelectedModel.isNotEmpty) {
        final modelConfigs = _getModelConfigs();
        if (modelConfigs.containsKey(remoteSelectedModel)) {
          logger.d('使用 Remote Config 預設模型: $remoteSelectedModel');
          // 將 Remote Config 的選擇保存到本地，避免重複查詢
          await prefs.setString(_selectedModelKey, remoteSelectedModel);
          return remoteSelectedModel;
        } else {
          logger.w('Remote Config 指定的模型 $remoteSelectedModel 不可用');
        }
      }

      // 如果都沒有，使用第一個可用的模型
      final availableModels = _getModelConfigs();
      if (availableModels.isNotEmpty) {
        final firstModel = availableModels.keys.first;
        logger.d('使用第一個可用模型: $firstModel');
        await prefs.setString(_selectedModelKey, firstModel);
        return firstModel;
      }

      logger.e('沒有可用的 AI 模型');
      return '';
    } catch (e) {
      logger.e('獲取選中模型時發生錯誤: $e');
      return '';
    }
  }

  /// 設置選中的模型
  static Future<void> setSelectedModel(String modelId) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_selectedModelKey, modelId);
  }

  /// 獲取當前選中模型的 Provider
  static Future<AIProvider> getCurrentProvider() async {
    try {
      final selectedModelId = await getSelectedModel();
      final modelConfigs = _getModelConfigs();
      final modelConfig = modelConfigs[selectedModelId];

      if (modelConfig != null) {
        logger.d('當前選中的 AI Provider: ${modelConfig.provider.displayName}');
        return modelConfig.provider;
      } else {
        logger.w('無法找到選中模型的配置，使用預設 Provider: OpenAI');
        return AIProvider.openai;
      }
    } catch (e) {
      logger.e('獲取當前 Provider 失敗: $e，使用預設 Provider: OpenAI');
      return AIProvider.openai;
    }
  }

  /// 獲取 OpenAI API Key
  static Future<String?> getOpenAIApiKey() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_openaiApiKeyKey);
  }

  /// 設置 OpenAI API Key
  static Future<void> setOpenAIApiKey(String apiKey) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_openaiApiKeyKey, apiKey);
  }

  /// 獲取 Anthropic API Key
  static Future<String?> getAnthropicApiKey() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_anthropicApiKeyKey);
  }

  /// 設置 Anthropic API Key
  static Future<void> setAnthropicApiKey(String apiKey) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_anthropicApiKeyKey, apiKey);
  }

  /// 獲取 Groq API Key
  static Future<String?> getGroqApiKey() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_groqApiKeyKey);
  }

  /// 設置 Groq API Key
  static Future<void> setGroqApiKey(String apiKey) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_groqApiKeyKey, apiKey);
  }

  /// 獲取 Gemini API Key
  static Future<String?> getGeminiApiKey() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_geminiApiKeyKey);
  }

  /// 設置 Gemini API Key
  static Future<void> setGeminiApiKey(String apiKey) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_geminiApiKeyKey, apiKey);
  }

  /// 檢查 API Key 是否已設置
  static Future<bool> isApiKeyConfigured(AIProvider provider) async {
    switch (provider) {
      case AIProvider.openai:
        final key = await getOpenAIApiKey();
        return key != null && key.isNotEmpty;
      case AIProvider.anthropic:
        final key = await getAnthropicApiKey();
        return key != null && key.isNotEmpty;
      case AIProvider.groq:
        final key = await getGroqApiKey();
        return key != null && key.isNotEmpty;
      case AIProvider.gemini:
        final key = await getGeminiApiKey();
        return key != null && key.isNotEmpty;
    }
  }

  /// 獲取星盤解讀
  static Future<AIApiResponse> getChartInterpretation({
    required ChartData chartData,
    required String prompt,
    String? customModelId,
  }) async {
    try {
      final startTime = DateTime.now();
      final modelId = customModelId ?? await getSelectedModel();
      final modelConfigs = _getModelConfigs();
      final modelConfig = modelConfigs[modelId];
      AIApiResponse apiResponse;

      if (modelConfig == null) {
        logger.w('不支援的 AI 模型: $modelId');
        apiResponse = AIApiResponse(
          success: false,
          content: '設定有誤，請聯繫管理員。',
          totalTokens: 0,
          promptTokens: 0,
          completionTokens: 0,
        );
        return apiResponse;
      }

      logger.i("====== AI 星盤解讀請求開始 ======");
      logger.i("選擇的模型：${modelConfig.name} ($modelId)");
      logger.i("API 提供商：${modelConfig.provider.name}");
      logger.i("請求時間：${startTime.toIso8601String()}");

      // 檢查 API Key
      final isConfigured = await isApiKeyConfigured(modelConfig.provider);
      if (!isConfigured) {
        logger.w('${modelConfig.provider.name} API Key 未設置');
        apiResponse = AIApiResponse(
          success: false,
          content: '設定有誤，請聯繫管理員。',
          totalTokens: 0,
          promptTokens: 0,
          completionTokens: 0,
        );
        return apiResponse;
      }

      if (chartData.planets == null || chartData.houses == null) {
        logger.w('數據為空，重新計算');
        // 重新計算
        ChartSettings chartSettings = await ChartSettings.loadFromPrefs();
        chartData = await AstrologyService()
            .calculateChartData(chartData, chartSettings: chartSettings);
      }

      // 構建星盤數據摘要
      final chartSummary = await buildChartSummary(chartData);
      final fullPrompt = await buildFullPrompt(prompt, chartSummary);

      logger.d("完整提示詞：\n$fullPrompt");


      // 🔥 Firebase 使用量控管：檢查是否可以使用
      final estimatedTokens = AIUsageStatsService.estimateTokens(fullPrompt) + 1000; // 預估回應 tokens
      final canUse = await FirebaseAIUsageService.canUseProvider(
        provider: modelConfig.provider,
        estimatedTokens: estimatedTokens,
      );

      if (!canUse) {
        logger.w('⚠️ 今日 ${modelConfig.provider.displayName} 使用量已達上限，請明日再試或選擇其他 AI 模型。');
        apiResponse = AIApiResponse(
          success: false,
          content: '今日使用量已達上限，請明日再試或者聯繫管理員。',
          totalTokens: 0,
          promptTokens: 0,
          completionTokens: 0,
        );
        return apiResponse;
      }

      logger.i("✅ 使用量檢查通過，預估使用 $estimatedTokens tokens");


      switch (modelConfig.provider) {
        case AIProvider.openai:
          apiResponse = await _callOpenAIAPI(modelConfig, fullPrompt);
          break;
        case AIProvider.anthropic:
          apiResponse = await _callAnthropicAPI(modelConfig, fullPrompt);
          break;
        case AIProvider.groq:
          apiResponse = await _callGroqAPI(modelConfig, fullPrompt);
          break;
        case AIProvider.gemini:
          apiResponse = await _callGeminiAPI(modelConfig, fullPrompt);
          break;
      }

      final endTime = DateTime.now();
      final duration = endTime.difference(startTime);
      logger.i("請求完成，耗時：${duration.inMilliseconds}ms");
      logger.i("回應長度：${apiResponse.content.length} 字符");
      logger.i("Token 使用量：${apiResponse.totalTokens ?? '未知'}");

      // 記錄實際使用量到 Firebase 和本地
      await _recordUsageStats(modelConfig.provider, fullPrompt, apiResponse);

      // 注意：分析次數的扣除已經在 AIInterpretationResultPage 的 _checkAndConsumePermission() 中處理
      // 這裡不再重複扣除，避免雙重扣費問題
      logger.i("✅ AI 解析成功完成");
      apiResponse.success = true;
      return apiResponse;
    } catch (e) {
      logger.e("AI 解讀請求失敗：$e");
      rethrow;
    }
  }

  /// 調用 OpenAI API
  static Future<AIApiResponse> _callOpenAIAPI(
      AIModelConfig config, String prompt) async {
    final apiKey = await getOpenAIApiKey();
    if (apiKey == null || apiKey.isEmpty) {
      throw Exception('OpenAI API Key 未設置');
    }

    var requestBody = {
      'model': config.id,
      'messages': [
        {
          'role': 'system',
          'content': '你是一位專業的占星師，擅長解讀星盤並提供深入的分析。請用繁體中文回答。',
        },
        {
          'role': 'user',
          'content': prompt,
        },
      ],
      'max_tokens': config.maxTokens,
      'temperature': config.temperature,
    };

    if (config.id.contains('gpt-5')) {
      requestBody = {
        'model': config.id,
        'messages': [
          {
            'role': 'system',
            'content': '你是一位專業的占星師，擅長解讀星盤並提供深入的分析。請用繁體中文回答。',
          },
          {
            'role': 'user',
            'content': prompt,
          },
        ],
        'max_completion_tokens': config.maxTokens,
        'temperature': config.temperature,
      };
    }

    logger.d("OpenAI 請求內容：\n${jsonEncode(requestBody)}");

    final response = await http.post(
      Uri.parse(config.endpoint),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $apiKey',
      },
      body: jsonEncode(requestBody),
    );

    logger.i("OpenAI 響應狀態碼：${response.statusCode}");
    logger.d("OpenAI 響應內容：\n${response.body}");

    if (response.statusCode != 200) {
      logger.e("OpenAI 響應錯誤：${response.statusCode} - ${response.body}");
      throw Exception(
          '請求失敗：${response.statusCode} - ${response.body}');
    }

    final responseData = jsonDecode(response.body);
    if (responseData['choices'] == null || responseData['choices'].isEmpty) {
      logger.e("OpenAI 回應格式錯誤：$responseData");
      throw Exception('回應格式錯誤');
    }

    final content = responseData['choices'][0]['message']['content'] as String;

    // 提取模型名稱
    final modelName = responseData['model'] as String?;

    // 提取 token 使用量
    int? totalTokens;
    int? promptTokens;
    int? completionTokens;
    Map<String, dynamic>? rawUsage;

    if (responseData['usage'] != null) {
      rawUsage = Map<String, dynamic>.from(responseData['usage']);
      totalTokens = rawUsage['total_tokens'] as int?;
      promptTokens = rawUsage['prompt_tokens'] as int?;
      completionTokens = rawUsage['completion_tokens'] as int?;

      logger.d("OpenAI Token 使用量 - 總計: $totalTokens, 提示: $promptTokens, 完成: $completionTokens, 模型: $modelName");
    }

    return AIApiResponse(
      success: true,
      content: content,
      totalTokens: totalTokens,
      promptTokens: promptTokens,
      completionTokens: completionTokens,
      modelName: modelName,
      rawUsage: rawUsage,
    );
  }

  /// 調用 Anthropic API
  static Future<AIApiResponse> _callAnthropicAPI(
      AIModelConfig config, String prompt) async {
    final apiKey = await getAnthropicApiKey();
    if (apiKey == null || apiKey.isEmpty) {
      throw Exception('Anthropic API Key 未設置');
    }

    final requestBody = {
      'model': config.id,
      'max_tokens': config.maxTokens,
      'temperature': config.temperature,
      'messages': [
        {
          'role': 'user',
          'content': prompt,
        },
      ],
      'system': '你是一位專業的占星師，擅長解讀星盤並提供深入的分析。請用繁體中文回答。',
    };

    logger.d("Anthropic 請求內容：\n${jsonEncode(requestBody)}");

    final response = await http.post(
      Uri.parse(config.endpoint),
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': apiKey,
        'anthropic-version': '2023-06-01',
      },
      body: jsonEncode(requestBody),
    );

    logger.i("Anthropic 響應狀態碼：${response.statusCode}");
    logger.d("Anthropic 響應內容：\n${response.body}");

    if (response.statusCode != 200) {
      throw Exception(
          '請求失敗：${response.statusCode} - ${response.body}');
    }

    final responseData = jsonDecode(response.body);
    if (responseData['content'] == null || responseData['content'].isEmpty) {
      throw Exception('回應格式錯誤');
    }

    final content = responseData['content'][0]['text'] as String;

    // 提取 token 使用量
    int? totalTokens;
    int? promptTokens;
    int? completionTokens;

    if (responseData['usage'] != null) {
      // Anthropic API 回應格式：input_tokens, output_tokens
      promptTokens = responseData['usage']['input_tokens'] as int?;
      completionTokens = responseData['usage']['output_tokens'] as int?;

      if (promptTokens != null && completionTokens != null) {
        totalTokens = promptTokens + completionTokens;
      }

      logger.d("Anthropic Token 使用量 - 總計: $totalTokens, 輸入: $promptTokens, 輸出: $completionTokens");
    }

    return AIApiResponse(
      success: true,
      content: content,
      totalTokens: totalTokens,
      promptTokens: promptTokens,
      completionTokens: completionTokens,
    );
  }

  /// 調用 Groq API
  static Future<AIApiResponse> _callGroqAPI(
      AIModelConfig config, String prompt) async {
    final apiKey = await getGroqApiKey();
    if (apiKey == null || apiKey.isEmpty) {
      throw Exception('Groq API Key 未設置');
    }

    final requestBody = {
      'model': config.id,
      'messages': [
        {
          'role': 'system',
          'content': '你是一位專業的占星師，擅長解讀星盤並提供深入的分析。請用繁體中文回答。',
        },
        {
          'role': 'user',
          'content': prompt,
        },
      ],
      'max_tokens': config.maxTokens,
      'temperature': config.temperature,
    };

    logger.d("Groq 請求內容：\n${jsonEncode(requestBody)}");

    final response = await http.post(
      Uri.parse(config.endpoint),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $apiKey',
      },
      body: jsonEncode(requestBody),
    );

    logger.i("Groq 響應狀態碼：${response.statusCode}");
    logger.d("Groq 響應內容：\n${response.body}");

    if (response.statusCode != 200) {
      throw Exception(
          '請求失敗：${response.statusCode} - ${response.body}');
    }

    final responseData = jsonDecode(response.body);
    if (responseData['choices'] == null || responseData['choices'].isEmpty) {
      throw Exception('回應格式錯誤');
    }

    final content = responseData['choices'][0]['message']['content'] as String;

    // 提取 token 使用量（Groq 使用 OpenAI 相容格式）
    int? totalTokens;
    int? promptTokens;
    int? completionTokens;

    if (responseData['usage'] != null) {
      totalTokens = responseData['usage']['total_tokens'] as int?;
      promptTokens = responseData['usage']['prompt_tokens'] as int?;
      completionTokens = responseData['usage']['completion_tokens'] as int?;

      logger.d("Groq Token 使用量 - 總計: $totalTokens, 提示: $promptTokens, 完成: $completionTokens");
    }

    return AIApiResponse(
      success: true,
      content: content,
      totalTokens: totalTokens,
      promptTokens: promptTokens,
      completionTokens: completionTokens,
    );
  }

  /// 調用 Gemini API
  static Future<AIApiResponse> _callGeminiAPI(
      AIModelConfig config, String prompt) async {
    final apiKey = await getGeminiApiKey();
    if (apiKey == null || apiKey.isEmpty) {
      throw Exception('Gemini API Key 未設置');
    }

    final requestBody = {
      'contents': [
        {
          'parts': [
            {
              'text': prompt,
            }
          ]
        }
      ],
      'generationConfig': {
        'temperature': config.temperature,
        'maxOutputTokens': config.maxTokens,
      },
    };

    logger.d("Gemini 請求內容：\n${jsonEncode(requestBody)}");

    final response = await http.post(
      Uri.parse('${config.endpoint}?key=$apiKey'),
      headers: {
        'Content-Type': 'application/json',
      },
      body: jsonEncode(requestBody),
    );

    logger.i("Gemini 響應狀態碼：${response.statusCode}");
    logger.d("Gemini 響應內容：\n${response.body}");

    if (response.statusCode != 200) {
      throw Exception(
          '請求失敗：${response.statusCode} - ${response.body}');
    }

    final responseData = jsonDecode(response.body);
    if (responseData['candidates'] == null ||
        responseData['candidates'].isEmpty) {
      throw Exception('回應格式錯誤');
    }

    final candidate = responseData['candidates'][0];
    if (candidate['content'] == null ||
        candidate['content']['parts'] == null ||
        candidate['content']['parts'].isEmpty) {
      throw Exception('Gemini API 回應內容為空');
    }

    final content = candidate['content']['parts'][0]['text'] as String;

    // 提取 token 使用量
    int? totalTokens;
    int? promptTokens;
    int? completionTokens;

    if (responseData['usageMetadata'] != null) {
      // Gemini API 回應格式：promptTokenCount, candidatesTokenCount, totalTokenCount
      promptTokens = responseData['usageMetadata']['promptTokenCount'] as int?;
      completionTokens = responseData['usageMetadata']['candidatesTokenCount'] as int?;
      totalTokens = responseData['usageMetadata']['totalTokenCount'] as int?;

      logger.d("Gemini Token 使用量 - 總計: $totalTokens, 提示: $promptTokens, 候選: $completionTokens");
    }

    return AIApiResponse(
      success: true,
      content: content,
      totalTokens: totalTokens,
      promptTokens: promptTokens,
      completionTokens: completionTokens,
    );
  }

  /// 構建星盤數據摘要
  static Future<String> buildChartSummary(ChartData chartData) async {
    try {
      // 使用統一的星盤資訊生成工具
      return await ChartInfoGenerator.generateFromChartData(
        chartData: chartData,
        options: const CopyOptions(
          includeBasicInfo: false,
          includePlanetPositions: true,
          includeHousePositions: true,
          includeAspects: true,
          includeElementStats: true,
          usePrettyFormat: true,
        ),
        calculateIfNeeded: false, // AI 服務中不自動計算，使用現有數據
      );
    } catch (e) {
      // 如果生成失敗，返回基本信息
      logger.w("生成星盤摘要失敗，使用基本信息：$e");
      return _buildBasicChartInfo(chartData);
    }
  }

  /// 構建基本星盤信息（備用方案）
  static String _buildBasicChartInfo(ChartData chartData) {
    final buffer = StringBuffer();

    buffer.writeln('=== 星盤基本資訊 ===');
    buffer.writeln(
        '出生日期：${chartData.primaryPerson.dateTime.year}/${chartData.primaryPerson.dateTime.month}/${chartData.primaryPerson.dateTime.day} ${chartData.primaryPerson.dateTime.hour}:${chartData.primaryPerson.dateTime.minute.toString().padLeft(2, '0')}');
    buffer.writeln('出生地點：${chartData.primaryPerson.birthPlace}');
    buffer.writeln('星盤類型：${chartData.chartType.displayName}');

    // 如果是合盤，添加第二人信息
    if (chartData.secondaryPerson != null) {
      buffer.writeln('\n=== 第二人資訊 ===');
      buffer.writeln(
          '出生日期：${chartData.secondaryPerson!.dateTime.year}/${chartData.secondaryPerson!.dateTime.month}/${chartData.secondaryPerson!.dateTime.day} ${chartData.secondaryPerson!.dateTime.hour}:${chartData.secondaryPerson!.dateTime.minute.toString().padLeft(2, '0')}');
      buffer.writeln('出生地點：${chartData.secondaryPerson!.birthPlace}');
    }

    // 特定日期（用於推運盤等）
    if (chartData.specificDate != null) {
      buffer.writeln(
          '特定日期：${chartData.specificDate!.year}/${chartData.specificDate!.month}/${chartData.specificDate!.day} ${chartData.specificDate!.hour}:${chartData.specificDate!.minute.toString().padLeft(2, '0')}');
    }

    return buffer.toString();
  }

  /// 記錄使用統計（同時記錄到 Firebase 和本地）
  static Future<void> _recordUsageStats(
      AIProvider provider, String prompt, AIApiResponse apiResponse) async {
    try {
      // 優先使用 API 回傳的實際 token 數量，如果沒有則使用估算
      int actualTokens;
      if (apiResponse.totalTokens != null) {
        actualTokens = apiResponse.totalTokens!;
        logger.i("✅ 使用 API 回傳的實際 token 數量：$actualTokens");
      } else {
        actualTokens = AIUsageStatsService.estimateRequestTokens(
          prompt: prompt,
          response: apiResponse.content,
        );
        logger.w("⚠️ API 未回傳 token 數量，使用估算值：$actualTokens");
      }

      // 記錄詳細的 token 使用資訊
      if (apiResponse.promptTokens != null && apiResponse.completionTokens != null) {
        logger.d("Token 詳細資訊 - 提示: ${apiResponse.promptTokens}, 完成: ${apiResponse.completionTokens}, 總計: $actualTokens");
      }

      // 🔥 記錄到 Firebase（主要的使用量控管）
      await FirebaseAIUsageService.recordUsage(
        provider: provider,
        actualTokens: actualTokens,
        prompt: prompt,
        response: apiResponse.content,
      );

      // 記錄到本地（向後相容和離線統計）
      await AIUsageStatsService.recordUsage(
        provider: provider,
        tokens: actualTokens,
      );

      logger.d("記錄使用統計：${provider.displayName} - $actualTokens tokens (Firebase + 本地)");
    } catch (e) {
      logger.w("記錄使用統計失敗：$e");

      // 如果 Firebase 記錄失敗，至少確保本地記錄成功
      try {
        final fallbackTokens = apiResponse.totalTokens ??
            AIUsageStatsService.estimateRequestTokens(
              prompt: prompt,
              response: apiResponse.content,
            );

        await AIUsageStatsService.recordUsage(
          provider: provider,
          tokens: fallbackTokens,
        );
        logger.d("Firebase 記錄失敗，但本地記錄成功 - $fallbackTokens tokens");
      } catch (localError) {
        logger.e("本地記錄也失敗：$localError");
      }
    }
  }

  /**
   * 請根據以上星盤資訊，提供專業的占星解讀。請注意：
      1. 使用繁體中文回答
      2. 提供具體而實用的分析
      3. 結合行星、星座、宮位的綜合影響
      4. 語言要親切易懂，避免過於艱深的術語
      5. 如果有相位資訊，請一併分析相位的影響
      6. **請使用 Markdown 格式回答**，包括：
      - 使用 # ## ### 來組織標題結構
      - 使用 **粗體** 來強調重要概念
      - 使用 *斜體* 來標示占星術語
      - 使用 - 或 * 來建立清單
      - 使用 > 來建立引用區塊（如重要提醒）
      - 適當使用分段來提高可讀性
      範例格式：
      # 星盤解讀

      ## 整體概況
      您的星盤顯示...

      ## 重要特質
      - **主要特質**：...
      - **天賦才能**：...

      > **重要提醒**：這些分析僅供參考...
      請根據以上星盤資訊，提供專業的占星解讀。請注意：
      1. 使用繁體中文回答
      2. 提供具體而實用的分析
      3. 結合行星、星座、宮位的綜合影響
      4. 語言要親切易懂，避免術語
      5. 如果有相位資訊，請一併分析相位的影響
      6. 不使用親愛的、你的內心充滿夢想這類主觀描述）禁用元素：開場白、讚美詞、表情符號（例如🌞、🌙）

   */

  /// 構建完整提示詞
  static Future<String> buildFullPrompt(
      String userPrompt, String chartSummary) async {
    final buffer = StringBuffer();

    // 加入星盤摘要
    buffer.writeln(chartSummary); // 星盤資料（行星、宮位、相位等）

    // 使用者提問主題
    // buffer.writeln('解讀要求：');
    buffer.writeln(userPrompt); // 使用者輸入的問題，例如：感情、事業、健康等

    // 解讀指引 - 從配置服務獲取
    final guidance = await InterpretationGuidanceService.getGuidance();
    buffer.writeln(guidance);

    return buffer.toString();
  }
}

/// AI API 回應資料模型
class AIApiResponse {
  late bool success;
  final String content;
  final int? totalTokens;
  final int? promptTokens;
  final int? completionTokens;
  final String? modelName; // 實際使用的模型名稱
  final Map<String, dynamic>? rawUsage; // 原始 usage 資訊

  AIApiResponse({
    required this.success,
    required this.content,
    this.totalTokens,
    this.promptTokens,
    this.completionTokens,
    this.modelName,
    this.rawUsage,
  });

  @override
  String toString() {
    return 'AIApiResponse(content: ${content.length} chars, totalTokens: $totalTokens, promptTokens: $promptTokens, completionTokens: $completionTokens, model: $modelName)';
  }
}

/// AI 提供商枚舉
enum AIProvider {
  openai('OpenAI'),
  anthropic('Anthropic'),
  groq('Groq'),
  gemini('Google Gemini');

  const AIProvider(this.displayName);

  final String displayName;
}

/// AI 模型配置類
class AIModelConfig {
  final String id;
  final String name;
  final AIProvider provider;
  final String endpoint;
  final int maxTokens;
  final double temperature;

  const AIModelConfig({
    required this.id,
    required this.name,
    required this.provider,
    required this.endpoint,
    required this.maxTokens,
    required this.temperature,
  });

  @override
  String toString() => '$name (${provider.displayName})';
}
