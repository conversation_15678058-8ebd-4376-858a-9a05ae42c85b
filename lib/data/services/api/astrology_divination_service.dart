import 'package:uuid/uuid.dart';

import '../../../core/utils/logger_utils.dart';
import '../../../shared/utils/geocoding_service.dart';
import '../../models/astrology/chart_data.dart';
import '../../models/astrology/chart_settings.dart';
import '../../models/astrology/chart_type.dart';
import '../../models/interpretation/divination_record.dart';
import '../../models/user/birth_data.dart';
import 'astrology_service.dart';
import 'divination_record_service.dart';

/// 占星卜卦服務
/// 專門處理占星卜卦（時刻盤）相關的業務邏輯
class AstrologyDivinationService {
  static final AstrologyDivinationService _instance = AstrologyDivinationService._internal();
  factory AstrologyDivinationService() => _instance;
  AstrologyDivinationService._internal();

  /// 執行占星卜卦分析
  /// 
  /// [questionTime] 提問時間
  /// [location] 提問地點
  /// [question] 問題內容
  /// 
  /// 返回計算完成的星盤數據
  static Future<ChartData> performDivination({
    required DateTime questionTime,
    required String location,
    required String question,
  }) async {
    try {
      logger.d('開始執行占星卜卦分析...');
      
      // 從地址獲取經緯度
      final coordinates = await GeocodingService.getCoordinatesFromAddress(location);
      final latitude = coordinates['latitude']!;
      final longitude = coordinates['longitude']!;

      // 創建卜卦時間的星盤數據
      final chartData = ChartData(
        chartType: ChartType.horary,
        primaryPerson: BirthData(
          id: 'horary_${questionTime.millisecondsSinceEpoch}',
          name: '占星卜卦',
          dateTime: questionTime,
          birthPlace: location,
          latitude: latitude,
          longitude: longitude,
          notes: question,
        ),
      );

      // 載入用戶星盤設定
      ChartSettings? chartSettings = await ChartSettings.loadFromPrefs();
      logger.d('已載入用戶星盤設定');

      // 計算行星位置、宮位和相位
      final calculatedChartData = await AstrologyService().calculateChartData(
        chartData,
        chartSettings: chartSettings
      );

      // 保存卜卦記錄
      await _saveHoraryRecord(question, location, questionTime, calculatedChartData);

      logger.d('占星卜卦分析完成');
      return calculatedChartData;
    } catch (e) {
      logger.e('占星卜卦分析時出錯: $e');
      rethrow;
    }
  }

  /// 保存占星卜卦記錄
  static Future<void> _saveHoraryRecord(
    String question,
    String location,
    DateTime timestamp,
    ChartData chartData,
  ) async {
    try {
      logger.d('開始保存占星卜卦記錄...');

      // 生成記錄ID
      final String recordId = const Uuid().v4();

      // 獲取卜卦結果文本
      final String resultText = await _generateHoraryResultText(chartData);
      logger.d('卜卦結果文本長度: ${resultText.length}');

      // 創建卜卦記錄
      final DivinationRecord record = DivinationRecord(
        id: recordId,
        question: question,
        location: location,
        timestamp: timestamp,
        type: 'horary',
        result: resultText,
        chartData: chartData.toJson(),
      );

      // 保存記錄
      await DivinationRecordService.saveRecord(record);
      logger.d('占星卜卦記錄已保存，ID: $recordId');
    } catch (e) {
      logger.e('保存占星卜卦記錄時出錯: $e');
      // 不拋出異常，避免影響主要功能
    }
  }

  /// 生成占星卜卦結果文本
  static Future<String> _generateHoraryResultText(ChartData chartData) async {
    try {
      final StringBuffer buffer = StringBuffer();
      
      // 基本資訊
      buffer.writeln('=== 占星卜卦結果 ===');
      buffer.writeln('卜卦時間: ${chartData.primaryPerson.dateTime}');
      buffer.writeln('卜卦地點: ${chartData.primaryPerson.birthPlace}');
      buffer.writeln('問題: ${chartData.primaryPerson.notes ?? '未記錄'}');
      buffer.writeln();

      // 行星位置
      if (chartData.planets != null && chartData.planets!.isNotEmpty) {
        buffer.writeln('=== 行星位置 ===');
        for (final planet in chartData.planets!) {
          buffer.writeln('${planet.name}: ${planet.sign} ${planet.longitude.toStringAsFixed(2)}°');
        }
        buffer.writeln();
      }

      // 宮位資訊
      if (chartData.houses != null) {
        buffer.writeln('=== 宮位資訊 ===');
        for (int i = 1; i < chartData.houses!.cusps.length; i++) {
          final houseCusp = chartData.houses!.cusps[i];
          final signIndex = (houseCusp / 30).floor();
          final signs = ['牡羊座', '金牛座', '雙子座', '巨蟹座', '獅子座', '處女座',
                        '天秤座', '天蠍座', '射手座', '摩羯座', '水瓶座', '雙魚座'];
          final signName = signs[signIndex % 12];
          final degree = houseCusp % 30;
          buffer.writeln('第$i宮: $signName ${degree.toStringAsFixed(2)}°');
        }
        buffer.writeln();
      }

      // 相位資訊
      if (chartData.aspects != null && chartData.aspects!.isNotEmpty) {
        buffer.writeln('=== 主要相位 ===');
        for (final aspect in chartData.aspects!) {
          buffer.writeln('${aspect.planet1.name} ${aspect.aspectType} ${aspect.planet2.name} (容許度: ${aspect.orb.toStringAsFixed(2)}°)');
        }
      }

      return buffer.toString();
    } catch (e) {
      logger.e('生成占星卜卦結果文本時出錯: $e');
      return '占星卜卦結果生成失敗: $e';
    }
  }

  /// 驗證卜卦參數
  static bool validateDivinationParams({
    required String question,
    required String location,
    required DateTime questionTime,
  }) {
    if (question.trim().isEmpty) {
      logger.w('問題不能為空');
      return false;
    }

    if (location.trim().isEmpty) {
      logger.w('地點不能為空');
      return false;
    }

    // 檢查時間是否合理（不能太久遠的未來）
    final now = DateTime.now();
    final maxFutureTime = now.add(const Duration(days: 365)); // 最多一年後
    
    if (questionTime.isAfter(maxFutureTime)) {
      logger.w('卜卦時間不能超過一年後');
      return false;
    }

    return true;
  }

  /// 獲取占星卜卦的建議時間
  /// 返回當前時間作為預設卜卦時間
  static DateTime getSuggestedDivinationTime() {
    return DateTime.now();
  }

  /// 獲取占星卜卦的說明文字
  static String getDivinationDescription() {
    return '占星卜卦（時刻盤）是西方占星學中的一種古老技術，用於回答特定問題。'
        '當您提出問題的那一刻，天空中的星象配置被認為與您的問題有著神秘的聯繫。\n\n'
        '請在下方輸入您的問題、提問時間和地點，然後點擊「開始卜卦」按鈕。'
        '系統將生成時刻盤，並直接顯示星盤資訊和相關解釋。';
  }

  /// 獲取占星卜卦的使用提示
  static List<String> getDivinationTips() {
    return [
      '卜卦前保持平靜的心態，專注於您的問題。',
      '問題應該明確具體，避免過於籠統或包含多個問題。',
      '提問時間應該是您真正想到這個問題的時刻。',
      '地點應該是您提問時所在的具體位置。',
      '同一個問題不宜在短時間內重複卜問，以免干擾結果。',
      '卜卦結果提供的是參考和啟示，最終決定仍應基於理性思考。',
    ];
  }
}
