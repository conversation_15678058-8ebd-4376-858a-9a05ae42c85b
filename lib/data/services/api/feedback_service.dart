import 'dart:io';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:uuid/uuid.dart';

import '../../../core/constants/firebase_collections.dart';
import '../../../core/utils/logger_utils.dart';
import '../../models/user/feedback_model.dart';
import 'auth_service.dart';
import 'log_management_service.dart';

/// 回饋服務
/// 處理用戶回饋的儲存、管理和查詢
class FeedbackService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseStorage _storage = FirebaseStorage.instance;
  static const String _collection = FirebaseCollections.userFeedback;
  static const Uuid _uuid = Uuid();

  /// 提交回饋
  static Future<bool> submitFeedback({
    required String content,
    required FeedbackType type,
    String? email,
    List<File>? attachments,
    bool includeLog = false,
  }) async {
    try {
      logger.i('開始提交回饋');

      // 獲取當前用戶
      final currentUser = AuthService.getCurrentUser();
      final userId = currentUser?.uid;

      // 獲取設備資訊
      final deviceInfo = await _getDeviceInfo();
      final appVersion = await _getAppVersion();
      final deviceId = await _getDeviceId();

      // 生成回饋ID
      final feedbackId = _uuid.v4();

      // 上傳附件
      List<String> attachmentUrls = [];
      if (attachments != null && attachments.isNotEmpty) {
        attachmentUrls = await _uploadAttachments(feedbackId, attachments);
      }

      // 上傳日誌文件
      String? logFileUrl;
      if (includeLog) {
        logFileUrl = await _uploadLogFile(feedbackId);
      }

      // 創建回饋模型
      final feedback = FeedbackModel(
        id: feedbackId,
        content: content,
        email: email,
        userId: userId,
        deviceId: deviceId,
        type: type,
        attachmentUrls: attachmentUrls,
        logFileUrl: logFileUrl,
        deviceInfo: deviceInfo,
        appVersion: appVersion,
      );

      // 儲存到 Firestore
      await _firestore
          .collection(_collection)
          .doc(feedbackId)
          .set(feedback.toFirestoreJson());

      logger.i('回饋提交成功: $feedbackId');
      return true;
    } catch (e) {
      logger.e('提交回饋失敗: $e');
      return false;
    }
  }

  /// 獲取用戶的回饋列表
  static Future<List<FeedbackModel>> getUserFeedbacks(String userId) async {
    try {
      final querySnapshot = await _firestore
          .collection(_collection)
          .where('userId', isEqualTo: userId)
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => FeedbackModel.fromJson({
                ...doc.data(),
                'id': doc.id,
              }))
          .toList();
    } catch (e) {
      logger.e('獲取用戶回饋失敗: $e');
      return [];
    }
  }

  /// 獲取所有回饋（管理員用）
  static Future<List<FeedbackModel>> getAllFeedbacks({
    int limit = 50,
    bool? isResolved,
    FeedbackType? type,
  }) async {
    try {
      Query query = _firestore
          .collection(_collection)
          .orderBy('createdAt', descending: true);

      if (isResolved != null) {
        query = query.where('isResolved', isEqualTo: isResolved);
      }

      if (type != null) {
        query = query.where('type', isEqualTo: type.name);
      }

      final querySnapshot = await query.limit(limit).get();

      return querySnapshot.docs
          .map((doc) => FeedbackModel.fromJson({
                ...doc.data() as Map<String, dynamic>,
                'id': doc.id,
              }))
          .toList();
    } catch (e) {
      logger.e('獲取所有回饋失敗: $e');
      return [];
    }
  }

  /// 標記回饋為已解決
  static Future<bool> markFeedbackAsResolved(String feedbackId) async {
    try {
      await _firestore
          .collection(_collection)
          .doc(feedbackId)
          .update({'isResolved': true});

      logger.i('回饋已標記為已解決: $feedbackId');
      return true;
    } catch (e) {
      logger.e('標記回饋為已解決失敗: $e');
      return false;
    }
  }

  /// 刪除回饋
  static Future<bool> deleteFeedback(String feedbackId) async {
    try {
      // 獲取回饋資料以刪除相關文件
      final doc = await _firestore.collection(_collection).doc(feedbackId).get();
      if (doc.exists) {
        final feedback = FeedbackModel.fromJson({
          ...doc.data()!,
          'id': doc.id,
        });

        // 刪除附件文件
        for (final url in feedback.attachmentUrls) {
          try {
            await _storage.refFromURL(url).delete();
          } catch (e) {
            logger.w('刪除附件失敗: $e');
          }
        }

        // 刪除日誌文件
        if (feedback.logFileUrl != null) {
          try {
            await _storage.refFromURL(feedback.logFileUrl!).delete();
          } catch (e) {
            logger.w('刪除日誌文件失敗: $e');
          }
        }
      }

      // 刪除 Firestore 文檔
      await _firestore.collection(_collection).doc(feedbackId).delete();

      logger.i('回饋已刪除: $feedbackId');
      return true;
    } catch (e) {
      logger.e('刪除回饋失敗: $e');
      return false;
    }
  }

  /// 上傳附件文件
  static Future<List<String>> _uploadAttachments(
      String feedbackId, List<File> attachments) async {
    final List<String> urls = [];

    for (int i = 0; i < attachments.length; i++) {
      try {
        final file = attachments[i];
        final fileName = 'attachment_${i}_${DateTime.now().millisecondsSinceEpoch}';
        final ref = _storage
            .ref()
            .child('feedback')
            .child(feedbackId)
            .child(fileName);

        final uploadTask = await ref.putFile(file);
        final downloadUrl = await uploadTask.ref.getDownloadURL();
        urls.add(downloadUrl);

        logger.d('附件上傳成功: $fileName');
      } catch (e) {
        logger.e('上傳附件失敗: $e');
      }
    }

    return urls;
  }

  /// 上傳日誌文件
  static Future<String?> _uploadLogFile(String feedbackId) async {
    try {
      // 獲取日誌文件
      final logFile = await LogManagementService.instance.exportLogsToFile();
      if (logFile == null) {
        logger.w('無法獲取日誌文件');
        return null;
      }

      final fileName = 'log_${DateTime.now().millisecondsSinceEpoch}.txt';
      final ref = _storage
          .ref()
          .child('feedback')
          .child(feedbackId)
          .child(fileName);

      final uploadTask = await ref.putFile(logFile);
      final downloadUrl = await uploadTask.ref.getDownloadURL();

      // 清理臨時文件
      try {
        await logFile.delete();
      } catch (e) {
        logger.w('清理臨時日誌文件失敗: $e');
      }

      logger.d('日誌文件上傳成功: $fileName');
      return downloadUrl;
    } catch (e) {
      logger.e('上傳日誌文件失敗: $e');
      return null;
    }
  }

  /// 獲取設備資訊
  static Future<Map<String, dynamic>> _getDeviceInfo() async {
    try {
      final deviceInfo = DeviceInfoPlugin();
      
      if (Platform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        return {
          'platform': 'Android',
          'model': androidInfo.model,
          'brand': androidInfo.brand,
          'version': androidInfo.version.release,
          'sdkInt': androidInfo.version.sdkInt,
        };
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfo.iosInfo;
        return {
          'platform': 'iOS',
          'model': iosInfo.model,
          'name': iosInfo.name,
          'systemVersion': iosInfo.systemVersion,
        };
      } else {
        return {'platform': Platform.operatingSystem};
      }
    } catch (e) {
      logger.e('獲取設備資訊失敗: $e');
      return {'platform': 'Unknown'};
    }
  }

  /// 獲取應用版本
  static Future<String> _getAppVersion() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      return '${packageInfo.version}+${packageInfo.buildNumber}';
    } catch (e) {
      logger.e('獲取應用版本失敗: $e');
      return 'Unknown';
    }
  }

  /// 獲取設備ID
  static Future<String> _getDeviceId() async {
    try {
      final deviceInfo = DeviceInfoPlugin();
      
      if (Platform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        return androidInfo.id;
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfo.iosInfo;
        return iosInfo.identifierForVendor ?? 'unknown_ios_device';
      } else {
        return 'unknown_device';
      }
    } catch (e) {
      logger.e('獲取設備ID失敗: $e');
      return 'unknown_device';
    }
  }
}
