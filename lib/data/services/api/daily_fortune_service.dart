import 'dart:math';

import '../../../core/utils/logger_utils.dart';
import '../../models/astrology/chart_data.dart';
import '../../models/astrology/chart_type.dart';
import '../../models/astrology/planet_position.dart';
import '../../models/astrology/zodiac_sign.dart';
import '../../models/interpretation/daily_fortune.dart';
import '../../models/user/birth_data.dart';
import 'astrology_service.dart';

/// 今日運勢服務
class DailyFortuneService {
  static final AstrologyService _astrologyService = AstrologyService();
  static final Random _random = Random();

  /// 獲取個人化今日運勢
  static Future<PersonalizedDailyFortune> getPersonalizedDailyFortune(
    BirthData birthData, {
    DateTime? targetDate,
  }) async {
    try {
      final date = targetDate ?? DateTime.now();
      logger.i('獲取個人化今日運勢: ${birthData.name}, 日期: $date');

      // 計算本命盤
      final natalChart = ChartData(
        chartType: ChartType.natal,
        primaryPerson: birthData,
      );
      final calculatedNatalChart = await _astrologyService.calculateChartData(natalChart);

      // 計算行運盤
      final transitChart = ChartData(
        chartType: ChartType.transit,
        primaryPerson: birthData,
        specificDate: date,
      );
      final calculatedTransitChart = await _astrologyService.calculateChartData(transitChart);

      // 分析個人運勢
      final personalFortune = await _analyzePersonalFortune(
        calculatedNatalChart,
        calculatedTransitChart,
        date,
      );

      // 獲取太陽星座運勢
      final sunSign = _getSunSign(calculatedNatalChart.planets);
      final sunSignFortune = await _getZodiacFortune(sunSign, date);

      // 獲取月亮星座運勢
      final moonSign = _getMoonSign(calculatedNatalChart.planets);
      final moonSignFortune = moonSign != null 
          ? await _getZodiacFortune(moonSign, date)
          : null;

      // 獲取上升星座運勢
      final risingSign = _getRisingSign(calculatedNatalChart.houses);
      final risingSignFortune = risingSign != null 
          ? await _getZodiacFortune(risingSign, date)
          : null;

      // 分析行運影響
      final transitInfluences = _analyzeTransitInfluences(
        calculatedNatalChart,
        calculatedTransitChart,
      );

      // 生成個人化建議
      final personalizedAdvice = _generatePersonalizedAdvice(
        calculatedNatalChart,
        calculatedTransitChart,
        personalFortune,
      );

      return PersonalizedDailyFortune(
        personalFortune: personalFortune,
        sunSignFortune: sunSignFortune,
        moonSignFortune: moonSignFortune,
        risingSignFortune: risingSignFortune,
        transitInfluences: transitInfluences,
        personalizedAdvice: personalizedAdvice,
      );
    } catch (e) {
      logger.e('獲取個人化今日運勢失敗: $e');
      // 返回默認運勢
      return _getDefaultPersonalizedFortune(birthData, targetDate ?? DateTime.now());
    }
  }

  /// 獲取星座今日運勢
  static Future<ZodiacDailyFortune> getZodiacDailyFortune(
    int zodiacId, {
    DateTime? targetDate,
  }) async {
    try {
      final date = targetDate ?? DateTime.now();
      final zodiacSign = ZodiacSign.fromId(zodiacId);
      
      return await _getZodiacFortune(zodiacSign, date);
    } catch (e) {
      logger.e('獲取星座今日運勢失敗: $e');
      return _getDefaultZodiacFortune(zodiacId, targetDate ?? DateTime.now());
    }
  }

  /// 獲取所有星座今日運勢
  static Future<List<ZodiacDailyFortune>> getAllZodiacDailyFortunes({
    DateTime? targetDate,
  }) async {
    final date = targetDate ?? DateTime.now();
    final fortunes = <ZodiacDailyFortune>[];

    for (int i = 1; i <= 12; i++) {
      try {
        final fortune = await getZodiacDailyFortune(i, targetDate: date);
        fortunes.add(fortune);
      } catch (e) {
        logger.e('獲取星座 $i 運勢失敗: $e');
        fortunes.add(_getDefaultZodiacFortune(i, date));
      }
    }

    return fortunes;
  }

  /// 分析個人運勢
  static Future<DailyFortune> _analyzePersonalFortune(
    ChartData natalChart,
    ChartData transitChart,
    DateTime date,
  ) async {
    // 分析行運相位
    final aspects = transitChart.aspects ?? [];
    final positiveAspects = aspects.where((aspect) =>
        aspect.aspectType == 'trine' ||
        aspect.aspectType == 'sextile').length;
    final negativeAspects = aspects.where((aspect) =>
        aspect.aspectType == 'square' ||
        aspect.aspectType == 'opposition').length;

    // 計算整體評分
    int overallScore = 3; // 基礎分數
    if (positiveAspects > negativeAspects) {
      overallScore = 4;
    } else if (negativeAspects > positiveAspects) {
      overallScore = 2;
    }

    // 隨機調整
    if (_random.nextDouble() > 0.7) {
      overallScore = (overallScore + 1).clamp(1, 5);
    }

    return DailyFortune(
      date: date,
      overallScore: overallScore,
      title: _generateFortuneTitle(overallScore),
      description: _generateFortuneDescription(overallScore, aspects),
      luckyColor: _generateLuckyColor(),
      luckyNumbers: _generateLuckyNumbers(),
      loveAspect: _generateFortuneAspect('love', overallScore),
      careerAspect: _generateFortuneAspect('career', overallScore),
      wealthAspect: _generateFortuneAspect('wealth', overallScore),
      healthAspect: _generateFortuneAspect('health', overallScore),
      planetaryInfluences: _generatePlanetaryInfluences(aspects),
      recommendations: _generateRecommendations(overallScore),
      warnings: _generateWarnings(overallScore),
    );
  }

  /// 獲取星座運勢
  static Future<ZodiacDailyFortune> _getZodiacFortune(
    ZodiacSign zodiacSign,
    DateTime date,
  ) async {
    // 基於星座特性生成運勢
    final baseScore = _getZodiacBaseScore(zodiacSign.id, date);
    
    final fortune = DailyFortune(
      date: date,
      overallScore: baseScore,
      title: _generateZodiacFortuneTitle(zodiacSign, baseScore),
      description: _generateZodiacFortuneDescription(zodiacSign, baseScore),
      luckyColor: _getZodiacLuckyColor(zodiacSign),
      luckyNumbers: _generateLuckyNumbers(),
      loveAspect: _generateFortuneAspect('love', baseScore),
      careerAspect: _generateFortuneAspect('career', baseScore),
      wealthAspect: _generateFortuneAspect('wealth', baseScore),
      healthAspect: _generateFortuneAspect('health', baseScore),
      planetaryInfluences: _generateZodiacPlanetaryInfluences(zodiacSign),
      recommendations: _generateZodiacRecommendations(zodiacSign, baseScore),
      warnings: _generateZodiacWarnings(zodiacSign, baseScore),
    );

    return ZodiacDailyFortune(
      zodiacId: zodiacSign.id,
      zodiacName: zodiacSign.name,
      zodiacSymbol: zodiacSign.symbol,
      fortune: fortune,
    );
  }

  /// 獲取太陽星座
  static ZodiacSign _getSunSign(List<PlanetPosition>? planets) {
    if (planets == null) return ZodiacSign.fromId(1);

    final sun = planets.firstWhere(
      (planet) => planet.id == 0, // 太陽
      orElse: () => planets.first,
    );

    return ZodiacSign.fromLongitude(sun.longitude);
  }

  /// 獲取月亮星座
  static ZodiacSign? _getMoonSign(List<PlanetPosition>? planets) {
    if (planets == null) return null;

    try {
      final moon = planets.firstWhere((planet) => planet.id == 1); // 月亮
      return ZodiacSign.fromLongitude(moon.longitude);
    } catch (e) {
      return null;
    }
  }

  /// 獲取上升星座
  static ZodiacSign? _getRisingSign(dynamic houses) {
    // 這裡需要根據實際的 houses 數據結構來實現
    // 暫時返回 null
    return null;
  }

  /// 生成運勢標題
  static String _generateFortuneTitle(int score) {
    switch (score) {
      case 5:
        return '星光璀璨的一天';
      case 4:
        return '充滿機會的時光';
      case 3:
        return '平穩前進的日子';
      case 2:
        return '需要謹慎的時刻';
      case 1:
        return '挑戰中成長';
      default:
        return '新的開始';
    }
  }

  /// 生成運勢描述
  static String _generateFortuneDescription(int score, List<dynamic> aspects) {
    final descriptions = {
      5: '今天的星象配置對您非常有利，是展現才華和追求目標的絕佳時機。',
      4: '今天整體運勢良好，適合推進重要計劃和建立新的人際關係。',
      3: '今天是平穩發展的一天，保持現有節奏，穩步前進即可。',
      2: '今天需要多加留意，避免衝動決定，以穩為主。',
      1: '今天可能面臨一些挑戰，但這也是學習和成長的機會。',
    };
    
    return descriptions[score] ?? descriptions[3]!;
  }

  /// 其他輔助方法...
  static String _generateLuckyColor() {
    final colors = ['金色', '紅色', '藍色', '綠色', '紫色', '橙色', '粉色'];
    return colors[_random.nextInt(colors.length)];
  }

  static List<int> _generateLuckyNumbers() {
    final numbers = <int>[];
    while (numbers.length < 3) {
      final number = _random.nextInt(99) + 1;
      if (!numbers.contains(number)) {
        numbers.add(number);
      }
    }
    return numbers..sort();
  }

  static FortuneAspect _generateFortuneAspect(String type, int baseScore) {
    final score = (baseScore + _random.nextInt(3) - 1).clamp(1, 5);
    
    final descriptions = {
      'love': {
        5: '愛情運勢極佳，單身者有機會遇到心儀對象',
        4: '感情生活和諧，適合表達愛意',
        3: '愛情運勢平穩，維持現狀即可',
        2: '感情需要多溝通，避免誤會',
        1: '愛情運勢較弱，需要耐心等待',
      },
      'career': {
        5: '事業運勢旺盛，工作表現出色',
        4: '工作順利，有晉升機會',
        3: '事業平穩發展，按部就班',
        2: '工作需要謹慎，避免出錯',
        1: '事業面臨挑戰，需要努力',
      },
      'wealth': {
        5: '財運亨通，投資有收穫',
        4: '財運不錯，收入穩定',
        3: '財運平平，量入為出',
        2: '理財需謹慎，避免損失',
        1: '財運較弱，節約為主',
      },
      'health': {
        5: '身體健康，精力充沛',
        4: '健康狀況良好，活力十足',
        3: '健康平穩，注意休息',
        2: '注意身體，避免過勞',
        1: '健康需關注，多休息',
      },
    };

    return FortuneAspect(
      score: score,
      description: descriptions[type]?[score] ?? '運勢平穩',
      advice: '保持積極心態，順應自然節奏',
    );
  }

  // 其他方法的實現...
  static List<String> _generatePlanetaryInfluences(List<dynamic> aspects) {
    return ['水星帶來溝通機會', '金星增強人際魅力', '火星提升行動力'];
  }

  static List<String> _generateRecommendations(int score) {
    return ['保持積極心態', '多與他人交流', '關注身體健康'];
  }

  static List<String> _generateWarnings(int score) {
    if (score <= 2) {
      return ['避免衝動決定', '注意人際關係', '謹慎理財'];
    }
    return [];
  }

  static int _getZodiacBaseScore(int zodiacId, DateTime date) {
    // 基於日期和星座生成基礎分數
    final dayOfYear = date.difference(DateTime(date.year, 1, 1)).inDays;
    final seed = zodiacId * 1000 + dayOfYear;
    final random = Random(seed);
    return random.nextInt(3) + 2; // 2-4 分
  }

  static String _generateZodiacFortuneTitle(ZodiacSign zodiac, int score) {
    return '${zodiac.name}今日運勢';
  }

  static String _generateZodiacFortuneDescription(ZodiacSign zodiac, int score) {
    return '${zodiac.name}的朋友今天運勢${score >= 4 ? '不錯' : score >= 3 ? '平穩' : '需要注意'}。';
  }

  static String _getZodiacLuckyColor(ZodiacSign zodiac) {
    final colors = {
      1: '紅色', 2: '綠色', 3: '黃色', 4: '銀色',
      5: '金色', 6: '藍色', 7: '粉色', 8: '深紅色',
      9: '紫色', 10: '棕色', 11: '天藍色', 12: '海藍色',
    };
    return colors[zodiac.id] ?? '白色';
  }

  static List<String> _generateZodiacPlanetaryInfluences(ZodiacSign zodiac) {
    return ['${zodiac.ruler}為您帶來正面能量'];
  }

  static List<String> _generateZodiacRecommendations(ZodiacSign zodiac, int score) {
    return ['發揮${zodiac.name}的優勢', '保持${zodiac.element}元素的平衡'];
  }

  static List<String> _generateZodiacWarnings(ZodiacSign zodiac, int score) {
    if (score <= 2) {
      return ['注意${zodiac.name}容易出現的問題'];
    }
    return [];
  }

  static List<String> _analyzeTransitInfluences(ChartData natal, ChartData transit) {
    return ['行運行星帶來新的機會', '注意行運相位的影響'];
  }

  static List<String> _generatePersonalizedAdvice(
    ChartData natal, ChartData transit, DailyFortune fortune) {
    return ['根據您的星盤配置，建議今天多關注人際關係'];
  }

  static PersonalizedDailyFortune _getDefaultPersonalizedFortune(
    BirthData birthData, DateTime date) {
    final defaultFortune = DailyFortune(
      date: date,
      overallScore: 3,
      title: '平穩的一天',
      description: '今天是平穩發展的一天，保持積極心態。',
      luckyColor: '藍色',
      luckyNumbers: [7, 14, 21],
      loveAspect: FortuneAspect(score: 3, description: '愛情運勢平穩', advice: '保持真誠'),
      careerAspect: FortuneAspect(score: 3, description: '事業穩定發展', advice: '專注工作'),
      wealthAspect: FortuneAspect(score: 3, description: '財運平平', advice: '理性消費'),
      healthAspect: FortuneAspect(score: 3, description: '健康狀況良好', advice: '注意休息'),
      planetaryInfluences: ['星象平穩'],
      recommendations: ['保持積極心態'],
      warnings: [],
    );

    return PersonalizedDailyFortune(
      personalFortune: defaultFortune,
      sunSignFortune: _getDefaultZodiacFortune(1, date),
      transitInfluences: [],
      personalizedAdvice: [],
    );
  }

  static ZodiacDailyFortune _getDefaultZodiacFortune(int zodiacId, DateTime date) {
    final zodiac = ZodiacSign.fromId(zodiacId);
    final fortune = DailyFortune(
      date: date,
      overallScore: 3,
      title: '${zodiac.name}今日運勢',
      description: '今天運勢平穩。',
      luckyColor: _getZodiacLuckyColor(zodiac),
      luckyNumbers: [1, 2, 3],
      loveAspect: FortuneAspect(score: 3, description: '愛情平穩', advice: '保持真誠'),
      careerAspect: FortuneAspect(score: 3, description: '事業穩定', advice: '專注工作'),
      wealthAspect: FortuneAspect(score: 3, description: '財運平平', advice: '理性消費'),
      healthAspect: FortuneAspect(score: 3, description: '健康良好', advice: '注意休息'),
      planetaryInfluences: [],
      recommendations: [],
      warnings: [],
    );

    return ZodiacDailyFortune(
      zodiacId: zodiac.id,
      zodiacName: zodiac.name,
      zodiacSymbol: zodiac.symbol,
      fortune: fortune,
    );
  }
}
