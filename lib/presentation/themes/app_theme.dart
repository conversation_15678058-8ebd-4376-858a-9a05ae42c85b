import 'package:flutter/material.dart';

class AppColors {
  // ======== 初心者模式 (Starlight) 主題色彩 ========
  // 主要色彩
  static const Color starlightPrimary = Color(0xFFF5A623); // 溫暖的琥珀金
  static const Color starlightSecondary = Color(0xFFFFB84D); // 柔和的珊瑚色
  static const Color starlightAccent = Color(0xFFFFB84D); // 明亮的橙黃色
  static const Color starlightBackground = Color(0xFFFFF8DC); // 淺玉米絲色背景
  static const Color starlightSurface = Color(0xFFFFF3E0); // 淺橙色表面
  static const Color starlightCard = Color(0xFFFFFBF0); // 淺米色卡片

  // 文字色彩
  static const Color starlightTextPrimary = Color(0xFF5D4037); // 深棕色文字
  static const Color starlightTextSecondary = Color(0xFF8D6E63); // 中棕色文字
  static const Color starlightTextHint = Color(0xFFBCAAA4); // 淺棕色提示文字

  // 功能色彩
  static const Color starlightSuccess = Color(0xFF66BB6A); // 柔和的成功綠
  static const Color starlightInfo = Color(0xFF4FC3F7); // 柔和的信息藍
  static const Color starlightWarning = Color(0xFFFFB74D); // 柔和的警告橙
  static const Color starlightError = Color(0xFFEF9A9A); // 柔和的錯誤紅

  // 邊框色彩
  static const Color starlightBorder = Color(0xFFE8E8E8); // 初心者模式邊框色

  // ======== 占星師模式 (Starmaster) 主題色彩 ========
  // 主要色彩
  static const Color starmasterPrimary = Color(0xFF3F51B5); // 皇家靛藍
  static const Color starmasterSecondary = Color(0xFF303F9F); // 深靛藍
  static const Color starmasterAccent = Color(0xFF7986CB); // 輕靛藍
  static const Color starmasterBackground = Color(0xFFE6F0FA); // 淺天藍色背景
  static const Color starmasterSurface = Color(0xFFECEFF1); // 淺藍灰色表面
  static const Color starmasterCard = Color(0xFFFFF3E0); // 淺藍白色卡片

  // 文字色彩
  static const Color starmasterTextPrimary = Color(0xFF263238); // 深藍灰色文字
  static const Color starmasterTextSecondary = Color(0xFF546E7A); // 中藍灰色文字
  static const Color starmasterTextHint = Color(0xFF90A4AE); // 淺藍灰色提示文字

  // 功能色彩
  static const Color starmasterSuccess = Color(0xFF4CAF50); // 專業的成功綠
  static const Color starmasterInfo = Color(0xFF2196F3); // 專業的信息藍
  static const Color starmasterWarning = Color(0xFFFFA000); // 專業的警告橙
  static const Color starmasterError = Color(0xFFE53935); // 專業的錯誤紅

  // 邊框色彩
  static const Color starmasterBorder = Color(0xFFE0E0E0); // 專業模式邊框色

  // ======== 設定相關專用主題色彩 (白灰黑極簡風) ========
  // 主要色彩
  static const Color settingsPrimary = Color(0xFF3F51B5); // 深灰黑 - 主要標題、圖示
  static const Color settingsSecondary = Color(0xFF7986CB); // 深灰 - 次要項目
  static const Color settingsAccent = Color(0xFFFFF8DC); // 中灰 - 滑桿、開關等互動元件
  static const Color settingsBackground = Color(0xFFF0F4FA); // 幾近純白背景
  static const Color settingsSurface = Color(0xFFF9F9F9); // 灰白色表面區塊
  static const Color settingsCard = Color(0xFFF9F9F9); // 純白卡片背景

  // 文字色彩
  static const Color settingsTextPrimary = Color(0xFF1A1A1A); // 幾近黑的主文字
  static const Color settingsTextSecondary = Color(0xFF555555); // 中深灰次文字
  static const Color settingsTextHint = Color(0xFF9E9E9E); // 淺灰提示/說明文字

  // 功能色彩（保留一點區分度，但不跳色）
  static const Color settingsSuccess = Color(0xFF4CAF50); // 保留沉穩綠色
  static const Color settingsInfo = Color(0xFF3F51B5); // 保留中藍（資訊/導覽指示）
  static const Color settingsWarning = Color(0xFF757575); // 使用中灰替代高彩警告
  static const Color settingsError = Color(0xFFD32F2F); // 深紅色錯誤提示（對比清楚）

  // ======== Starlight 模式設定頁主題色彩 ========
  static const Color starlightSettingsPrimary = Color(0xFF5D4037); // 深棕 - 與主文字一致
  static const Color starlightSettingsSecondary = Color(0xFF8D6E63); // 中棕
  static const Color starlightSettingsAccent = Color(0xFFFFB84D); // 主模式強調色
  static const Color starlightSettingsBackground = Color(0xFFFFFBEA); // 淡暖米黃背景
  static const Color starlightSettingsSurface = Color(0xFFFFF6E6); // 淺橙米色表面
  static const Color starlightSettingsCard = Color(0xFFFFFDF5); // 幾近白的暖調卡片

  static const Color starlightSettingsTextPrimary = Color(0xFF5D4037); // 深棕文字
  static const Color starlightSettingsTextSecondary = Color(0xFF8D6E63); // 中棕文字
  static const Color starlightSettingsTextHint = Color(0xFFBCAAA4); // 淺棕提示

  static const Color starlightSettingsSuccess = Color(0xFF66BB6A);
  static const Color starlightSettingsInfo = Color(0xFF4FC3F7);
  static const Color starlightSettingsWarning = Color(0xFFFFB74D);
  static const Color starlightSettingsError = Color(0xFFEF9A9A);

  // ======== Starmaster 模式設定頁主題色彩 ========
  static const Color starmasterSettingsPrimary = Color(0xFF263238); // 深藍灰
  static const Color starmasterSettingsSecondary = Color(0xFF546E7A); // 中藍灰
  static const Color starmasterSettingsAccent = Color(0xFF7986CB); // 輕靛藍
  static const Color starmasterSettingsBackground = Color(0xFFF0F4FA); // 極淡天藍背景
  static const Color starmasterSettingsSurface = Color(0xFFE9EFF3); // 冷白表面
  static const Color starmasterSettingsCard = Color(0xFFFFFFFF); // 純白卡片

  static const Color starmasterSettingsTextPrimary = Color(0xFF263238); // 深藍灰文字
  static const Color starmasterSettingsTextSecondary = Color(0xFF546E7A); // 中藍灰文字
  static const Color starmasterSettingsTextHint = Color(0xFF90A4AE); // 淺藍灰提示

  static const Color starmasterSettingsSuccess = Color(0xFF4CAF50);
  static const Color starmasterSettingsInfo = Color(0xFF2196F3);
  static const Color starmasterSettingsWarning = Color(0xFFFFA000);
  static const Color starmasterSettingsError = Color(0xFFE53935);


  // ======== 保留原有顏色以維持兼容性 ========
  // 主要顏色
  static const Color royalIndigo = starmasterPrimary; // 主色：皇家靛藍
  static const Color solarAmber = starlightPrimary; // 點綴：流光黃
  static const Color indigoSurface = starmasterSecondary; // 較深靛藍，用於 AppBar 或底部
  static const Color indigoLight = starmasterAccent; // 輕靛藍，用於強調元素

  static const Color veryLightGray = Color(0xFFF8F9FA);
  static const Color pastelSkyBlue = starmasterBackground;
  static const Color lightCornsilk = starlightBackground;

  static const Color darkIndigo = Color(0xFF092B42); // 深靛藍
  static const Color steelBlue = Color(0xFF105186); // 鋼鐵藍（接近藍鋼色調）
  static const Color paleGray = Color(0xFFF0F2F3); // 淡灰白
  static const Color goldenAmber = Color(0xFFFDB338); // 琥珀金黃
  static const Color charcoalBlack = Color(0xFF171C1D); // 木炭黑（極深灰）

  static const Color denimIndigo = Color(0xFF2C5B82); // 丹寧靛藍，偏牛仔藍的深色
  static const Color slateBlue = Color(0xFF367098); // 石板藍，略帶灰感的藍色
  static const Color mistGray = Color(0xFFD4D7D1); // 霧灰，柔和中性灰
  static const Color iceBlue = Color(0xFF79BBCF); // 冰藍，帶點亮度與冷感的藍綠
  static const Color royalBlue = Color(0xFF183AAF); // 皇家藍，鮮明濃郁的藍色

  // 輔助顏色
  static const Color paleAmber = Color(0xFFFFE9B3); // 柔和黃，背景可用
  static const Color softGray = settingsSurface; // 卡片或背景底色
  static const Color textDark = settingsTextPrimary; // 深色文字
  static const Color textMedium = settingsTextSecondary; // 中灰色文字
  static const Color textLight = settingsTextHint; // 淺灰色文字

  // 現代化文字顏色
  static const Color textPrimary = settingsTextPrimary; // 主要文字顏色
  static const Color textSecondary = settingsTextSecondary; // 次要文字顏色

  // 功能顏色
  static const Color success = settingsSuccess; // 成功綠
  static const Color successGreen = settingsSuccess; // 成功綠（別名）
  static const Color warning = settingsWarning; // 警告橙
  static const Color error = settingsError; // 錯誤紅
  static const Color info = settingsInfo; // 信息藍

  // 擴展顏色
  static const Color cosmicPurple = Color(0xFF8B5CF6); // 宇宙紫
  static const Color earthGreen = Color(0xFF10B981); // 大地綠
  static const Color lunarSilver = Color(0xFF94A3B8); // 月亮銀
  static const Color marsRed = Color(0xFFEF4444); // 火星紅

  // 背景與卡片
  static const Color cardBackground = settingsCard; // 卡片背景
  static const Color scaffoldBackground = settingsBackground; // 主背景色

  // 經典主題顏色（黑白灰配色）
  static const Color classicBlack = Color(0xFF000000); // 純黑
  static const Color classicDarkGray = Color(0xFF424242); // 深灰
  static const Color classicMediumGray = Color(0xFF757575); // 中灰
  static const Color classicLightGray = Color(0xFFBDBDBD); // 淺灰
  static const Color classicVeryLightGray = Color(0xFFE0E0E0); // 極淺灰
  static const Color classicWhite = Color(0xFFFFFFFF); // 純白
}

class AstrealAppTheme {
  static ThemeData get lightTheme {
    return ThemeData(
      brightness: Brightness.light,
      fontFamily: 'NotoSansTC',
      // 支援中文的字型
      scaffoldBackgroundColor: AppColors.scaffoldBackground,
      primaryColor: AppColors.royalIndigo,
      useMaterial3: true,
      // 啟用 Material 3
      colorScheme: const ColorScheme.light(
        primary: AppColors.royalIndigo,
        secondary: AppColors.solarAmber,
        tertiary: AppColors.indigoLight,
        background: AppColors.scaffoldBackground,
        surface: AppColors.softGray,
        onPrimary: Colors.white,
        onSecondary: Colors.black,
        error: AppColors.error,
      ),
      appBarTheme: const AppBarTheme(
        backgroundColor: AppColors.indigoSurface,
        foregroundColor: Colors.white,
        elevation: 2,
        centerTitle: false,
        titleTextStyle: TextStyle(
          color: Colors.white,
          fontSize: 20,
          fontWeight: FontWeight.bold,
          letterSpacing: 0.5,
        ),
      ),
      floatingActionButtonTheme: const FloatingActionButtonThemeData(
        backgroundColor: AppColors.solarAmber,
        foregroundColor: Colors.white,
        elevation: 4,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(16)),
        ),
      ),
      // 添加 NavigationBar 主題
      navigationBarTheme: NavigationBarThemeData(
        backgroundColor: Colors.white,
        indicatorColor: AppColors.royalIndigo.withValues(alpha: 0.1),
        labelTextStyle: MaterialStateProperty.resolveWith((states) {
          if (states.contains(MaterialState.selected)) {
            return const TextStyle(
              color: AppColors.royalIndigo,
              fontSize: 12,
              fontWeight: FontWeight.bold,
            );
          }
          return const TextStyle(
            color: AppColors.textMedium,
            fontSize: 12,
          );
        }),
        iconTheme: MaterialStateProperty.resolveWith((states) {
          if (states.contains(MaterialState.selected)) {
            return const IconThemeData(
              color: AppColors.royalIndigo,
              size: 24,
            );
          }
          return const IconThemeData(
            color: AppColors.textMedium,
            size: 24,
          );
        }),
        elevation: 2,
        height: 65,
        shadowColor: Colors.black.withValues(alpha: 0.1),
      ),
      cardTheme: CardThemeData(
        color: AppColors.cardBackground,
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        shadowColor: Colors.black.withValues(alpha: 0.1),
      ),
      textTheme: const TextTheme(
        displayLarge: TextStyle(
            color: AppColors.textDark,
            fontSize: 26,
            fontWeight: FontWeight.bold),
        displayMedium: TextStyle(
            color: AppColors.textDark,
            fontSize: 22,
            fontWeight: FontWeight.bold),
        displaySmall: TextStyle(
            color: AppColors.textDark,
            fontSize: 18,
            fontWeight: FontWeight.bold),
        bodyLarge: TextStyle(color: AppColors.textDark, fontSize: 16),
        bodyMedium: TextStyle(color: AppColors.textDark, fontSize: 14),
        bodySmall: TextStyle(color: AppColors.textMedium, fontSize: 12),
        titleLarge: TextStyle(
            color: AppColors.royalIndigo,
            fontSize: 20,
            fontWeight: FontWeight.bold),
        titleMedium: TextStyle(
            color: AppColors.royalIndigo,
            fontSize: 18,
            fontWeight: FontWeight.bold),
        titleSmall: TextStyle(
            color: AppColors.royalIndigo,
            fontSize: 16,
            fontWeight: FontWeight.bold),
      ),
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: Colors.white,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: AppColors.textLight),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: AppColors.textLight),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: AppColors.royalIndigo, width: 2),
        ),
        contentPadding:
            const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.royalIndigo,
          foregroundColor: Colors.white,
          elevation: 2,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          textStyle: const TextStyle(fontWeight: FontWeight.bold),
        ),
      ),
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: AppColors.royalIndigo,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          textStyle: const TextStyle(fontWeight: FontWeight.bold),
        ),
      ),
    );
  }

  /// Starlight 模式主題（初心者）- 溫暖友好的配色
  static ThemeData get starlightTheme {
    return ThemeData(
      brightness: Brightness.light,
      fontFamily: 'NotoSansTC',
      scaffoldBackgroundColor: AppColors.starlightBackground,
      primaryColor: AppColors.starlightPrimary,
      useMaterial3: true,
      colorScheme: const ColorScheme.light(
        primary: AppColors.starlightPrimary,
        secondary: AppColors.starlightSecondary,
        tertiary: AppColors.starlightAccent,
        background: AppColors.starlightBackground,
        surface: AppColors.starlightSurface,
        onPrimary: Colors.white,
        onSecondary: Colors.white,
        error: AppColors.starlightError,
      ),
      appBarTheme: starlightAppBarTheme,
      cardTheme: CardThemeData(
        color: AppColors.starlightCard,
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        shadowColor: Colors.black.withValues(alpha: 0.1),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.starlightPrimary,
          foregroundColor: Colors.white,
          elevation: 2,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          textStyle: const TextStyle(fontWeight: FontWeight.bold),
        ),
      ),
      textTheme: const TextTheme(
        displayLarge: TextStyle(
          color: AppColors.starlightTextPrimary,
          fontSize: 26,
          fontWeight: FontWeight.bold,
        ),
        bodyLarge: TextStyle(
          color: AppColors.starlightTextPrimary,
          fontSize: 16,
        ),
        bodyMedium: TextStyle(
          color: AppColors.starlightTextSecondary,
          fontSize: 14,
        ),
      ),
    );
  }

  /// Starmaster 模式主題（專業）- 專業精確的配色
  static ThemeData get starmasterTheme {
    return ThemeData(
      brightness: Brightness.light,
      fontFamily: 'NotoSansTC',
      scaffoldBackgroundColor: AppColors.starmasterBackground,
      primaryColor: AppColors.starmasterPrimary,
      useMaterial3: true,
      colorScheme: const ColorScheme.light(
        primary: AppColors.starmasterPrimary,
        secondary: AppColors.starmasterSecondary,
        tertiary: AppColors.starmasterAccent,
        background: AppColors.starmasterBackground,
        surface: AppColors.starmasterSurface,
        onPrimary: Colors.white,
        onSecondary: Colors.white,
        error: AppColors.starmasterError,
      ),
      appBarTheme: starmasterAppBarTheme,
      cardTheme: CardThemeData(
        color: AppColors.starmasterCard,
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        shadowColor: Colors.black.withValues(alpha: 0.1),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.starmasterPrimary,
          foregroundColor: Colors.white,
          elevation: 2,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          textStyle: const TextStyle(fontWeight: FontWeight.bold),
        ),
      ),
      textTheme: const TextTheme(
        displayLarge: TextStyle(
          color: AppColors.starmasterTextPrimary,
          fontSize: 26,
          fontWeight: FontWeight.bold,
        ),
        bodyLarge: TextStyle(
          color: AppColors.starmasterTextPrimary,
          fontSize: 16,
        ),
        bodyMedium: TextStyle(
          color: AppColors.starmasterTextSecondary,
          fontSize: 14,
        ),
      ),
    );
  }

  /// Settings 模式主題（設定相關）- 中性功能性的配色
  static ThemeData get settingsTheme {
    return ThemeData(
      brightness: Brightness.light,
      fontFamily: 'NotoSansTC',
      scaffoldBackgroundColor: AppColors.settingsBackground,
      primaryColor: AppColors.settingsPrimary,
      useMaterial3: true,
      colorScheme: const ColorScheme.light(
        primary: AppColors.settingsPrimary,
        secondary: AppColors.settingsSecondary,
        tertiary: AppColors.settingsAccent,
        background: AppColors.settingsBackground,
        surface: AppColors.settingsSurface,
        onPrimary: Colors.white,
        onSecondary: Colors.white,
        error: AppColors.settingsError,
      ),
      appBarTheme: settingsAppBarTheme,
      cardTheme: CardThemeData(
        color: AppColors.settingsCard,
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        shadowColor: Colors.black.withValues(alpha: 0.1),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.settingsPrimary,
          foregroundColor: Colors.white,
          elevation: 2,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          textStyle: const TextStyle(fontWeight: FontWeight.bold),
        ),
      ),
      textTheme: const TextTheme(
        displayLarge: TextStyle(
          color: AppColors.settingsTextPrimary,
          fontSize: 26,
          fontWeight: FontWeight.bold,
        ),
        bodyLarge: TextStyle(
          color: AppColors.settingsTextPrimary,
          fontSize: 16,
        ),
        bodyMedium: TextStyle(
          color: AppColors.settingsTextSecondary,
          fontSize: 14,
        ),
      ),
    );
  }

  /// Starlight 模式 AppBar 主題（初心者）
  static AppBarTheme get starlightAppBarTheme {
    return const AppBarTheme(
      backgroundColor: AppColors.starlightPrimary,
      foregroundColor: Colors.white,
      elevation: 2,
      centerTitle: true,
      titleTextStyle: TextStyle(
        color: Colors.white,
        fontSize: 20,
        fontWeight: FontWeight.bold,
      ),
      iconTheme: IconThemeData(
        color: Colors.white,
      ),
      actionsIconTheme: IconThemeData(
        color: Colors.white,
      ),
    );
  }

  /// Starmaster 模式 AppBar 主題（專業）
  static AppBarTheme get starmasterAppBarTheme {
    return const AppBarTheme(
      backgroundColor: AppColors.starmasterPrimary,
      foregroundColor: Colors.white,
      elevation: 2,
      centerTitle: true,
      titleTextStyle: TextStyle(
        color: Colors.white,
        fontSize: 20,
        fontWeight: FontWeight.bold,
      ),
      iconTheme: IconThemeData(
        color: Colors.white,
      ),
      actionsIconTheme: IconThemeData(
        color: Colors.white,
      ),
    );
  }

  /// Settings 模式 AppBar 主題（設定相關）
  static AppBarTheme get settingsAppBarTheme {
    return const AppBarTheme(
      backgroundColor: AppColors.settingsPrimary,
      foregroundColor: Colors.white,
      elevation: 2,
      centerTitle: true,
      titleTextStyle: TextStyle(
        color: Colors.white,
        fontSize: 20,
        fontWeight: FontWeight.bold,
      ),
      iconTheme: IconThemeData(
        color: Colors.white,
      ),
      actionsIconTheme: IconThemeData(
        color: Colors.white,
      ),
    );
  }
}

/// 主題管理器 - 提供便捷的主題選擇方法
class ThemeManager {
  /// 根據頁面類型獲取對應的主題
  static ThemeData getThemeForPage(PageType pageType) {
    switch (pageType) {
      case PageType.starlight:
        return AstrealAppTheme.starlightTheme;
      case PageType.starmaster:
        return AstrealAppTheme.starmasterTheme;
      case PageType.settings:
        return AstrealAppTheme.settingsTheme;
      case PageType.general:
        return AstrealAppTheme.lightTheme;
    }
  }

  /// 根據頁面類型獲取對應的主要顏色
  static Color getPrimaryColorForPage(PageType pageType) {
    switch (pageType) {
      case PageType.starlight:
        return AppColors.starlightPrimary;
      case PageType.starmaster:
        return AppColors.starmasterPrimary;
      case PageType.settings:
        return AppColors.settingsPrimary;
      case PageType.general:
        return AppColors.royalIndigo;
    }
  }

  /// 根據頁面類型獲取對應的背景顏色
  static Color getBackgroundColorForPage(PageType pageType) {
    switch (pageType) {
      case PageType.starlight:
        return AppColors.starlightBackground;
      case PageType.starmaster:
        return AppColors.starmasterBackground;
      case PageType.settings:
        return AppColors.settingsBackground;
      case PageType.general:
        return AppColors.scaffoldBackground;
    }
  }

  /// 根據頁面類型獲取對應的卡片顏色
  static Color getCardColorForPage(PageType pageType) {
    switch (pageType) {
      case PageType.starlight:
        return AppColors.starlightCard;
      case PageType.starmaster:
        return AppColors.starmasterCard;
      case PageType.settings:
        return AppColors.settingsCard;
      case PageType.general:
        return AppColors.cardBackground;
    }
  }

  /// 根據頁面類型獲取對應的表面顏色
  static Color getSurfaceColorForPage(PageType pageType) {
    switch (pageType) {
      case PageType.starlight:
        return AppColors.starlightSurface;
      case PageType.starmaster:
        return AppColors.starmasterSurface;
      case PageType.settings:
        return AppColors.settingsSurface;
      case PageType.general:
        return AppColors.softGray;
    }
  }

  /// 根據頁面類型獲取對應的強調顏色
  static Color getAccentColorForPage(PageType pageType) {
    switch (pageType) {
      case PageType.starlight:
        return AppColors.starlightAccent;
      case PageType.starmaster:
        return AppColors.starmasterAccent;
      case PageType.settings:
        return AppColors.settingsAccent;
      case PageType.general:
        return AppColors.indigoLight;
    }
  }

  /// 根據頁面類型獲取對應的文字顏色
  static Color getTextColorForPage(PageType pageType,
      {bool isSecondary = false}) {
    switch (pageType) {
      case PageType.starlight:
        return isSecondary
            ? AppColors.starlightTextSecondary
            : AppColors.starlightTextPrimary;
      case PageType.starmaster:
        return isSecondary
            ? AppColors.starmasterTextSecondary
            : AppColors.starmasterTextPrimary;
      case PageType.settings:
        return isSecondary
            ? AppColors.settingsTextSecondary
            : AppColors.settingsTextPrimary;
      case PageType.general:
        return isSecondary ? AppColors.textSecondary : AppColors.textPrimary;
    }
  }

  /// 根據頁面類型獲取對應的功能顏色
  static Color getFunctionalColorForPage(
      PageType pageType, FunctionalColorType colorType) {
    switch (pageType) {
      case PageType.starlight:
        switch (colorType) {
          case FunctionalColorType.success:
            return AppColors.starlightSuccess;
          case FunctionalColorType.info:
            return AppColors.starlightInfo;
          case FunctionalColorType.warning:
            return AppColors.starlightWarning;
          case FunctionalColorType.error:
            return AppColors.starlightError;
        }
      case PageType.starmaster:
        switch (colorType) {
          case FunctionalColorType.success:
            return AppColors.starmasterSuccess;
          case FunctionalColorType.info:
            return AppColors.starmasterInfo;
          case FunctionalColorType.warning:
            return AppColors.starmasterWarning;
          case FunctionalColorType.error:
            return AppColors.starmasterError;
        }
      case PageType.settings:
        switch (colorType) {
          case FunctionalColorType.success:
            return AppColors.settingsSuccess;
          case FunctionalColorType.info:
            return AppColors.settingsInfo;
          case FunctionalColorType.warning:
            return AppColors.settingsWarning;
          case FunctionalColorType.error:
            return AppColors.settingsError;
        }
      case PageType.general:
        switch (colorType) {
          case FunctionalColorType.success:
            return AppColors.success;
          case FunctionalColorType.info:
            return AppColors.info;
          case FunctionalColorType.warning:
            return AppColors.warning;
          case FunctionalColorType.error:
            return AppColors.error;
        }
    }
  }
}

/// 頁面類型枚舉
enum PageType {
  starlight, // 初心者模式頁面
  starmaster, // 占星師模式頁面
  settings, // 設定相關頁面
  general, // 一般頁面
}

/// 功能顏色類型枚舉
enum FunctionalColorType {
  success, // 成功
  info, // 信息
  warning, // 警告
  error, // 錯誤
}
