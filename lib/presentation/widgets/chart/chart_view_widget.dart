import 'package:astreal/presentation/pages/settings/chart_display_settings_page.dart';
import 'package:astreal/shared/widgets/chart_title_widget.dart';
import 'package:astreal/shared/widgets/chart_type_quick_selector.dart';
import 'package:astreal/shared/widgets/zoomable_chart_widget.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../astreal.dart';
import '../../../presentation/pages/term_ruler_stock_analysis_page.dart';
import '../../../shared/widgets/draggable/draggable_planet_detail_card.dart';
import '../../../shared/widgets/draggable/draggable_term_ruler_progression_card.dart';
import '../../../shared/widgets/painter/chart_painter.dart';
import '../../../shared/widgets/painter/dual_chart_painter.dart';
import '../../../shared/widgets/painter/firdaria_chart_painter.dart';
import '../../../shared/widgets/person_info_widget.dart';
import '../../../shared/widgets/time_adjustment_widget.dart';
import '../../pages/main/settings_page.dart';
import '../../pages/profection_timeline_page.dart';

class ChartViewWidget extends StatefulWidget {
  final ChartViewModel viewModel;

  const ChartViewWidget({
    Key? key,
    required this.viewModel,
  }) : super(key: key);

  @override
  State<ChartViewWidget> createState() => _ChartViewWidgetState();
}

class _ChartViewWidgetState extends State<ChartViewWidget> {
  // 用於顯示行星詳情卡片的 OverlayEntry
  OverlayEntry? _overlayEntry;

  // 緩存 CustomPainter 實例
  CustomPainter? _cachedPainter;

  // 滾動控制器，用於監聽滾動位置
  final ScrollController _scrollController = ScrollController();

  // 交換按鈕的位置偏移
  double _swapButtonOffset = 0.0;

  /// 加載法達盤數據
  Future<void> _loadFirdariaData() async {
    try {
      // 計算法達盤數據
      await widget.viewModel.calculateFirdaria();

      // 數據加載完成後滾動到當前週期
      if (mounted) {
        // _getChartPainter();
      }
    } catch (e) {
      print('加載法達盤數據時出錯: $e');
    }
  }

  @override
  void didUpdateWidget(ChartViewWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    // 如果圖表類型發生變化，清除緩存的 CustomPainter 實例
    if (oldWidget.viewModel.chartType != widget.viewModel.chartType ||
        oldWidget.viewModel.chartData != widget.viewModel.chartData) {
      print('didUpdateWidget: 清除緩存的 CustomPainter 實例');
      _cachedPainter = null;
    }

    if (widget.viewModel.chartType == ChartType.firdaria) {
      // 檢查是否已經有法達盤數據，如果沒有則計算
      if (widget.viewModel.firdariaData == null ||
          widget.viewModel.firdariaData!.isEmpty) {
        _loadFirdariaData();
      }
    }
  }

  @override
  void initState() {
    super.initState();
    // 監聽滾動位置變化
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    // 確保在 widget 銷毀時移除 overlay
    _removeOverlay();
    // 清理滾動控制器
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  /// 滾動監聽器
  void _onScroll() {
    if (mounted) {
      setState(() {
        _swapButtonOffset = _scrollController.offset;
      });
    }
  }

  // 移除 overlay
  void _removeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  /// 計算界主星相位
  List<AspectInfo> _calculateTermRulerAspects(TermRulerTimelineItem term) {
    final aspects = <AspectInfo>[];
    final chartData = widget.viewModel.chartData;

    if (chartData.planets?.isEmpty ?? true) return aspects;

    // 找到界主星行星
    final termRulerPlanet = chartData.planets!.firstWhere(
      (planet) => planet.name == term.termRulerName,
      orElse: () => chartData.planets!.first,
    );

    // 計算與其他行星的相位
    for (final planet in chartData.planets!) {
      if (planet.name == termRulerPlanet.name) continue;

      final orb = _calculateOrb(termRulerPlanet.longitude, planet.longitude);
      final aspectType = _getAspectType(orb);

      if (aspectType != null) {
        final aspectAngle = _getAspectAngle(aspectType);
        aspects.add(AspectInfo(
          planet1: termRulerPlanet,
          planet2: planet,
          angle: aspectAngle.toInt(),
          orb: orb,
          aspectType: aspectType,
          symbol: _getAspectSymbol(aspectType),
          shortZh: _getAspectSymbol(aspectType),
        ));
      }
    }

    return aspects;
  }

  /// 計算容許度
  double _calculateOrb(double longitude1, double longitude2) {
    double diff = (longitude1 - longitude2).abs();
    if (diff > 180) diff = 360 - diff;
    return diff;
  }

  /// 獲取相位類型
  String? _getAspectType(double orb) {
    const aspectOrbs = {
      'conjunction': 8.0,
      'opposition': 8.0,
      'trine': 6.0,
      'square': 6.0,
      'sextile': 4.0,
    };

    for (final entry in aspectOrbs.entries) {
      final expectedAngle = _getAspectAngle(entry.key);
      if ((orb - expectedAngle).abs() <= entry.value) {
        return entry.key;
      }
    }
    return null;
  }

  /// 獲取相位角度
  double _getAspectAngle(String aspectType) {
    switch (aspectType) {
      case 'conjunction': return 0;
      case 'opposition': return 180;
      case 'trine': return 120;
      case 'square': return 90;
      case 'sextile': return 60;
      default: return 0;
    }
  }

  /// 獲取相位符號
  String _getAspectSymbol(String aspectType) {
    switch (aspectType) {
      case 'conjunction': return '☌';
      case 'opposition': return '☍';
      case 'trine': return '△';
      case 'square': return '□';
      case 'sextile': return '⚹';
      default: return '';
    }
  }

  /// 顯示界主星配置法卡片（使用 Overlay）
  void _showTermRulerProgressionCard(
      BuildContext context, TermRulerTimelineResult result) {
    try {
      // 移除現有的 overlay（如果有的話）
      _removeOverlay();

      // 計算初始位置（螢幕中央偏右上）
      final screenSize = MediaQuery.of(context).size;
      final left = screenSize.width * 0.1;
      final top = screenSize.height * 0.1;

      // 創建並插入 overlay
      _overlayEntry = OverlayEntry(
        builder: (context) => Stack(
          children: [
            // 可拖動卡片，不使用全屏底層以允許底層滑動
            DraggableTermRulerProgressionCard(
              result: result,
              viewModel: widget.viewModel,
              onClose: _removeOverlay,
              initialPosition: Offset(left, top),
              onNavigateToAnalysis: (term) {
                // 先關閉 overlay
                _removeOverlay();

                // 然後導航到分析頁面
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    settings: const RouteSettings(name: 'term_ruler_stock_analysis'),
                    builder: (context) => TermRulerStockAnalysisPage(
                      chartViewModel: widget.viewModel,
                      termRulerItem: term,
                      aspects: _calculateTermRulerAspects(term),
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      );

      // 插入 overlay
      Overlay.of(context).insert(_overlayEntry!);
      print('_showTermRulerProgressionCard: overlay inserted');
    } catch (e) {
      print('_showTermRulerProgressionCard error: $e');
    }
  }

  /// 顯示界主星配置法
  void _showTermRulerProgression(BuildContext context) async {
    try {
      // 顯示載入對話框
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(),
        ),
      );

      // 計算界主星配置法時間表
      final TermRulerTimelineResult result =
          await widget.viewModel.calculateTermRulerProgressionTimeline();

      // 關閉載入對話框
      if (mounted) Navigator.of(context).pop();

      // 顯示結果對話框
      if (mounted) {
        _showTermRulerProgressionCard(context, result);
      }
    } catch (e) {
      logger.e('計算界主星配置法失敗: $e');
      // 關閉載入對話框
      if (mounted) Navigator.of(context).pop();

      // 顯示錯誤對話框
      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('錯誤'),
            content: Text('計算界主星配置法失敗：$e'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('確定'),
              ),
            ],
          ),
        );
      }
    }
  }

  /// 顯示小限法頁面
  void _showProfectionCard(
      BuildContext context, ProfectionTimelineResult result) {
    try {
      // 導航到小限法時間軸頁面
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => ProfectionTimelinePage(
            result: result,
            viewModel: widget.viewModel,
          ),
        ),
      );
    } catch (e) {
      print('_showProfectionCard error: $e');
    }
  }

  /// 顯示小限法
  void _showProfection(BuildContext context) async {
    try {
      // 顯示載入對話框
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(),
        ),
      );

      // 計算小限法時間表
      final ProfectionTimelineResult result =
          await widget.viewModel.calculateProfectionTimeline();

      // 關閉載入對話框
      if (mounted) Navigator.of(context).pop();

      // 顯示結果對話框
      if (mounted) {
        _showProfectionCard(context, result);
      }
    } catch (e) {
      logger.e('計算小限法失敗: $e');
      // 關閉載入對話框
      if (mounted) Navigator.of(context).pop();

      // 顯示錯誤對話框
      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('錯誤'),
            content: Text('計算小限法失敗：$e'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('確定'),
              ),
            ],
          ),
        );
      }
    }
  }

  /// 顯示行星詳細信息
  void _showPlanetDetails(BuildContext context, PlanetPosition planet) {
    print('_showPlanetDetails: planet = ${planet.name}');

    // 先移除現有的 overlay
    _removeOverlay();

    // 計算行星在畫面上的位置
    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final Size size = renderBox.size;
    print('_showPlanetDetails: size = $size');

    // 獲取行星相位信息
    final aspects = widget.viewModel.chartData.aspects
            ?.where((aspect) =>
                (aspect.planet1.name == planet.name ||
                    aspect.planet2.name == planet.name) &&
                aspect.receptionType == ReceptionType.none)
            .toList() ??
        [];
    print('_showPlanetDetails: aspects.length = ${aspects.length}');

    // 獲取行星互容接納關係
    final receptions = widget.viewModel.chartData.aspects
            ?.where((aspect) =>
                (aspect.planet1.name == planet.name ||
                    aspect.planet2.name == planet.name) &&
                aspect.receptionType != ReceptionType.none)
            .toList() ??
        [];
    print('_showPlanetDetails: receptions.length = ${receptions.length}');

    // 計算卡片的位置，確保它不會超出畫面
    const double cardWidth = 280;
    const double cardHeight = 300; // 估計高度

    // 先將卡片放在畫面中央
    double left = (size.width - cardWidth) / 2;
    double top = (size.height - cardHeight) / 2;
    print('_showPlanetDetails: card position = ($left, $top)');

    try {
      // 創建並插入 overlay
      _overlayEntry = OverlayEntry(
        builder: (context) => Stack(
          children: [
            // 可拖動卡片，不使用全屏底層以允許底層滑動
            DraggablePlanetDetailCard(
              planet: planet,
              aspects: aspects,
              viewModel: widget.viewModel,
              onClose: _removeOverlay,
              initialPosition: Offset(left, top),
            ),
          ],
        ),
      );

      // 插入 overlay
      Overlay.of(context).insert(_overlayEntry!);
      print('_showPlanetDetails: overlay inserted');
    } catch (e) {
      print('_showPlanetDetails error: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.viewModel.chartData.planets == null ||
        widget.viewModel.chartData.houses == null) {
      return const Center(child: CircularProgressIndicator());
    }

    if (widget.viewModel.chartType == ChartType.firdaria) {
      // 檢查是否已經有法達盤數據，如果沒有則計算
      if (widget.viewModel.firdariaData == null ||
          widget.viewModel.firdariaData!.isEmpty) {
        _loadFirdariaData();
      }
    }

    final screenSize = MediaQuery.of(context).size;
    final screenWidth = screenSize.width;
    final screenHeight = screenSize.height;

    // 計算星盤大小 - 最大化寬度利用，符合螢幕寬度
    final availableWidth = screenWidth; // 左右各8px最小邊距，最大化寬度
    final availableHeight = screenHeight - 200; // 預留200px給標題、按鈕和其他UI元素

    // 優先使用寬度，讓星盤拉到最寬
    double chartSize = availableWidth;

    // 如果寬度超出高度限制，則使用高度限制
    if (chartSize > availableHeight) {
      chartSize = availableHeight;
    }

    // 確保星盤不會太小，移除上限以最大化寬度
    chartSize = chartSize.clamp(280.0, double.infinity);

    // 使用Stack佈局，將標題放在左上角
    // 使用 Consumer 監聽 SettingsViewModel 的變化
    return Consumer<SettingsViewModel>(
      builder: (context, settingsViewModel, child) {
        return Stack(
          children: [
            // 主要內容區域
            SingleChildScrollView(
              controller: _scrollController,
              child: Column(
                children: [

                  // 快速星盤類型切換器 - 放在最上方
                  ChartTypeQuickSelector(
                    viewModel: widget.viewModel,
                    onChartTypeChanged: (chartType) {
                      // 清除緩存的繪製器，強制重新繪製
                      _cachedPainter = null;
                      setState(() {});
                    },
                  ),
                  Row(
                    children: [
                      const SizedBox(width: 10),
                      // 使用 Consumer 監聽 ChartViewModel 的變化
                      Consumer<ChartViewModel>(
                        builder: (context, chartViewModel, child) {
                          return ChartTitleWidget(viewModel: chartViewModel);
                        },
                      ),
                      const Spacer(),
                      // 螢幕最右側：星盤外觀設定
                      _buildSettingsButton(),
                      const SizedBox(width: 10),
                    ],
                  ),

                  // 可縮放星盤圖 - 置中顯示，最大化寬度
                  SizedBox(
                    width: double.infinity, // 佔滿寬度
                    height: chartSize,
                    child: Center(
                      // 確保星盤置中
                      child: Container(
                        height: chartSize,
                        width: chartSize,
                        decoration: BoxDecoration(
                          color: Colors.transparent, // 透明背景
                          borderRadius: BorderRadius.circular(12), // 統一的圓角
                        ),
                        child: ZoomableChartWidget(
                          viewModel: widget.viewModel,
                          onPlanetTap: (planet) =>
                              _showPlanetDetails(context, planet),
                          onFirdariaTap: (period) {
                            final index = widget.viewModel.firdariaData
                                    ?.indexOf(period) ??
                                -1;
                            if (index >= 0) {
                              widget.viewModel.selectedFirdariaPeriodIndex =
                                  index;
                              setState(() {});
                            }
                          },
                        ),
                      ),
                    ),
                  ),

                  const SizedBox(height: 10),

                  Stack(
                    children: [
                      Row(
                        children: [
                          const SizedBox(width: 10),
                          _buildFunctionButtonGroupLeft(),
                          const Spacer(),
                          _buildFunctionButtonGroupCenter(),
                          const Spacer(),
                          // 只在返照盤類型時顯示比較按鈕
                          if (widget.viewModel.chartType.isReturnChart) ...[
                            const SizedBox(width: 10),
                            _buildReturnChartComparisonButton(),
                          ],
                          const Spacer(),
                          // 螢幕最右側：星盤外觀設定
                          _buildChartAppearanceButton(),
                          const SizedBox(width: 10),
                        ],
                      ),
                      // Align(
                      //   alignment: Alignment.center,
                      //   child: _buildFunctionButtonGroupCenter(),
                      // ),
                    ],
                  ),

                  const SizedBox(height: 10),

                  // 出生時間不確定警告區域
                  if (_hasTimeUncertainty()) ...[
                    _buildTimeUncertaintyWarning(),
                    const SizedBox(height: 10),
                  ],

                  // 時間調整控制元素
                  TimeAdjustmentWidget(viewModel: widget.viewModel),

                  const SizedBox(height: 10),

                  // 使用模組化的人物信息組件，放在底部
                  PersonInfoWidget(viewModel: widget.viewModel),
                  const SizedBox(height: 10),
                ],
              ),
            ),

            // 星盤標題 - 放在螢幕最左邊
            // Positioned(
            //   top: 58, // 快速選擇器高度 + 間距
            //   left: 8,
            //   child: ChartTitleWidget(viewModel: widget.viewModel),
            // ),
            //
            // // 設定按鈕 - 放在螢幕最右邊
            // Positioned(
            //   top: 58, // 與標題對齊
            //   right: 8,
            //   child: _buildSettingsButton(),
            // ),
          ],
        );
      },
    );
  }

  /// 檢查是否需要重新創建星盤繪製器
  bool _shouldRecreateChartPainter() {
    // 如果沒有緩存的繪製器，需要創建
    if (_cachedPainter == null) return true;

    // 檢查設定是否變更
    final currentSettings = widget.viewModel.settingsViewModel?.chartSettings;
    if (currentSettings == null) return false;

    // 檢查度數顯示設定是否變更
    if (_cachedPainter is ChartPainter) {
      final cachedPainter = _cachedPainter as ChartPainter;
      final cachedSettings = cachedPainter.chartSettings;

      if (cachedSettings == null && currentSettings != null) return true;
      if (cachedSettings != null && currentSettings == null) return true;
      if (cachedSettings == null && currentSettings == null) return false;

      // 檢查關鍵設定是否變更
      return cachedSettings!.showHouseDegrees !=
              currentSettings.showHouseDegrees ||
          cachedSettings.showPlanetDegrees !=
              currentSettings.showPlanetDegrees ||
          cachedSettings.showZodiacRulers != currentSettings.showZodiacRulers;
    }

    // 對於其他類型的繪製器，也進行類似檢查
    if (_cachedPainter is DualChartPainter) {
      final cachedPainter = _cachedPainter as DualChartPainter;
      final cachedSettings = cachedPainter.chartSettings;

      if (cachedSettings == null && currentSettings != null) return true;
      if (cachedSettings != null && currentSettings == null) return true;
      if (cachedSettings == null && currentSettings == null) return false;

      return cachedSettings!.showHouseDegrees !=
              currentSettings.showHouseDegrees ||
          cachedSettings.showPlanetDegrees !=
              currentSettings.showPlanetDegrees ||
          cachedSettings.showZodiacRulers != currentSettings.showZodiacRulers;
    }

    // 對於法達盤繪製器，檢查推運時間是否變化
    if (_cachedPainter is FirdariaChartPainter) {
      final cachedPainter = _cachedPainter as FirdariaChartPainter;
      final currentDate = widget.viewModel.specificDate;

      // 如果推運時間發生變化，需要重新創建繪製器
      return cachedPainter.currentDate != currentDate;
    }

    return false;
  }

  Widget _buildChartAppearanceButton() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.9),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Colors.grey.shade300,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(20),
          onTap: () => _showChartAppearanceSettings(context),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.palette_outlined,
                  size: 16,
                  color: Colors.grey.shade700,
                ),
                const SizedBox(width: 4),
                Text(
                  '外觀',
                  style: TextStyle(
                    fontSize: 11,
                    fontWeight: FontWeight.w500,
                    color: Colors.grey.shade700,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTermRulerButton() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.9),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Colors.grey.shade300,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(20),
          onTap: () => _showTermRulerProgression(context),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.timeline,
                  size: 16,
                  color: Colors.grey.shade700,
                ),
                const SizedBox(width: 4),
                Text(
                  '界主',
                  style: TextStyle(
                    fontSize: 11,
                    fontWeight: FontWeight.w500,
                    color: Colors.grey.shade700,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildProfectionButton() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.9),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Colors.grey.shade300,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(20),
          onTap: () => _showProfection(context),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.access_time,
                  size: 16,
                  color: Colors.grey.shade700,
                ),
                const SizedBox(width: 4),
                Text(
                  '小限',
                  style: TextStyle(
                    fontSize: 11,
                    fontWeight: FontWeight.w500,
                    color: Colors.grey.shade700,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 構建返照盤比較按鈕
  Widget _buildReturnChartComparisonButton() {
    final isComparisonMode = widget.viewModel.isReturnChartComparisonMode;
    final chartType = widget.viewModel.chartType;

    // 根據星盤類型決定按鈕文字
    String buttonText;
    if (chartType == ChartType.solarReturn) {
      buttonText = isComparisonMode ? '太陽返照' : '與本命比較';
    } else if (chartType == ChartType.lunarReturn) {
      buttonText = isComparisonMode ? '月亮返照' : '與本命比較';
    } else {
      buttonText = isComparisonMode ? '返照盤' : '與本命比較';
    }

    return Container(
      decoration: BoxDecoration(
        color: isComparisonMode
            ? Colors.blue.shade50.withOpacity(0.9)
            : Colors.white.withOpacity(0.9),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: isComparisonMode
              ? Colors.blue.shade300
              : Colors.grey.shade300,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(20),
          onTap: () => _toggleReturnChartComparisonMode(),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  isComparisonMode ? Icons.compare_arrows : Icons.compare,
                  size: 16,
                  color: isComparisonMode
                      ? Colors.blue.shade700
                      : Colors.grey.shade700,
                ),
                const SizedBox(width: 4),
                Text(
                  buttonText,
                  style: TextStyle(
                    fontSize: 11,
                    fontWeight: FontWeight.w500,
                    color: isComparisonMode
                        ? Colors.blue.shade700
                        : Colors.grey.shade700,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 功能按鈕組
  Widget _buildFunctionButtonGroupLeft() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // 螢幕最左側：界主星 + 小限法 + 返照盤比較按鈕
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildTermRulerButton(),
            const SizedBox(width: 10),
            _buildProfectionButton(),
          ],
        ),
      ],
    );
  }

  /// 切換返照盤比較模式
  void _toggleReturnChartComparisonMode() {
    final currentMode = widget.viewModel.isReturnChartComparisonMode;
    widget.viewModel.setReturnChartComparisonMode(!currentMode);

    // 清除緩存的繪製器，強制重新繪製
    _cachedPainter = null;
    setState(() {});

    logger.i('返照盤比較模式已切換為: ${!currentMode}');
  }

  Widget _buildFunctionButtonGroupCenter() {
    final hasSecondary = widget.viewModel.secondaryPerson != null;
    if (hasSecondary) {
      return _buildSwapPersonsButton();
    } else {
      return const SizedBox.shrink();
    }
  }

  Widget _buildSwapPersonsButton() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.9),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Colors.grey.shade300,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(20),
          onTap: () => _swapPersons(),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.swap_horiz,
                  size: 16,
                  color: Colors.grey.shade700,
                ),
                const SizedBox(width: 4),
                Text(
                  '交換',
                  style: TextStyle(
                    fontSize: 11,
                    fontWeight: FontWeight.w500,
                    color: Colors.grey.shade700,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 構建設定按鈕
  Widget _buildSettingsButton() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.9),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Colors.grey.shade300,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(20),
          onTap: () => _showChartSettings(),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.settings,
                  size: 16,
                  color: Colors.grey.shade700,
                ),
                const SizedBox(width: 4),
                Text(
                  '設定',
                  style: TextStyle(
                    fontSize: 11,
                    fontWeight: FontWeight.w500,
                    color: Colors.grey.shade700,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 交換主次要人物
  void _swapPersons() {
    try {
      widget.viewModel.swapPrimaryAndSecondaryPersons();

      // 顯示成功訊息
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(Icons.swap_horiz, color: Colors.white),
                const SizedBox(width: 8),
                Text(
                    '已交換主次要人物：${widget.viewModel.primaryPerson.name} ↔ ${widget.viewModel.secondaryPerson?.name}'),
              ],
            ),
            backgroundColor: Colors.blue.shade600,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      // 顯示錯誤訊息
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(Icons.error, color: Colors.white),
                const SizedBox(width: 8),
                Text('交換失敗：$e'),
              ],
            ),
            backgroundColor: Colors.red.shade600,
          ),
        );
      }
    }
  }

  void _showChartAppearanceSettings(BuildContext context) async {
    await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => ChartDisplaySettingsPage(
          chartViewModel: widget.viewModel,
          initialChartType: widget.viewModel.chartType, // 傳遞當前星盤類型
        ),
      ),
    );

    // 設置頁面返回後，刷新星盤顯示
    if (mounted) {
      // 通知 ChartViewModel 重新載入設置並刷新星盤
      await widget.viewModel.calculateChart();
    }
  }

  /// 顯示星盤設定
  void _showChartSettings() async {
    print('⚙️ 打開星盤設定');

    await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => SettingsPage()
    ),
    );

    // 設置頁面返回後，刷新星盤顯示
    print('🔄 設置頁面返回，刷新星盤顯示');
    if (mounted) {
      // 通知 ChartViewModel 重新載入設置並刷新星盤
      await widget.viewModel.calculateChart();
      print('✅ 星盤設置已更新');
    }
  }

  /// 檢查是否有時間不確定的情況
  bool _hasTimeUncertainty() {
    final primaryUncertain = widget.viewModel.primaryPerson.isTimeUncertain;
    final secondaryUncertain = widget.viewModel.secondaryPerson?.isTimeUncertain ?? false;
    return primaryUncertain || secondaryUncertain;
  }

  /// 構建出生時間不確定警告
  Widget _buildTimeUncertaintyWarning() {
    final primaryUncertain = widget.viewModel.primaryPerson.isTimeUncertain;
    final secondaryUncertain = widget.viewModel.secondaryPerson?.isTimeUncertain ?? false;

    String warningText;
    if (primaryUncertain && secondaryUncertain) {
      warningText = '兩位人物的出生時間都不確定，星盤分析可能受到影響';
    } else if (primaryUncertain) {
      warningText = '主要人物的出生時間不確定，星盤分析可能受到影響';
    } else {
      warningText = '次要人物的出生時間不確定，星盤分析可能受到影響';
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.orange.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.orange.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          const Icon(
            Icons.warning_amber_rounded,
            color: Colors.orange,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              warningText,
              style: TextStyle(
                color: Colors.orange[800],
                fontSize: 14,
              ),
            ),
          ),
          GestureDetector(
            onTap: () => _showTimeUncertaintyDialog(),
            child: Container(
              padding: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: Colors.orange.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Icon(
                Icons.help_outline,
                size: 16,
                color: Colors.orange,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 顯示出生時間不確定說明對話框
  Future<void> _showTimeUncertaintyDialog() async {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Row(
            children: [
              Icon(
                Icons.warning_amber_rounded,
                color: Colors.orange,
                size: 24,
              ),
              SizedBox(width: 8),
              Text('出生時間不確定的影響'),
            ],
          ),
          content: const SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  '當出生時間不確定時，以下星盤要素可能會受到影響：',
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 16,
                  ),
                ),
                SizedBox(height: 12),
                Text('• 上升星座（需要精確的出生時間）'),
                Text('• 宮位分析（宮位邊界會因時間而變化）'),
                Text('• 月亮星座（如果時間差距較大）'),
                Text('• 行星宮位位置'),
                Text('• 相位的精確度'),
                Text('• 中天星座和天底星座'),
                SizedBox(height: 12),
                Text(
                  '建議：',
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 16,
                  ),
                ),
                SizedBox(height: 8),
                Text('• 盡量確認準確的出生時間'),
                Text('• 重點關注太陽星座和主要行星位置'),
                Text('• 參考星盤時保持適當的彈性'),
                Text('• 可以使用太陽星座為主的分析方法'),
                SizedBox(height: 12),
                Text(
                  '注意：星盤顯示會以提供的時間為準，但解讀時請考慮時間不確定的因素。',
                  style: TextStyle(
                    color: Colors.grey,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('了解'),
            ),
          ],
        );
      },
    );
  }
}

/// 星盤外觀設定對話框
class _ChartAppearanceDialog extends StatefulWidget {
  final ChartViewModel viewModel;

  const _ChartAppearanceDialog({
    Key? key,
    required this.viewModel,
  }) : super(key: key);

  @override
  State<_ChartAppearanceDialog> createState() => _ChartAppearanceDialogState();
}

class _ChartAppearanceDialogState extends State<_ChartAppearanceDialog> {
  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        width: 320,
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 標題
            Row(
              children: [
                Icon(
                  Icons.palette_outlined,
                  color: Colors.blue.shade600,
                  size: 24,
                ),
                const SizedBox(width: 8),
                const Text(
                  '星盤外觀設定',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                  iconSize: 20,
                ),
              ],
            ),
            const SizedBox(height: 16),

            // 星盤顯示模式
            _buildSettingSection(
              title: '顯示模式',
              icon: Icons.visibility_outlined,
              child: Column(
                children: [
                  _buildRadioOption(
                    title: '經典模式',
                    subtitle: '傳統星盤顯示，簡潔清晰',
                    value: 'classic',
                    groupValue: _getCurrentDisplayMode(),
                    onChanged: (value) {
                      _setDisplayMode('classic');
                    },
                  ),
                  _buildRadioOption(
                    title: '度數模式',
                    subtitle: '顯示宮位度數與行星度數',
                    value: 'degrees',
                    groupValue: _getCurrentDisplayMode(),
                    onChanged: (value) {
                      _setDisplayMode('degrees');
                    },
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // 界主星顯示
            _buildSettingSection(
              title: '界主星',
              icon: Icons.star_outline,
              child: _buildSwitchOption(
                title: '顯示界主星',
                subtitle: '在星盤外圈顯示星座界主星符號',
                value: widget.viewModel.settingsViewModel?.chartSettings
                        ?.showZodiacRulers ??
                    false,
                onChanged: (value) {
                  final settingsViewModel = widget.viewModel.settingsViewModel;
                  if (settingsViewModel != null) {
                    settingsViewModel.updateZodiacRulersVisibility(value);
                    setState(() {}); // 更新對話框狀態
                  }
                },
              ),
            ),

            const SizedBox(height: 20),

            // 確認按鈕
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () => Navigator.of(context).pop(),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue.shade600,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: const Text('確認'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSettingSection({
    required String title,
    required IconData icon,
    required Widget child,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, size: 18, color: Colors.grey.shade600),
              const SizedBox(width: 6),
              Text(
                title,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey.shade700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          child,
        ],
      ),
    );
  }

  Widget _buildRadioOption({
    required String title,
    required String subtitle,
    required String value,
    required String groupValue,
    required ValueChanged<String?> onChanged,
  }) {
    return InkWell(
      onTap: () => onChanged(value),
      borderRadius: BorderRadius.circular(6),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
        child: Row(
          children: [
            Radio<String>(
              value: value,
              groupValue: groupValue,
              onChanged: onChanged,
              materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSwitchOption({
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
          ),
        ],
      ),
    );
  }

  String _getCurrentDisplayMode() {
    final chartSettings = widget.viewModel.settingsViewModel?.chartSettings;
    if (chartSettings == null) return 'classic';

    // 如果宮位度數和行星度數都開啟，則為度數模式
    if (chartSettings.showHouseDegrees && chartSettings.showPlanetDegrees) {
      return 'degrees';
    }
    return 'classic';
  }

  void _setDisplayMode(String mode) {
    final settingsViewModel = widget.viewModel.settingsViewModel;
    if (settingsViewModel == null) return;

    if (mode == 'degrees') {
      // 度數模式：開啟宮位度數、星座度數和行星度數
      settingsViewModel.updateHouseDegreesVisibility(true);
      settingsViewModel.updatePlanetDegreesVisibility(true);
    } else {
      // 經典模式：關閉所有度數顯示
      settingsViewModel.updateHouseDegreesVisibility(false);
      settingsViewModel.updatePlanetDegreesVisibility(false);
    }

    setState(() {}); // 更新對話框狀態
  }
}

/// 可拖動的透明對話框組件
class _DraggableTransparentDialog extends StatefulWidget {
  final Widget child;
  final double maxWidth;

  const _DraggableTransparentDialog({
    Key? key,
    required this.child,
    required this.maxWidth,
  }) : super(key: key);

  @override
  State<_DraggableTransparentDialog> createState() =>
      _DraggableTransparentDialogState();
}

class _DraggableTransparentDialogState
    extends State<_DraggableTransparentDialog> {
  Offset _position = Offset.zero;
  bool _isDragging = false;
  double _opacity = 1.0;

  @override
  void initState() {
    super.initState();
    // 初始位置設置為螢幕中央
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final screenSize = MediaQuery.of(context).size;
      setState(() {
        _position = Offset(
          (screenSize.width - widget.maxWidth) / 2,
          screenSize.height * 0.1, // 距離頂部 10%
        );
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;

    return Stack(
      children: [
        // 透明背景，點擊時關閉對話框
        GestureDetector(
          onTap: () => Navigator.of(context).pop(),
          child: Container(
            width: screenSize.width,
            height: screenSize.height,
            color: Colors.transparent,
          ),
        ),
        // 可拖動的對話框
        Positioned(
          left: _position.dx,
          top: _position.dy,
          child: GestureDetector(
            onPanStart: (details) {
              setState(() {
                _isDragging = true;
                _opacity = 0.7; // 拖動時變透明
              });
            },
            onPanUpdate: (details) {
              setState(() {
                _position += details.delta;

                // 限制在螢幕範圍內
                _position = Offset(
                  _position.dx.clamp(0, screenSize.width - widget.maxWidth),
                  _position.dy.clamp(0, screenSize.height - 100), // 保留底部空間
                );
              });
            },
            onPanEnd: (details) {
              setState(() {
                _isDragging = false;
                _opacity = 1.0; // 停止拖動時恢復不透明
              });
            },
            onTap: () {
              // 點擊對話框內容時切換透明度
              setState(() {
                _opacity = _opacity == 1.0 ? 0.3 : 1.0;
              });
            },
            child: AnimatedOpacity(
              duration: const Duration(milliseconds: 200),
              opacity: _opacity,
              child: Container(
                width: widget.maxWidth,
                constraints: BoxConstraints(
                  maxHeight: screenSize.height * 0.8,
                ),
                decoration: BoxDecoration(
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.3),
                      blurRadius: 10,
                      offset: const Offset(0, 5),
                    ),
                  ],
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: Material(
                    borderRadius: BorderRadius.circular(12),
                    elevation: 0,
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: _isDragging
                              ? Colors.blue.withOpacity(0.5)
                              : Colors.grey.withOpacity(0.3),
                          width: _isDragging ? 2 : 1,
                        ),
                      ),
                      child: widget.child,
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
        // 拖動提示（僅在第一次顯示時出現）
        if (!_isDragging && _position == Offset.zero)
          Positioned(
            right: 10, // 縮小右邊距
            top: 10, // 縮小上邊距
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              // 縮小內邊距
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.7),
                borderRadius: BorderRadius.circular(15), // 縮小圓角
              ),
              child: const Text(
                '可拖動 • 點擊切換透明度',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 10, // 縮小字體
                ),
              ),
            ),
          ),
      ],
    );
  }
}
