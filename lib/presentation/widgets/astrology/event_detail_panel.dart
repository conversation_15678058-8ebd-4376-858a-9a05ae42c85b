import 'package:flutter/material.dart';

import '../../../data/models/astrology/astro_event.dart';
import '../../../data/models/astrology/event_score.dart';
import '../../../presentation/themes/app_theme.dart';
import '../../widgets/common/styled_card.dart';

/// 事件詳情面板主題配色
class EventDetailTheme {
  /// 背景顏色
  final Color backgroundColor;

  /// 標題區域顏色
  final Color headerColor;

  /// 標題文字顏色
  final Color headerTextColor;

  /// 一般文字顏色
  final Color textColor;

  /// 評分背景顏色
  final Color scoreBackgroundColor;

  const EventDetailTheme({
    required this.backgroundColor,
    required this.headerColor,
    required this.headerTextColor,
    required this.textColor,
    required this.scoreBackgroundColor,
  });

  /// Starmaster 專業模式主題
  const EventDetailTheme.starmaster()
      : backgroundColor = Colors.white,
        headerColor = AppColors.starmasterPrimary,
        headerTextColor = Colors.white,
        textColor = AppColors.starmasterTextPrimary,
        scoreBackgroundColor = AppColors.starmasterSurface;

  /// Starlight 初心者模式主題
  const EventDetailTheme.starlight()
      : backgroundColor = AppColors.starlightBackground,
        headerColor = AppColors.starlightPrimary,
        headerTextColor = Colors.white,
        textColor = AppColors.starlightTextPrimary,
        scoreBackgroundColor = AppColors.starlightSurface;

  /// 深色模式主題
  const EventDetailTheme.dark()
      : backgroundColor = const Color(0xFF1E1E1E),
        headerColor = const Color(0xFF2D2D2D),
        headerTextColor = Colors.white,
        textColor = Colors.white,
        scoreBackgroundColor = const Color(0xFF2D2D2D);

  /// 管理後台主題（統一配色）
  const EventDetailTheme.admin()
      : backgroundColor = Colors.white,
        headerColor = AppColors.royalIndigo,
        headerTextColor = Colors.white,
        textColor = AppColors.textDark,
        scoreBackgroundColor = const Color(0xFFF8FAFC);
}

/// 事件詳情面板組件
/// 
/// 點擊日期展開顯示觸發的行星、宮位、相位等詳細說明
class EventDetailPanel extends StatefulWidget {
  /// 選中的日期
  final DateTime selectedDate;
  
  /// 該日的事件評分
  final DailyEventScore? dailyScore;
  
  /// 該日的所有事件
  final List<AstroEvent> events;
  
  /// 關閉面板回調
  final VoidCallback? onClose;
  
  /// 主題配色方案
  final EventDetailTheme theme;
  
  /// 是否顯示詳細評分
  final bool showDetailedScores;

  const EventDetailPanel({
    super.key,
    required this.selectedDate,
    this.dailyScore,
    required this.events,
    this.onClose,
    this.theme = const EventDetailTheme.starmaster(),
    this.showDetailedScores = true,
  });

  @override
  State<EventDetailPanel> createState() => _EventDetailPanelState();
}

class _EventDetailPanelState extends State<EventDetailPanel>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _slideAnimation;
  late Animation<double> _fadeAnimation;

  // 篩選狀態
  Set<AstroEventType> _selectedEventTypes = {};
  Set<EventImportance> _selectedImportances = {};
  Set<String> _selectedPlanets = {};
  bool _showFilterPanel = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _slideAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeIn,
    ));

    // 初始化篩選狀態 - 預設顯示所有事件
    _initializeFilters();

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, _slideAnimation.value * 50),
          child: Opacity(
            opacity: _fadeAnimation.value,
            child: _buildPanel(),
          ),
        );
      },
    );
  }

  /// 建立面板主體
  Widget _buildPanel() {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: widget.theme.backgroundColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.15),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildHeader(),
          _buildContent(),
        ],
      ),
    );
  }

  /// 建立標題區域
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: widget.theme.headerColor,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.event_note,
              color: widget.theme.headerTextColor,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _formatDate(widget.selectedDate),
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: widget.theme.headerTextColor,
                  ),
                ),
                if (widget.dailyScore != null)
                  Text(
                    '總分: ${widget.dailyScore!.totalScore.toStringAsFixed(1)} | ${widget.events.length}個事件',
                    style: TextStyle(
                      fontSize: 14,
                      color: widget.theme.headerTextColor.withOpacity(0.8),
                    ),
                  ),
              ],
            ),
          ),
          // 篩選按鈕
          IconButton(
            onPressed: _toggleFilterPanel,
            icon: Icon(
              _showFilterPanel ? Icons.filter_list_off : Icons.filter_list,
              color: widget.theme.headerTextColor,
            ),
            tooltip: _showFilterPanel ? '隱藏篩選' : '顯示篩選',
          ),
          // 關閉按鈕
          IconButton(
            onPressed: _handleClose,
            icon: Icon(
              Icons.close,
              color: widget.theme.headerTextColor,
            ),
            tooltip: '關閉',
          ),
        ],
      ),
    );
  }

  /// 建立內容區域
  Widget _buildContent() {
    return Container(
      constraints: const BoxConstraints(maxHeight: 400),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 篩選面板
          if (_showFilterPanel) _buildFilterPanel(),
          // 事件列表
          Expanded(
            child: _filteredEvents.isEmpty
                ? _buildEmptyState()
                : _buildEventsList(),
          ),
        ],
      ),
    );
  }

  /// 建立空狀態
  Widget _buildEmptyState() {
    return Padding(
      padding: const EdgeInsets.all(40),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.event_busy,
            size: 48,
            color: widget.theme.textColor.withOpacity(0.5),
          ),
          const SizedBox(height: 16),
          Text(
            '此日期沒有重要的占星事件',
            style: TextStyle(
              fontSize: 16,
              color: widget.theme.textColor.withOpacity(0.7),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            '可以嘗試調整事件偵測設定或查看其他日期',
            style: TextStyle(
              fontSize: 14,
              color: widget.theme.textColor.withOpacity(0.5),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// 建立事件列表
  Widget _buildEventsList() {
    final events = _filteredEvents;
    return ListView.builder(
      shrinkWrap: true,
      padding: const EdgeInsets.all(16),
      itemCount: events.length,
      itemBuilder: (context, index) {
        final event = events[index];
        return _buildEventCard(event, index);
      },
    );
  }

  /// 建立事件卡片
  Widget _buildEventCard(AstroEvent event, int index) {
    return Container(
      margin: EdgeInsets.only(bottom: index < widget.events.length - 1 ? 12 : 0),
      child: StyledCard(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildEventHeader(event),
            const SizedBox(height: 16),
            _buildEventDetails(event),
            if (widget.showDetailedScores && event.score != null)
              _buildScoreDetails(event),
          ],
        ),
      ),
    );
  }

  /// 建立事件標題
  Widget _buildEventHeader(AstroEvent event) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(6),
          decoration: BoxDecoration(
            color: event.color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Icon(
            event.icon,
            color: event.color,
            size: 16,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                event.eventTypeName,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: widget.theme.textColor,
                ),
              ),
              Text(
                event.title,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: widget.theme.textColor,
                ),
              ),
              Text(
                event.typeDisplayName,
                style: TextStyle(
                  fontSize: 12,
                  color: widget.theme.textColor.withOpacity(0.6),
                ),
              ),
            ],
          ),
        ),
        if (event.score != null)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: _getScoreColor(event.score!),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              event.score!.toStringAsFixed(1),
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ),
      ],
    );
  }

  /// 建立事件詳情
  Widget _buildEventDetails(AstroEvent event) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          event.description,
          style: TextStyle(
            fontSize: 14,
            color: widget.theme.textColor.withOpacity(0.8),
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 4,
          children: [
            if (event.involvedPlanets?.isNotEmpty == true)
              ...event.involvedPlanets!.map((planet) => _buildTag(planet, Colors.blue)),
            if (event.involvedSigns?.isNotEmpty == true)
              ...event.involvedSigns!.map((sign) => _buildTag(sign, Colors.green)),
            if (event.involvedHouses?.isNotEmpty == true)
              ...event.involvedHouses!.map((house) => _buildTag('第$house宮', Colors.orange)),
            if (event.aspectType != null)
              _buildTag(event.aspectType!, Colors.purple),
          ],
        ),
      ],
    );
  }

  /// 建立評分詳情
  Widget _buildScoreDetails(AstroEvent event) {
    final scoreData = event.additionalData?['score'] as EventScore?;
    if (scoreData == null) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.only(top: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: widget.theme.scoreBackgroundColor,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '評分詳情',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: widget.theme.textColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            scoreData.explanation,
            style: TextStyle(
              fontSize: 12,
              color: widget.theme.textColor.withOpacity(0.7),
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  /// 建立標籤
  Widget _buildTag(String text, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 10,
          color: color,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  /// 格式化日期
  String _formatDate(DateTime date) {
    return '${date.year}年${date.month}月${date.day}日';
  }

  /// 獲取分數顏色
  Color _getScoreColor(double score) {
    if (score >= 80) return Colors.red;
    if (score >= 60) return Colors.orange;
    if (score >= 40) return Colors.amber;
    if (score >= 20) return Colors.blue;
    return Colors.grey;
  }

  /// 處理關閉
  void _handleClose() {
    _animationController.reverse().then((_) {
      widget.onClose?.call();
    });
  }

  /// 初始化篩選狀態
  void _initializeFilters() {
    // 收集所有事件類型
    _selectedEventTypes = widget.events.map((e) => e.type).toSet();

    // 收集所有重要性等級
    _selectedImportances = widget.events
        .where((e) => e.eventImportance != null)
        .map((e) => e.eventImportance!)
        .toSet();

    // 收集所有涉及的行星
    _selectedPlanets = widget.events
        .where((e) => e.involvedPlanets != null)
        .expand((e) => e.involvedPlanets!)
        .toSet();
  }

  /// 切換篩選面板顯示
  void _toggleFilterPanel() {
    setState(() {
      _showFilterPanel = !_showFilterPanel;
    });
  }

  /// 獲取篩選後的事件列表
  List<AstroEvent> get _filteredEvents {
    return widget.events.where((event) {
      // 事件類型篩選
      if (_selectedEventTypes.isNotEmpty && !_selectedEventTypes.contains(event.type)) {
        return false;
      }

      // 重要性篩選
      if (_selectedImportances.isNotEmpty &&
          (event.eventImportance == null || !_selectedImportances.contains(event.eventImportance!))) {
        return false;
      }

      // 行星篩選
      if (_selectedPlanets.isNotEmpty &&
          (event.involvedPlanets == null ||
           !event.involvedPlanets!.any((planet) => _selectedPlanets.contains(planet)))) {
        return false;
      }

      return true;
    }).toList();
  }

  /// 建立篩選面板
  Widget _buildFilterPanel() {
    return Container(
      constraints: const BoxConstraints(maxHeight: 280), // 限制篩選面板最大高度
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: widget.theme.backgroundColor,
        border: Border(
          bottom: BorderSide(
            color: Colors.grey.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
      ),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              children: [
                Icon(
                  Icons.filter_list,
                  size: 18,
                  color: widget.theme.textColor,
                ),
                const SizedBox(width: 8),
                Text(
                  '事件篩選',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: widget.theme.textColor,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: _resetFilters,
                  child: const Text('重置'),
                ),
              ],
            ),
            const SizedBox(height: 8),
            _buildEventTypeFilter(),
            const SizedBox(height: 8),
            _buildImportanceFilter(),
            const SizedBox(height: 8),
            _buildPlanetFilter(),
          ],
        ),
      ),
    );
  }

  /// 建立事件類型篩選器
  Widget _buildEventTypeFilter() {
    final allEventTypes = widget.events.map((e) => e.type).toSet().toList();
    if (allEventTypes.isEmpty) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '事件類型',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: widget.theme.textColor,
          ),
        ),
        const SizedBox(height: 6),
        Wrap(
          spacing: 6,
          runSpacing: 3,
          children: allEventTypes.map((eventType) {
            final isSelected = _selectedEventTypes.contains(eventType);
            return FilterChip(
              materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
              visualDensity: VisualDensity.compact,
              label: Text(
                eventType.name,
                style: TextStyle(
                  fontSize: 11,
                  color: isSelected ? Colors.white : widget.theme.textColor,
                ),
              ),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  if (selected) {
                    _selectedEventTypes.add(eventType);
                  } else {
                    _selectedEventTypes.remove(eventType);
                  }
                });
              },
              backgroundColor: Colors.grey.withValues(alpha: 0.2),
              selectedColor: eventType.defaultColor,
              checkmarkColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
            );
          }).toList(),
        ),
      ],
    );
  }

  /// 建立重要性篩選器
  Widget _buildImportanceFilter() {
    final allImportances = widget.events
        .where((e) => e.eventImportance != null)
        .map((e) => e.eventImportance!)
        .toSet()
        .toList();

    if (allImportances.isEmpty) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '重要性等級',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: widget.theme.textColor,
          ),
        ),
        const SizedBox(height: 6),
        Wrap(
          spacing: 6,
          runSpacing: 3,
          children: allImportances.map((importance) {
            final isSelected = _selectedImportances.contains(importance);
            return FilterChip(
              materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
              visualDensity: VisualDensity.compact,
              label: Text(
                importance.displayName,
                style: TextStyle(
                  fontSize: 11,
                  color: isSelected ? Colors.white : widget.theme.textColor,
                ),
              ),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  if (selected) {
                    _selectedImportances.add(importance);
                  } else {
                    _selectedImportances.remove(importance);
                  }
                });
              },
              backgroundColor: Colors.grey.withValues(alpha: 0.2),
              selectedColor: importance.color,
              checkmarkColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
            );
          }).toList(),
        ),
      ],
    );
  }

  /// 建立行星篩選器（可上下滾動）
  Widget _buildPlanetFilter() {
    final allPlanets = widget.events
        .where((e) => e.involvedPlanets != null)
        .expand((e) => e.involvedPlanets!)
        .toSet()
        .toList();

    if (allPlanets.isEmpty) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '涉及行星',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: widget.theme.textColor,
          ),
        ),
        const SizedBox(height: 8),
        // 使用 ConstrainedBox 限制最大高度，避免溢出
        ConstrainedBox(
          constraints: const BoxConstraints(maxHeight: 80),
          child: SingleChildScrollView(
            child: Wrap(
              spacing: 6,
              runSpacing: 4,
              children: allPlanets.map((planet) {
                final isSelected = _selectedPlanets.contains(planet);
                return FilterChip(
                  materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  visualDensity: VisualDensity.compact,
                  label: Text(
                    planet,
                    style: TextStyle(
                      fontSize: 11,
                      color: isSelected ? Colors.white : widget.theme.textColor,
                    ),
                  ),
                  selected: isSelected,
                  onSelected: (selected) {
                    setState(() {
                      if (selected) {
                        _selectedPlanets.add(planet);
                      } else {
                        _selectedPlanets.remove(planet);
                      }
                    });
                  },
                  backgroundColor: Colors.grey.withValues(alpha: 0.2),
                  selectedColor: AppColors.royalIndigo,
                  checkmarkColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                );
              }).toList(),
            ),
          ),
        ),
      ],
    );
  }


  /// 重置篩選器
  void _resetFilters() {
    setState(() {
      _initializeFilters();
    });
  }
}
