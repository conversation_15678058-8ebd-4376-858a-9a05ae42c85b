import 'dart:math' as math;

import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';

import '../../../data/models/astrology/event_score.dart';
import '../../../data/models/astrology/event_timeline_data.dart';
import '../../../presentation/themes/app_theme.dart';

/// 事件時間軸主題配色
class EventTimelineTheme {
  /// 背景顏色
  final Color backgroundColor;

  /// 標題區域顏色
  final Color headerColor;

  /// 標題文字顏色
  final Color headerTextColor;

  /// 一般文字顏色
  final Color textColor;

  /// 主線條顏色
  final Color lineColor;

  /// 平均線顏色
  final Color averageLineColor;

  /// 數據點顏色
  final Color dotColor;

  /// 數據點邊框顏色
  final Color dotBorderColor;

  /// 高亮顏色
  final Color highlightColor;

  /// 網格顏色
  final Color gridColor;

  /// 邊框顏色
  final Color borderColor;

  /// 工具提示背景顏色
  final Color tooltipBackgroundColor;

  /// 工具提示文字顏色
  final Color tooltipTextColor;

  const EventTimelineTheme({
    required this.backgroundColor,
    required this.headerColor,
    required this.headerTextColor,
    required this.textColor,
    required this.lineColor,
    required this.averageLineColor,
    required this.dotColor,
    required this.dotBorderColor,
    required this.highlightColor,
    required this.gridColor,
    required this.borderColor,
    required this.tooltipBackgroundColor,
    required this.tooltipTextColor,
  });

  /// Starmaster 專業模式主題
  const EventTimelineTheme.starmaster()
      : backgroundColor = Colors.white,
        headerColor = AppColors.starmasterPrimary,
        headerTextColor = Colors.white,
        textColor = AppColors.starmasterTextPrimary,
        lineColor = AppColors.starmasterAccent,
        averageLineColor = AppColors.starmasterSecondary,
        dotColor = AppColors.starmasterAccent,
        dotBorderColor = Colors.white,
        highlightColor = AppColors.starmasterPrimary,
        gridColor = AppColors.starmasterBorder,
        borderColor = AppColors.starmasterBorder,
        tooltipBackgroundColor = AppColors.starmasterSurface,
        tooltipTextColor = AppColors.starmasterTextPrimary;

  /// Starlight 初心者模式主題
  const EventTimelineTheme.starlight()
      : backgroundColor = AppColors.starlightBackground,
        headerColor = AppColors.starlightPrimary,
        headerTextColor = Colors.white,
        textColor = AppColors.starlightTextPrimary,
        lineColor = AppColors.starlightAccent,
        averageLineColor = AppColors.starlightSecondary,
        dotColor = AppColors.starlightAccent,
        dotBorderColor = Colors.white,
        highlightColor = AppColors.starlightPrimary,
        gridColor = AppColors.starlightBorder,
        borderColor = AppColors.starlightBorder,
        tooltipBackgroundColor = AppColors.starlightSurface,
        tooltipTextColor = AppColors.starlightTextPrimary;

  /// 深色模式主題
  const EventTimelineTheme.dark()
      : backgroundColor = const Color(0xFF1E1E1E),
        headerColor = const Color(0xFF2D2D2D),
        headerTextColor = Colors.white,
        textColor = Colors.white,
        lineColor = const Color(0xFF6200EA),
        averageLineColor = const Color(0xFF03DAC6),
        dotColor = const Color(0xFF6200EA),
        dotBorderColor = const Color(0xFF2D2D2D),
        highlightColor = const Color(0xFFBB86FC),
        gridColor = const Color(0xFF404040),
        borderColor = const Color(0xFF404040),
        tooltipBackgroundColor = const Color(0xFF2D2D2D),
        tooltipTextColor = Colors.white;

  /// 管理後台主題（統一配色）
  const EventTimelineTheme.admin()
      : backgroundColor = Colors.white,
        headerColor = AppColors.royalIndigo,
        headerTextColor = Colors.white,
        textColor = AppColors.textDark,
        lineColor = AppColors.royalIndigo,
        averageLineColor = AppColors.solarAmber,
        dotColor = AppColors.royalIndigo,
        dotBorderColor = Colors.white,
        highlightColor = AppColors.solarAmber,
        gridColor = const Color(0xFFE2E8F0),
        borderColor = const Color(0xFFE2E8F0),
        tooltipBackgroundColor = const Color(0xFFF8FAFC),
        tooltipTextColor = AppColors.textDark;
}

/// 事件時間軸圖表組件
/// 
/// 展示事件分數的變化趨勢，支援縮放和互動操作
class EventTimelineWidget extends StatefulWidget {
  /// 事件時間軸資料
  final EventTimelineData timelineData;
  
  /// 點擊數據點回調
  final Function(DateTime date, DailyEventScore? score)? onDataPointTapped;
  
  /// 主題配色方案
  final EventTimelineTheme theme;
  
  /// 圖表高度
  final double height;
  
  /// 是否顯示網格
  final bool showGrid;
  
  /// 是否顯示平均線
  final bool showAverageLine;
  
  /// 是否顯示數據標籤
  final bool showDataLabels;

  const EventTimelineWidget({
    super.key,
    required this.timelineData,
    this.onDataPointTapped,
    this.theme = const EventTimelineTheme.starmaster(),
    this.height = 300,
    this.showGrid = true,
    this.showAverageLine = true,
    this.showDataLabels = false,
  });

  @override
  State<EventTimelineWidget> createState() => _EventTimelineWidgetState();
}

class _EventTimelineWidgetState extends State<EventTimelineWidget> {
  int? _touchedIndex;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: widget.height,
      decoration: BoxDecoration(
        color: widget.theme.backgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildHeader(),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: _buildChart(),
            ),
          ),
        ],
      ),
    );
  }

  /// 建立標題區域
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: widget.theme.headerColor,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.timeline,
            color: widget.theme.headerTextColor,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              '事件分數時間軸',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: widget.theme.headerTextColor,
              ),
            ),
          ),
          Flexible(
            child: _buildStatistics(),
          ),
        ],
      ),
    );
  }

  /// 建立統計資訊
  Widget _buildStatistics() {
    return Wrap(
      spacing: 12,
      runSpacing: 8,
      children: [
        _buildStatItem('最高', widget.timelineData.maxScore),
        _buildStatItem('平均', widget.timelineData.averageScore),
        _buildStatItem('事件', widget.timelineData.totalEventCount.toDouble(), isCount: true),
      ],
    );
  }

  /// 建立統計項目
  Widget _buildStatItem(String label, double value, {bool isCount = false}) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: widget.theme.headerTextColor.withOpacity(0.8),
          ),
        ),
        Text(
          isCount ? value.toInt().toString() : value.toStringAsFixed(1),
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: widget.theme.headerTextColor,
          ),
        ),
      ],
    );
  }

  /// 建立圖表
  Widget _buildChart() {
    final spots = _generateSpots();

    // 確保間隔值不為零
    final maxScore = widget.timelineData.maxScore;
    final horizontalInterval = maxScore > 0 ? maxScore / 5 : 10.0;
    final verticalInterval = spots.length > 0 ? spots.length / 10 : 1.0;

    return LineChart(
      LineChartData(
        gridData: FlGridData(
          show: widget.showGrid,
          drawVerticalLine: true,
          drawHorizontalLine: true,
          horizontalInterval: horizontalInterval,
          verticalInterval: verticalInterval,
          getDrawingHorizontalLine: (value) => FlLine(
            color: widget.theme.gridColor,
            strokeWidth: 0.5,
          ),
          getDrawingVerticalLine: (value) => FlLine(
            color: widget.theme.gridColor,
            strokeWidth: 0.5,
          ),
        ),
        titlesData: FlTitlesData(
          show: true,
          rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 30,
              interval: verticalInterval,
              getTitlesWidget: _buildBottomTitle,
            ),
          ),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 40,
              interval: horizontalInterval,
              getTitlesWidget: _buildLeftTitle,
            ),
          ),
        ),
        borderData: FlBorderData(
          show: true,
          border: Border.all(
            color: widget.theme.borderColor,
            width: 1,
          ),
        ),
        minX: 0,
        maxX: math.max(spots.length.toDouble() - 1, 1),
        minY: 0,
        maxY: math.max(maxScore * 1.1, 10),
        lineBarsData: [
          _buildMainLine(spots),
          if (widget.showAverageLine) _buildAverageLine(spots),
        ],
        lineTouchData: LineTouchData(
          enabled: true,
          touchCallback: _handleTouch,
          touchTooltipData: LineTouchTooltipData(
            getTooltipColor: (touchedSpot) => widget.theme.tooltipBackgroundColor,
            getTooltipItems: _buildTooltipItems,
          ),
        ),
      ),
    );
  }

  /// 生成數據點
  List<FlSpot> _generateSpots() {
    final spots = <FlSpot>[];

    // 如果沒有資料，返回預設點
    if (widget.timelineData.dailyScores.isEmpty) {
      spots.add(const FlSpot(0, 0));
      spots.add(const FlSpot(1, 0));
      return spots;
    }

    for (int i = 0; i < widget.timelineData.dailyScores.length; i++) {
      final score = widget.timelineData.dailyScores[i];
      spots.add(FlSpot(i.toDouble(), score.totalScore));
    }

    return spots;
  }

  /// 建立主要線條
  LineChartBarData _buildMainLine(List<FlSpot> spots) {
    return LineChartBarData(
      spots: spots,
      isCurved: true,
      color: widget.theme.lineColor,
      barWidth: 2,
      isStrokeCapRound: true,
      dotData: FlDotData(
        show: true,
        getDotPainter: (spot, percent, barData, index) {
          final isHighlighted = index == _touchedIndex;
          return FlDotCirclePainter(
            radius: isHighlighted ? 6 : 3,
            color: isHighlighted ? widget.theme.highlightColor : widget.theme.dotColor,
            strokeWidth: isHighlighted ? 2 : 1,
            strokeColor: widget.theme.dotBorderColor,
          );
        },
      ),
      belowBarData: BarAreaData(
        show: true,
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            widget.theme.lineColor.withOpacity(0.3),
            widget.theme.lineColor.withOpacity(0.1),
          ],
        ),
      ),
    );
  }

  /// 建立平均線
  LineChartBarData _buildAverageLine(List<FlSpot> spots) {
    final averageSpots = spots.map((spot) => 
      FlSpot(spot.x, widget.timelineData.averageScore)
    ).toList();
    
    return LineChartBarData(
      spots: averageSpots,
      isCurved: false,
      color: widget.theme.averageLineColor,
      barWidth: 1,
      dashArray: [5, 5],
      dotData: const FlDotData(show: false),
    );
  }

  /// 建立底部標題
  Widget _buildBottomTitle(double value, TitleMeta meta) {
    if (value.toInt() >= widget.timelineData.dailyScores.length) {
      return const SizedBox.shrink();
    }
    
    final date = widget.timelineData.dailyScores[value.toInt()].date;
    return SideTitleWidget(
      axisSide: meta.axisSide,
      child: Text(
        '${date.month}/${date.day}',
        style: TextStyle(
          fontSize: 10,
          color: widget.theme.textColor,
        ),
      ),
    );
  }

  /// 建立左側標題
  Widget _buildLeftTitle(double value, TitleMeta meta) {
    return SideTitleWidget(
      axisSide: meta.axisSide,
      child: Text(
        value.toInt().toString(),
        style: TextStyle(
          fontSize: 10,
          color: widget.theme.textColor,
        ),
      ),
    );
  }

  /// 處理觸摸事件
  void _handleTouch(FlTouchEvent event, LineTouchResponse? response) {
    if (response?.lineBarSpots?.isNotEmpty == true) {
      final spot = response!.lineBarSpots!.first;
      final index = spot.x.toInt();
      
      setState(() {
        _touchedIndex = index;
      });
      
      if (index < widget.timelineData.dailyScores.length) {
        final dailyScore = widget.timelineData.dailyScores[index];
        widget.onDataPointTapped?.call(dailyScore.date, dailyScore);
      }
    } else {
      setState(() {
        _touchedIndex = null;
      });
    }
  }

  /// 建立工具提示項目
  List<LineTooltipItem> _buildTooltipItems(List<LineBarSpot> touchedSpots) {
    return touchedSpots.map((spot) {
      final index = spot.x.toInt();
      if (index >= widget.timelineData.dailyScores.length) {
        return LineTooltipItem('', const TextStyle());
      }
      
      final dailyScore = widget.timelineData.dailyScores[index];
      final date = dailyScore.date;
      
      return LineTooltipItem(
        '${date.month}/${date.day}\n分數: ${spot.y.toStringAsFixed(1)}\n事件: ${dailyScore.eventCount}個',
        TextStyle(
          color: widget.theme.tooltipTextColor,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      );
    }).toList();
  }
}
