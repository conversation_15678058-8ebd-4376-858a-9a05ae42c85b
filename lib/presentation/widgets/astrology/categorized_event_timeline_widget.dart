import 'dart:math' as math;

import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';

import '../../../data/models/astrology/categorized_event_score.dart';
import '../../../data/models/astrology/categorized_timeline_data.dart';
import '../../themes/app_theme.dart';

/// 分類事件時間軸篩選器
class EventTimelineFilter {
  /// 啟用的事件類別
  final Set<EventCategory> enabledCategories;

  /// 啟用的星盤類型
  final Set<String> enabledChartTypes;

  /// 是否顯示總分線
  final bool showTotalScore;

  const EventTimelineFilter({
    required this.enabledCategories,
    required this.enabledChartTypes,
    this.showTotalScore = true,
  });

  /// 預設篩選器（顯示所有類別）
  factory EventTimelineFilter.all() {
    return EventTimelineFilter(
      enabledCategories: Set.from(EventCategory.values),
      enabledChartTypes: {'transit', 'progression', 'solar_arc'},
      showTotalScore: true,
    );
  }

  /// 複製並修改篩選器
  EventTimelineFilter copyWith({
    Set<EventCategory>? enabledCategories,
    Set<String>? enabledChartTypes,
    bool? showTotalScore,
  }) {
    return EventTimelineFilter(
      enabledCategories: enabledCategories ?? this.enabledCategories,
      enabledChartTypes: enabledChartTypes ?? this.enabledChartTypes,
      showTotalScore: showTotalScore ?? this.showTotalScore,
    );
  }
}

/// 分類事件時間軸圖表組件
class CategorizedEventTimelineWidget extends StatefulWidget {
  /// 分類事件時間軸資料
  final CategorizedTimelineData timelineData;

  /// 點擊數據點回調
  final Function(DateTime date, CategorizedEventScore? score)?
      onDataPointTapped;

  /// 圖表高度
  final double height;

  /// 是否顯示網格
  final bool showGrid;

  /// 是否顯示圖例
  final bool showLegend;

  /// 初始篩選器
  final EventTimelineFilter initialFilter;

  const CategorizedEventTimelineWidget({
    super.key,
    required this.timelineData,
    this.onDataPointTapped,
    this.height = 400,
    this.showGrid = true,
    this.showLegend = true,
    this.initialFilter = const EventTimelineFilter(
      enabledCategories: {},
      enabledChartTypes: {},
    ),
  });

  @override
  State<CategorizedEventTimelineWidget> createState() =>
      _CategorizedEventTimelineWidgetState();
}

class _CategorizedEventTimelineWidgetState
    extends State<CategorizedEventTimelineWidget> {
  late EventTimelineFilter _filter;
  int? _touchedIndex;

  @override
  void initState() {
    super.initState();
    // 如果初始篩選器為空，使用所有啟用的類別
    if (widget.initialFilter.enabledCategories.isEmpty) {
      _filter = EventTimelineFilter(
        enabledCategories: Set.from(widget.timelineData.getActiveCategories()),
        enabledChartTypes: widget.initialFilter.enabledChartTypes,
        showTotalScore: widget.initialFilter.showTotalScore,
      );
    } else {
      _filter = widget.initialFilter;
    }
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        children: [
          // 篩選器控制面板
          _buildFilterPanel(),
          const SizedBox(height: 16),

          // 圖表
          SizedBox(
            height: widget.height,
            child: _buildChart(),
          ),

          // 圖例
          if (widget.showLegend) ...[
            const SizedBox(height: 16),
            _buildLegend(),
          ],
          const SizedBox(height: 50),
        ],
      ),
    );
  }

  /// 建立篩選器控制面板
  Widget _buildFilterPanel() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '事件類型篩選',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
            ),
            const SizedBox(height: 12),

            // 事件類別篩選
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: EventCategory.values.map((category) {
                final isEnabled = _filter.enabledCategories.contains(category);
                final hasEvents = widget.timelineData
                        .getTotalEventCountForCategory(category) >
                    0;

                return FilterChip(
                  label: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        category.icon,
                        size: 16,
                        color: hasEvents ? category.color : Colors.grey,
                      ),
                      const SizedBox(width: 4),
                      Text(category.displayName),
                      const SizedBox(width: 4),
                      Text(
                        '(${widget.timelineData.getTotalEventCountForCategory(category)})',
                        style: const TextStyle(fontSize: 12),
                      ),
                    ],
                  ),
                  selected: isEnabled,
                  onSelected: hasEvents
                      ? (selected) {
                          setState(() {
                            if (selected) {
                              _filter = _filter.copyWith(
                                enabledCategories: {
                                  ..._filter.enabledCategories,
                                  category
                                },
                              );
                            } else {
                              _filter = _filter.copyWith(
                                enabledCategories: _filter.enabledCategories
                                    .where((c) => c != category)
                                    .toSet(),
                              );
                            }
                          });
                        }
                      : null,
                  backgroundColor: hasEvents ? null : Colors.grey.shade200,
                  selectedColor: AppColors.royalIndigo.withValues(alpha: 0.1),
                );
              }).toList(),
            ),

            const SizedBox(height: 0),

            // 總分線開關
            SwitchListTile(
              title: const Text('顯示總分線'),
              value: _filter.showTotalScore,
              onChanged: (value) {
                setState(() {
                  _filter = _filter.copyWith(showTotalScore: value);
                });
              },
              dense: true,
            ),
          ],
        ),
      ),
    );
  }

  /// 建立圖表
  Widget _buildChart() {
    final lines = _generateLines();
    if (lines.isEmpty) {
      return const Center(
        child: Text('請選擇要顯示的事件類型'),
      );
    }

    // 計算最大值用於設定Y軸範圍
    double maxY = 0.0;
    for (final category in _filter.enabledCategories) {
      final categoryMax = widget.timelineData.getMaxScoreForCategory(category);
      if (categoryMax > maxY) maxY = categoryMax;
    }

    if (_filter.showTotalScore) {
      final totalMax = widget.timelineData.dailyScores
          .map((score) => score.totalScore)
          .fold<double>(0.0, (max, score) => math.max(max, score));
      if (totalMax > maxY) maxY = totalMax;
    }

    return LineChart(
      LineChartData(
        gridData: FlGridData(
          show: widget.showGrid,
          drawVerticalLine: true,
          drawHorizontalLine: true,
          horizontalInterval: maxY > 0 ? maxY / 5 : 10.0,
          verticalInterval: widget.timelineData.dailyScores.length > 0
              ? widget.timelineData.dailyScores.length / 10
              : 1.0,
          getDrawingHorizontalLine: (value) => FlLine(
            color: Colors.grey.shade300,
            strokeWidth: 0.5,
          ),
          getDrawingVerticalLine: (value) => FlLine(
            color: Colors.grey.shade300,
            strokeWidth: 0.5,
          ),
        ),
        titlesData: FlTitlesData(
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              getTitlesWidget: _buildBottomTitle,
              interval:
                  math.max(1, widget.timelineData.dailyScores.length / 10),
            ),
          ),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              getTitlesWidget: _buildLeftTitle,
              interval: maxY > 0 ? maxY / 5 : 10.0,
            ),
          ),
          topTitles:
              const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          rightTitles:
              const AxisTitles(sideTitles: SideTitles(showTitles: false)),
        ),
        borderData: FlBorderData(
          show: true,
          border: Border.all(color: Colors.grey.shade300),
        ),
        minX: 0,
        maxX:
            math.max(widget.timelineData.dailyScores.length.toDouble() - 1, 1),
        minY: 0,
        maxY: math.max(maxY * 1.1, 10),
        lineBarsData: lines,
        lineTouchData: LineTouchData(
          enabled: true,
          touchCallback: _handleTouch,
          touchTooltipData: LineTouchTooltipData(
            getTooltipColor: (touchedSpot) => Colors.black87,
            getTooltipItems: _buildTooltipItems,
          ),
        ),
      ),
    );
  }

  /// 生成線條資料
  List<LineChartBarData> _generateLines() {
    final lines = <LineChartBarData>[];

    // 各類別線條
    for (final category in _filter.enabledCategories) {
      final spots = <FlSpot>[];
      for (int i = 0; i < widget.timelineData.dailyScores.length; i++) {
        final score =
            widget.timelineData.dailyScores[i].getScoreForCategory(category);
        spots.add(FlSpot(i.toDouble(), score));
      }

      lines.add(LineChartBarData(
        spots: spots,
        isCurved: true,
        color: category.color,
        barWidth: 2,
        dotData: FlDotData(
          show: true,
          getDotPainter: (spot, percent, barData, index) {
            return FlDotCirclePainter(
              radius: 3,
              color: category.color,
              strokeWidth: 1,
              strokeColor: Colors.white,
            );
          },
        ),
      ));
    }

    // 總分線
    if (_filter.showTotalScore) {
      final totalSpots = <FlSpot>[];
      for (int i = 0; i < widget.timelineData.dailyScores.length; i++) {
        final score = widget.timelineData.dailyScores[i].totalScore;
        totalSpots.add(FlSpot(i.toDouble(), score));
      }

      lines.add(LineChartBarData(
        spots: totalSpots,
        isCurved: true,
        color: Colors.black,
        barWidth: 3,
        dashArray: [5, 5],
        dotData: const FlDotData(show: false),
      ));
    }

    return lines;
  }

  /// 建立圖例
  Widget _buildLegend() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '圖例',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 16,
              runSpacing: 8,
              children: [
                // 各類別圖例
                ..._filter.enabledCategories.map((category) {
                  return Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        width: 16,
                        height: 3,
                        decoration: BoxDecoration(
                          color: category.color,
                          borderRadius: BorderRadius.circular(1.5),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Icon(category.icon, size: 16, color: category.color),
                      const SizedBox(width: 4),
                      Text(
                        category.displayName,
                        style: const TextStyle(fontSize: 12),
                      ),
                    ],
                  );
                }),

                // 總分線圖例
                if (_filter.showTotalScore)
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        width: 16,
                        height: 3,
                        decoration: BoxDecoration(
                          color: Colors.black,
                          borderRadius: BorderRadius.circular(1.5),
                        ),
                        child: CustomPaint(
                          painter: DashedLinePainter(),
                        ),
                      ),
                      const SizedBox(width: 8),
                      const Icon(Icons.timeline, size: 16, color: Colors.black),
                      const SizedBox(width: 4),
                      const Text(
                        '總分',
                        style: TextStyle(fontSize: 12),
                      ),
                    ],
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 建立底部標題
  Widget _buildBottomTitle(double value, TitleMeta meta) {
    if (value.toInt() >= widget.timelineData.dailyScores.length) {
      return const SizedBox.shrink();
    }

    final date = widget.timelineData.dailyScores[value.toInt()].date;
    return SideTitleWidget(
      axisSide: meta.axisSide,
      child: Text(
        '${date.month}/${date.day}',
        style: const TextStyle(
          fontSize: 10,
          color: Colors.grey,
        ),
      ),
    );
  }

  /// 建立左側標題
  Widget _buildLeftTitle(double value, TitleMeta meta) {
    return SideTitleWidget(
      axisSide: meta.axisSide,
      child: Text(
        value.toInt().toString(),
        style: const TextStyle(
          fontSize: 10,
          color: Colors.grey,
        ),
      ),
    );
  }

  /// 處理觸摸事件
  void _handleTouch(FlTouchEvent event, LineTouchResponse? response) {
    if (response?.lineBarSpots?.isNotEmpty == true) {
      final spot = response!.lineBarSpots!.first;
      final index = spot.x.toInt();

      setState(() {
        _touchedIndex = index;
      });

      if (index < widget.timelineData.dailyScores.length) {
        final categorizedScore = widget.timelineData.dailyScores[index];
        widget.onDataPointTapped?.call(categorizedScore.date, categorizedScore);
      }
    } else {
      setState(() {
        _touchedIndex = null;
      });
    }
  }

  /// 建立工具提示項目
  List<LineTooltipItem> _buildTooltipItems(List<LineBarSpot> touchedSpots) {
    if (touchedSpots.isEmpty) return [];

    final index = touchedSpots.first.x.toInt();
    if (index >= widget.timelineData.dailyScores.length) {
      return [LineTooltipItem('', const TextStyle())];
    }

    final categorizedScore = widget.timelineData.dailyScores[index];
    final date = categorizedScore.date;

    final items = <LineTooltipItem>[];

    // 日期標題
    items.add(LineTooltipItem(
      '${date.month}/${date.day}\n',
      const TextStyle(
        color: Colors.white,
        fontSize: 12,
        fontWeight: FontWeight.bold,
      ),
    ));

    // 各類別分數
    for (final category in _filter.enabledCategories) {
      final score = categorizedScore.getScoreForCategory(category);
      final count = categorizedScore.getCountForCategory(category);

      if (score > 0) {
        items.add(LineTooltipItem(
          '${category.displayName}: ${score.toStringAsFixed(1)} (${count}個)\n',
          TextStyle(
            color: category.color,
            fontSize: 11,
          ),
        ));
      }
    }

    // 總分
    if (_filter.showTotalScore) {
      items.add(LineTooltipItem(
        '總分: ${categorizedScore.totalScore.toStringAsFixed(1)}',
        const TextStyle(
          color: Colors.white,
          fontSize: 11,
          fontWeight: FontWeight.w500,
        ),
      ));
    }

    return items;
  }
}

/// 虛線繪製器
class DashedLinePainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.black
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    const dashWidth = 3.0;
    const dashSpace = 2.0;
    double startX = 0;

    while (startX < size.width) {
      canvas.drawLine(
        Offset(startX, size.height / 2),
        Offset(startX + dashWidth, size.height / 2),
        paint,
      );
      startX += dashWidth + dashSpace;
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
