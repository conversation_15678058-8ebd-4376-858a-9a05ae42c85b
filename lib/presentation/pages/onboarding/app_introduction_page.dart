import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import '../../../core/utils/logger_utils.dart';
import '../../../data/models/app_introduction_data.dart';
import '../../../shared/utils/user_preferences.dart';
import '../../../shared/widgets/common/responsive_wrapper.dart';
import '../../themes/app_theme.dart';

/// 應用程式介紹頁面
/// 在用戶首次啟動應用時顯示，介紹應用功能和特色
class AppIntroductionPage extends StatefulWidget {
  const AppIntroductionPage({super.key});

  @override
  State<AppIntroductionPage> createState() => _AppIntroductionPageState();
}

class _AppIntroductionPageState extends State<AppIntroductionPage>
    with TickerProviderStateMixin {
  late PageController _pageController;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  int _currentPage = 0;
  final List<IntroductionPageData> _pages =
      AppIntroductionConfig.getIntroductionPages();

  @override
  void initState() {
    super.initState();
    _pageController = PageController();

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _pageController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  /// 跳到下一頁
  void _nextPage() {
    if (_currentPage < _pages.length - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      _completeIntroduction();
    }
  }

  /// 跳到上一頁
  void _previousPage() {
    if (_currentPage > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  /// 跳過介紹
  void _skipIntroduction() {
    _completeIntroduction();
  }

  /// 跳轉到指定頁面
  void _goToPage(int pageIndex) {
    if (pageIndex >= 0 && pageIndex < _pages.length) {
      _pageController.animateToPage(
        pageIndex,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
      logger.i('跳轉到第 ${pageIndex + 1} 頁');
    }
  }

  /// 完成介紹流程
  Future<void> _completeIntroduction() async {
    try {
      // 標記首次啟動流程已完成
      await UserPreferences.markFirstTimeUserCompleted();

      logger.i('用戶完成應用介紹流程');

      if (mounted) {
        // 導航到模式選擇頁面
        Navigator.pushReplacementNamed(context, '/mode-selection');
      }
    } catch (e) {
      logger.e('完成介紹流程失敗: $e');
      // 即使失敗也要導航，避免用戶卡住
      if (mounted) {
        Navigator.pushReplacementNamed(context, '/mode-selection');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          color: AppColors.scaffoldBackground,
        ),
        child: SafeArea(
          child: ResponsivePageWrapper(
            maxWidth: 600.0,
            child: Column(
              children: [
                // 頂部導航區域
                _buildTopNavigation(),

                // 主要內容區域
                Expanded(
                  child: PageView.builder(
                    controller: _pageController,
                    onPageChanged: (index) {
                      setState(() {
                        _currentPage = index;
                      });
                      // 重新播放動畫
                      _animationController.reset();
                      _animationController.forward();
                    },
                    itemCount: _pages.length,
                    itemBuilder: (context, index) {
                      return _buildIntroductionPage(_pages[index]);
                    },
                  ),
                ),

                // 底部導航區域
                _buildBottomNavigation(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 檢測是否為 PWA 環境
  bool get _isPWAEnvironment {
    if (!kIsWeb) return false;
    // 在 Web 環境中，PWA 模式通常有特定的特徵
    // 這裡使用簡單的檢測邏輯
    return kIsWeb;
  }

  /// PWA 專用按鈕處理
  void _handlePWAButtonPress(VoidCallback onPressed, String debugName) {
    logger.d('$debugName PWA button press handler triggered');

    // 立即執行
    onPressed();

    // PWA 環境中的額外保險觸發
    if (_isPWAEnvironment) {
      Future.delayed(const Duration(milliseconds: 100), () {
        logger.d('$debugName PWA delayed execution');
        // 這裡不再次執行 onPressed，避免重複操作
      });
    }
  }

  /// 構建優化的按鈕（直接創建，避免嵌套）
  Widget _buildOptimizedButton({
    required String text,
    required VoidCallback onPressed,
    required Color backgroundColor,
    required String debugName,
    bool isOutlined = false,
  }) {
    return SizedBox(
      width: double.infinity,
      height: 50,
      child: GestureDetector(
        onTap: () {
          logger.d('$debugName button tapped');
          _handlePWAButtonPress(onPressed, debugName);
        },
        behavior: HitTestBehavior.opaque,
        child: Container(
          decoration: BoxDecoration(
            color: isOutlined ? Colors.transparent : backgroundColor,
            border: isOutlined
                ? Border.all(color: backgroundColor, width: 2)
                : null,
            borderRadius: BorderRadius.circular(25),
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: () {
                logger.d('$debugName InkWell tapped');
                _handlePWAButtonPress(onPressed, debugName);
              },
              borderRadius: BorderRadius.circular(25),
              child: Container(
                alignment: Alignment.center,
                child: Text(
                  text,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: isOutlined ? backgroundColor : Colors.white,
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 構建簡化的文字按鈕
  Widget _buildTextButton({
    required String text,
    required VoidCallback onPressed,
    required Color textColor,
    required String debugName,
    bool isSmallScreen = false,
  }) {
    return GestureDetector(
      onTap: () {
        logger.d('$debugName text button tapped');
        _handlePWAButtonPress(onPressed, debugName);
      },
      behavior: HitTestBehavior.opaque,
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: isSmallScreen ? 8 : 12,
          vertical: isSmallScreen ? 6 : 8,
        ),
        child: Text(
          text,
          style: TextStyle(
            color: textColor,
            fontSize: isSmallScreen ? 14 : 16,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  /// 構建頂部導航
  Widget _buildTopNavigation() {
    final screenHeight = MediaQuery.of(context).size.height;
    final isSmallScreen = screenHeight < 700;

    return Container(
      // 使用 Container 提供背景色和陰影
      decoration: BoxDecoration(
        color: AppColors.scaffoldBackground,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      padding: EdgeInsets.symmetric(
        horizontal: 16.0,
        vertical: isSmallScreen ? 8.0 : 16.0,
      ),
      child: SafeArea(
        bottom: false, // 只保護頂部
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // 返回按鈕（第一頁時隱藏）
            SizedBox(
              width: isSmallScreen ? 70 : 80,
              child: _currentPage > 0
                  ? _buildTextButton(
                      text: '上一步',
                      onPressed: _previousPage,
                      textColor: AppColors.royalIndigo,
                      debugName: 'Previous',
                      isSmallScreen: isSmallScreen,
                    )
                  : null,
            ),

            // 頁面指示器
            _buildPageIndicator(),

            // 跳過按鈕（最後一頁時隱藏）
            SizedBox(
              width: isSmallScreen ? 50 : 60,
              child: _currentPage < _pages.length - 1
                  ? _buildTextButton(
                      text: '跳過',
                      onPressed: _skipIntroduction,
                      textColor: AppColors.textMedium,
                      debugName: 'Skip',
                      isSmallScreen: isSmallScreen,
                    )
                  : null,
            ),
          ],
        ),
      ),
    );
  }

  /// 構建頁面指示器
  Widget _buildPageIndicator() {
    final screenHeight = MediaQuery.of(context).size.height;
    final isSmallScreen = screenHeight < 700;

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(_pages.length, (index) {
        final isActive = _currentPage == index;
        return GestureDetector(
          onTap: () {
            logger.d('Page indicator $index tapped');
            _goToPage(index);
          },
          behavior: HitTestBehavior.opaque,
          child: Container(
            margin: EdgeInsets.symmetric(horizontal: isSmallScreen ? 4 : 6),
            padding: EdgeInsets.all(isSmallScreen ? 3 : 4), // 增加觸控區域
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              width: isActive ? (isSmallScreen ? 16 : 18) : (isSmallScreen ? 10 : 12),
              height: isActive ? (isSmallScreen ? 16 : 18) : (isSmallScreen ? 10 : 12),
              decoration: BoxDecoration(
                color: isActive
                    ? _pages[_currentPage].primaryColor
                    : AppColors.textLight.withValues(alpha: 0.5),
                shape: BoxShape.circle, // 關鍵：改成圓形
                boxShadow: isActive
                    ? [
                        BoxShadow(
                          color: _pages[_currentPage]
                              .primaryColor
                              .withValues(alpha: 0.4),
                          blurRadius: isSmallScreen ? 4 : 6,
                          offset: const Offset(0, 2),
                        )
                      ]
                    : [],
              ),
              child: isActive
                  ? Center(
                      child: Text(
                        '${index + 1}',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: isSmallScreen ? 8 : 10,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    )
                  : null,
            ),
          ),
        );
      }),
    );
  }

  /// 構建介紹頁面內容
  Widget _buildIntroductionPage(IntroductionPageData pageData) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: LayoutBuilder(
          builder: (context, constraints) {
            final screenHeight = MediaQuery.of(context).size.height;
            final isSmallScreen = screenHeight < 700; // 小螢幕判斷
            final isVerySmallScreen = screenHeight < 600; // 極小螢幕判斷

            return SingleChildScrollView(
              padding: EdgeInsets.symmetric(
                horizontal: 16.0,
                vertical: isSmallScreen ? 8.0 : 16.0,
              ),
              child: Column(
                children: [
                  // 圖標區域 - 響應式大小
                  Container(
                    width: isVerySmallScreen ? 70 : (isSmallScreen ? 80 : 100),
                    height: isVerySmallScreen ? 70 : (isSmallScreen ? 80 : 100),
                    decoration: BoxDecoration(
                      color: pageData.primaryColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(
                        isVerySmallScreen ? 35 : (isSmallScreen ? 40 : 50),
                      ),
                    ),
                    child: Icon(
                      pageData.icon,
                      size: isVerySmallScreen ? 35 : (isSmallScreen ? 40 : 50),
                      color: pageData.primaryColor,
                    ),
                  ),

                  SizedBox(height: isVerySmallScreen ? 12 : (isSmallScreen ? 16 : 24)),

                  // 標題 - 響應式字體大小
                  Text(
                    pageData.title,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: isVerySmallScreen ? 22 : (isSmallScreen ? 24 : 28),
                      fontWeight: FontWeight.bold,
                      color: pageData.primaryColor,
                    ),
                  ),

                  SizedBox(height: isVerySmallScreen ? 6 : (isSmallScreen ? 8 : 12)),

                  // 副標題 - 響應式字體大小
                  Text(
                    pageData.subtitle,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: isVerySmallScreen ? 14 : (isSmallScreen ? 16 : 18),
                      fontWeight: FontWeight.w500,
                      color: AppColors.textDark,
                    ),
                  ),

                  SizedBox(height: isVerySmallScreen ? 8 : (isSmallScreen ? 12 : 16)),

                  // 描述 - 響應式字體大小
                  Text(
                    pageData.description,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: isVerySmallScreen ? 13 : (isSmallScreen ? 14 : 16),
                      color: AppColors.textMedium,
                      height: 1.4,
                    ),
                  ),

                  SizedBox(height: isVerySmallScreen ? 12 : (isSmallScreen ? 16 : 24)),

                  // 功能列表 - 使用 Column 而非 Expanded + SingleChildScrollView
                  Column(
                    children: pageData.features.map((feature) {
                      return _buildFeatureCard(feature, isSmallScreen: isSmallScreen, isVerySmallScreen: isVerySmallScreen);
                    }).toList(),
                  ),

                  // 底部額外間距，確保內容不會被底部按鈕遮擋
                  SizedBox(height: isSmallScreen ? 80 : 100),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  /// 構建功能卡片
  Widget _buildFeatureCard(AppFeature feature, {bool isSmallScreen = false, bool isVerySmallScreen = false}) {
    return Container(
      margin: EdgeInsets.only(bottom: isVerySmallScreen ? 8 : (isSmallScreen ? 12 : 16)),
      padding: EdgeInsets.all(isVerySmallScreen ? 12 : (isSmallScreen ? 14 : 16)),
      decoration: BoxDecoration(
        color: AppColors.cardBackground,
        borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: isSmallScreen ? 6 : 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // 功能圖標 - 響應式大小
          Container(
            width: isVerySmallScreen ? 36 : (isSmallScreen ? 40 : 48),
            height: isVerySmallScreen ? 36 : (isSmallScreen ? 40 : 48),
            decoration: BoxDecoration(
              color: feature.color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(isSmallScreen ? 8 : 12),
            ),
            child: Icon(
              feature.icon,
              size: isVerySmallScreen ? 18 : (isSmallScreen ? 20 : 24),
              color: feature.color,
            ),
          ),

          SizedBox(width: isSmallScreen ? 12 : 16),

          // 功能內容 - 響應式字體大小
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  feature.title,
                  style: TextStyle(
                    fontSize: isVerySmallScreen ? 13 : (isSmallScreen ? 14 : 16),
                    fontWeight: FontWeight.bold,
                    color: AppColors.textDark,
                  ),
                ),
                SizedBox(height: isSmallScreen ? 2 : 4),
                Text(
                  feature.description,
                  style: TextStyle(
                    fontSize: isVerySmallScreen ? 11 : (isSmallScreen ? 12 : 14),
                    color: AppColors.textMedium,
                    height: 1.3,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 構建底部導航
  Widget _buildBottomNavigation() {
    final isLastPage = _currentPage == _pages.length - 1;
    final screenHeight = MediaQuery.of(context).size.height;
    final isSmallScreen = screenHeight < 700;

    return Container(
      // 使用 Container 而非 Padding，提供背景色避免內容透過
      decoration: BoxDecoration(
        color: AppColors.scaffoldBackground,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      padding: EdgeInsets.symmetric(
        horizontal: 16.0,
        vertical: isSmallScreen ? 12.0 : 16.0,
      ),
      child: SafeArea(
        top: false, // 只保護底部
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 主要按鈕
            _buildOptimizedButton(
              text: isLastPage ? '開始使用' : '下一步',
              onPressed: isLastPage ? _completeIntroduction : _nextPage,
              backgroundColor: _pages[_currentPage].primaryColor,
              debugName: isLastPage ? 'Complete' : 'Next',
            ),
          ],
        ),
      ),
    );
  }
}
