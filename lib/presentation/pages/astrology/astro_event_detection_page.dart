import 'package:flutter/material.dart' as material;
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../data/models/astrology/astro_event.dart';
import '../../../data/models/astrology/categorized_event_score.dart';
import '../../../data/models/astrology/categorized_timeline_data.dart';
import '../../../data/models/astrology/event_detection_config.dart';
import '../../../data/models/astrology/event_score.dart';
import '../../../data/models/astrology/event_timeline_data.dart';
import '../../../data/models/user/birth_data.dart';
import '../../../data/services/api/birth_data_service.dart';
import '../../../features/astrology/services/event_detection_service.dart';
import '../../../presentation/themes/app_theme.dart';
import '../../../shared/utils/logger_utils.dart';
import '../../widgets/astrology/astro_event_calendar_widget.dart';
import '../../widgets/astrology/categorized_event_timeline_widget.dart';
import '../../widgets/astrology/event_detail_panel.dart';
import '../../widgets/astrology/event_detection_settings_dialog.dart';
import '../../widgets/astrology/event_timeline_widget.dart';


/// 占星事件偵測主頁面
///
/// 整合年曆熱度圖、時間軸圖表和事件詳情面板
class AstroEventDetectionPage extends StatefulWidget {
  /// 出生資料
  final BirthData birthData;

  const AstroEventDetectionPage({
    super.key,
    required this.birthData,
  });

  @override
  State<AstroEventDetectionPage> createState() =>
      _AstroEventDetectionPageState();
}

class _AstroEventDetectionPageState extends State<AstroEventDetectionPage>
    with TickerProviderStateMixin {
  late TabController _tabController;

  EventTimelineData? _timelineData;
  CategorizedTimelineData? _categorizedTimelineData;
  DateTime? _selectedDate;
  DailyEventScore? _selectedDayScore;
  CategorizedEventScore? _selectedCategorizedScore;
  List<AstroEvent> _selectedDayEvents = [];

  bool _isLoading = true;
  String? _errorMessage;

  // 時間軸視圖模式
  bool _useAdvancedTimeline = false;

  late EventDetectionService _eventDetectionService;

  // 日期範圍
  late DateTime _startDate;
  late DateTime _endDate;

  // 選中的用戶資訊
  BirthData? _selectedUser;
  bool _isDateRangeSet = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);

    // 初始化事件偵測服務（使用優化的預設配置）
    _eventDetectionService = EventDetectionService(
      config: _createOptimizedDefaultConfig(),
    );

    // 延遲初始化，讓頁面先顯示
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await _initializePageData();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: _buildBody(),
    );
  }

  /// 初始化頁面資料
  Future<void> _initializePageData() async {
    try {
      // 1. 載入選中的用戶資訊
      await _loadSelectedUser();

      // 2. 設定日期範圍（如果還沒設定）
      if (!_isDateRangeSet) {
        await _showDateRangeSelector();
      }

      // 3. 載入事件資料
      if (_selectedUser != null && _isDateRangeSet) {
        await _loadEventData();
      }
    } catch (e) {
      logger.e('初始化頁面資料失敗: $e');
      setState(() {
        _errorMessage = '初始化失敗: $e';
        _isLoading = false;
      });
    }
  }

  /// 載入選中的用戶資訊
  Future<void> _loadSelectedUser() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final selectedPersonId = prefs.getString('selected_person_id');

      if (selectedPersonId != null) {
        final birthDataService = BirthDataService();
        final allUsers = await birthDataService.getAllBirthData();
        try {
          _selectedUser = allUsers.firstWhere(
            (user) => user.id == selectedPersonId,
          );
        } catch (e) {
          _selectedUser = allUsers.isNotEmpty ? allUsers.first : null;
        }
      }

      // 如果沒有選中用戶，使用第一個用戶
      if (_selectedUser == null) {
        final birthDataService = BirthDataService();
        final allUsers = await birthDataService.getAllBirthData();
        if (allUsers.isNotEmpty) {
          _selectedUser = allUsers.first;
        }
      }

      setState(() {});
    } catch (e) {
      logger.e('載入用戶資訊失敗: $e');
    }
  }

  /// 創建優化的預設配置（預設只啟用行運盤）
  EventDetectionConfig _createOptimizedDefaultConfig() {
    return EventDetectionConfig(
      enabledEventTypes: {
        AstroEventType.transitAspect, // 預設只啟用行運盤
      },
      detectionRangeDays: 365,
      minimumEventScore: 20.0,
      enablePersonalization: true,
      progressionSettings: ProgressionSettings.defaultSettings(),
      aspectSettings: AspectSettings.defaultSettings(),
      planetSettings: PlanetSettings.defaultSettings(),
      houseSettings: HouseSettings.defaultSettings(),
    );
  }



  /// 建立應用欄（使用管理後台配色）
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: const Text(
        '占星事件偵測',
        style: TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.bold,
          color: AppColors.textDark,
        ),
      ),
      backgroundColor: AppColors.pastelSkyBlue,
      elevation: 0,
      iconTheme: const IconThemeData(color: AppColors.textDark),
      actions: [
        IconButton(
          icon: const Icon(Icons.settings),
          onPressed: _showSettings,
          tooltip: '設定',
        ),
        IconButton(
          icon: const Icon(Icons.refresh),
          onPressed: _isLoading ? null : _loadEventData,
          tooltip: '重新載入',
        ),
      ],
      bottom: TabBar(
        controller: _tabController,
        indicatorColor: AppColors.royalIndigo,
        labelColor: AppColors.royalIndigo,
        unselectedLabelColor: AppColors.textSecondary,
        labelStyle: const TextStyle(
          fontWeight: FontWeight.w600,
          fontSize: 14,
        ),
        unselectedLabelStyle: const TextStyle(
          fontWeight: FontWeight.w500,
          fontSize: 14,
        ),
        tabs: const [
          Tab(
            icon: Icon(Icons.calendar_view_month),
            text: '年曆熱度圖',
          ),
          Tab(
            icon: Icon(Icons.timeline),
            text: '時間軸圖表',
          ),
          Tab(
            icon: Icon(Icons.multiline_chart),
            text: '分類時間軸',
          ),
        ],
      ),
    );
  }

  /// 建立主體內容
  Widget _buildBody() {
    if (_isLoading) {
      return _buildLoadingState();
    }

    if (_errorMessage != null) {
      return _buildErrorState();
    }

    if (_timelineData == null) {
      return _buildEmptyState();
    }

    return Stack(
      children: [
        Column(
          children: [
            _buildUserInfoSection(),
            _buildDateRangeSelector(),
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildCalendarView(),
                  _buildTimelineView(),
                  _buildCategorizedTimelineView(),
                ],
              ),
            ),
          ],
        ),
        if (_selectedDate != null) _buildEventDetailPanel(),
      ],
    );
  }

  /// 建立載入狀態
  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const SizedBox(
            width: 60,
            height: 60,
            child: CircularProgressIndicator(
              strokeWidth: 3,
            ),
          ),
          const SizedBox(height: 24),
          const Text(
            '正在分析占星事件...',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            '分析期間: ${_startDate.year}/${_startDate.month}/${_startDate.day} - ${_endDate.year}/${_endDate.month}/${_endDate.day}',
            style: const TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
          ),
          // const SizedBox(height: 16),
          // const Text(
          //   '首次載入可能需要較長時間\n後續載入會使用快取加速',
          //   textAlign: TextAlign.center,
          //   style: TextStyle(
          //     fontSize: 12,
          //     color: Colors.grey,
          //   ),
          // ),
        ],
      ),
    );
  }

  /// 建立錯誤狀態
  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red,
          ),
          const SizedBox(height: 16),
          Text(
            '載入事件資料時發生錯誤',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          Text(
            _errorMessage ?? '未知錯誤',
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: _loadEventData,
            child: const Text('重新載入'),
          ),
        ],
      ),
    );
  }

  /// 建立空狀態
  Widget _buildEmptyState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.event_busy,
            size: 64,
            color: Colors.grey,
          ),
          SizedBox(height: 16),
          Text('沒有找到事件資料'),
        ],
      ),
    );
  }

  /// 建立用戶資訊區域
  Widget _buildUserInfoSection() {
    if (_selectedUser == null) {
      return Container(
        margin: const EdgeInsets.only(right: 16, left: 16, top: 16, bottom: 0),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.orange.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
        ),
        child: Row(
          children: [
            Icon(
              Icons.warning_amber_rounded,
              color: Colors.orange,
              size: 20,
            ),
            const SizedBox(width: 12),
            const Expanded(
              child: Text(
                '請選擇要分析的用戶',
                style: TextStyle(
                  fontWeight: FontWeight.w500,
                  color: Colors.orange,
                ),
              ),
            ),
            TextButton(
              onPressed: _selectUser,
              child: const Text('選擇用戶'),
            ),
          ],
        ),
      );
    }

    return Container(
      margin: const EdgeInsets.only(right: 16, left: 16, top: 16, bottom: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.royalIndigo.withValues(alpha: 0.2)),
        boxShadow: [
          BoxShadow(
            color: AppColors.royalIndigo.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: AppColors.royalIndigo.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Icon(
              Icons.person,
              color: AppColors.royalIndigo,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _selectedUser!.name,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                    color: AppColors.textDark,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  '${_selectedUser!.dateTime.year}/${_selectedUser!.dateTime.month}/${_selectedUser!.dateTime.day} ${_selectedUser!.birthPlace}',
                  style: const TextStyle(
                    fontSize: 12,
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(width: 12),
          TextButton(
            onPressed: _selectUser,
            style: TextButton.styleFrom(
              foregroundColor: AppColors.royalIndigo,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            ),
            child: const Text(
              '更換',
              style: TextStyle(fontWeight: FontWeight.w600),
            ),
          ),
        ],
      ),
    );
  }

  /// 建立日期範圍選擇器
  Widget _buildDateRangeSelector() {
    return Container(
      margin: const EdgeInsets.only(right: 16, left: 16, top: 8, bottom: 0),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.royalIndigo.withValues(alpha: 0.2)),
        boxShadow: [
          BoxShadow(
            color: AppColors.royalIndigo.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Icon(
            Icons.date_range,
            color: AppColors.royalIndigo,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              '分析期間: ${_startDate.year}/${_startDate.month}/${_startDate.day} - ${_endDate.year}/${_endDate.month}/${_endDate.day}',
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                color: AppColors.textDark,
                fontSize: 14,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
          const SizedBox(width: 12),
          TextButton(
            onPressed: _selectDateRange,
            style: TextButton.styleFrom(
              foregroundColor: AppColors.royalIndigo,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            ),
            child: const Text(
              '更改',
              style: TextStyle(fontWeight: FontWeight.w600),
            ),
          ),
        ],
      ),
    );
  }

  /// 建立日曆視圖
  Widget _buildCalendarView() {
    // 使用管理後台風格的主題
    final theme = EventCalendarTheme.admin();

    return Padding(
      padding: const EdgeInsets.all(16),
      child: AstroEventCalendarWidget(
        timelineData: _timelineData!,
        theme: theme,
        onDayTapped: _handleDayTapped,
      ),
    );
  }

  /// 建立時間軸視圖
  Widget _buildTimelineView() {
    // 使用管理後台風格的主題
    final theme = const EventTimelineTheme.admin();

    return Padding(
      padding: const EdgeInsets.all(16),
      child: EventTimelineWidget(
        timelineData: _timelineData!,
        theme: theme,
        onDataPointTapped: _handleDayTapped,
      ),
    );
  }

  /// 建立分類時間軸視圖
  Widget _buildCategorizedTimelineView() {
    if (_categorizedTimelineData == null) {
      return const Center(
        child: Text(
          '分類事件資料載入中...',
          style: TextStyle(
            color: AppColors.textSecondary,
            fontSize: 16,
          ),
        ),
      );
    }

    return Padding(
      padding: const EdgeInsets.all(16),
      child: CategorizedEventTimelineWidget(
        timelineData: _categorizedTimelineData!,
        onDataPointTapped: _handleCategorizedDayTapped,
        height: 400,
        showGrid: true,
        showLegend: true,
        initialFilter: EventTimelineFilter.all(),
      ),
    );
  }

  /// 建立事件詳情面板
  Widget _buildEventDetailPanel() {
    // 使用管理後台風格的主題
    final theme = const EventDetailTheme.admin();

    return Positioned(
      bottom: 0,
      left: 0,
      right: 0,
      child: EventDetailPanel(
        selectedDate: _selectedDate!,
        dailyScore: _selectedDayScore,
        events: _selectedDayEvents,
        theme: theme,
        onClose: () {
          setState(() {
            _selectedDate = null;
            _selectedDayScore = null;
            _selectedDayEvents = [];
          });
        },
      ),
    );
  }

  /// 載入事件資料
  Future<void> _loadEventData() async {
    if (_selectedUser == null) {
      setState(() {
        _errorMessage = '請先選擇用戶';
        _isLoading = false;
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // 載入基本事件資料
      EventTimelineData timelineData =
          await _eventDetectionService.detectEvents(
        _selectedUser!,
        _startDate,
        _endDate,
      );

      // 載入分類事件資料
      CategorizedTimelineData categorizedTimelineData =
          await _eventDetectionService.detectCategorizedEvents(
        _selectedUser!,
        _startDate,
        _endDate,
      );

      setState(() {
        _timelineData = timelineData;
        _categorizedTimelineData = categorizedTimelineData;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
        _isLoading = false;
      });
    }
  }

  /// 處理日期點擊
  void _handleDayTapped(DateTime date, DailyEventScore? score) {
    // 使用精確的日期過濾，只獲取該天的事件
    List<AstroEvent> events = _timelineData?.getEventsForDate(date) ?? [];

    setState(() {
      _selectedDate = date;
      _selectedDayScore = score;
      _selectedDayEvents = events;
    });
  }

  /// 處理分類事件點擊
  void _handleCategorizedDayTapped(
      DateTime date, CategorizedEventScore? score) {
    // 使用精確的日期過濾，只獲取該天的事件
    List<AstroEvent> events =
        _categorizedTimelineData?.getEventsForDate(date) ?? [];

    setState(() {
      _selectedDate = date;
      _selectedCategorizedScore = score;
      _selectedDayEvents = events;
    });
  }

  /// 顯示日期範圍選擇器（初始化時使用）
  Future<void> _showDateRangeSelector() async {
    // 設定預設日期範圍（當前年份）
    final now = DateTime.now();
    _startDate = DateTime(now.year, 1, 1);
    _endDate = DateTime(now.year, 12, 31);

    final dateRange = await showDateRangePicker(
      context: context,
      firstDate: DateTime(DateTime.now().year - 5),
      lastDate: DateTime(DateTime.now().year + 5),
      initialDateRange: material.DateTimeRange(start: _startDate, end: _endDate),
      helpText: '選擇事件偵測的日期範圍',
      confirmText: '確定',
      cancelText: '取消',
    );

    if (dateRange != null) {
      setState(() {
        _startDate = dateRange.start;
        _endDate = dateRange.end;
        _isDateRangeSet = true;
      });
    } else {
      // 用戶取消選擇，使用預設範圍
      setState(() {
        _isDateRangeSet = true;
      });
    }
  }

  /// 選擇日期範圍（更改時使用）
  Future<void> _selectDateRange() async {
    final dateRange = await showDateRangePicker(
      context: context,
      firstDate: DateTime(DateTime.now().year - 5),
      lastDate: DateTime(DateTime.now().year + 5),
      initialDateRange: material.DateTimeRange(start: _startDate, end: _endDate),
    );

    if (dateRange != null) {
      setState(() {
        _startDate = dateRange.start;
        _endDate = dateRange.end;
      });

      _loadEventData();
    }
  }

  /// 選擇用戶
  Future<void> _selectUser() async {
    try {
      final birthDataService = BirthDataService();
      final allUsers = await birthDataService.getAllBirthData();

      if (allUsers.isEmpty) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('沒有可用的用戶資料')),
          );
        }
        return;
      }

      if (!mounted) return;

      final selectedUser = await showDialog<BirthData>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('選擇用戶'),
          content: SizedBox(
            width: double.maxFinite,
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: allUsers.length,
              itemBuilder: (context, index) {
                final user = allUsers[index];
                final isSelected = _selectedUser?.id == user.id;
                return ListTile(
                  leading: CircleAvatar(
                    backgroundColor: isSelected
                        ? AppColors.royalIndigo
                        : AppColors.royalIndigo.withValues(alpha: 0.1),
                    child: Icon(
                      Icons.person,
                      color: isSelected ? Colors.white : AppColors.royalIndigo,
                      size: 20,
                    ),
                  ),
                  title: Text(
                    user.name,
                    style: TextStyle(
                      fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                    ),
                  ),
                  subtitle: Text(
                    '${user.dateTime.year}/${user.dateTime.month}/${user.dateTime.day} ${user.birthPlace}',
                    style: const TextStyle(fontSize: 12),
                  ),
                  selected: isSelected,
                  onTap: () => Navigator.of(context).pop(user),
                );
              },
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('取消'),
            ),
          ],
        ),
      );

      if (selectedUser != null && selectedUser.id != _selectedUser?.id) {
        setState(() {
          _selectedUser = selectedUser;
        });

        // 保存選中的用戶
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('selected_person_id', selectedUser.id);

        // 重新載入事件資料
        if (_isDateRangeSet) {
          await _loadEventData();
        }
      }
    } catch (e) {
      logger.e('選擇用戶失敗: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('選擇用戶失敗: $e')),
        );
      }
    }
  }

  /// 顯示設定
  void _showSettings() {
    if (_selectedUser == null) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('請先選擇用戶')),
        );
      }
      return;
    }

    showDialog(
      context: context,
      builder: (context) => EventDetectionSettingsDialog(
        birthData: _selectedUser!,
        onSettingsChanged: () {
          // 設定變更後重新載入資料
          _loadEventData();
        },
      ),
    );
  }
}
