import 'package:flutter/material.dart';

import '../../../astreal.dart';
import '../../../shared/widgets/common/responsive_wrapper.dart';
import '../../../shared/widgets/unified_card.dart';
import '../starlight_chart_selection_page.dart';

/// Starlight 關係分析頁面
/// 提供完整的關係分析選項和詳細說明
class StarlightRelationshipAnalysisPage extends StatefulWidget {
  final BirthData? primaryPerson;

  const StarlightRelationshipAnalysisPage({
    super.key,
    this.primaryPerson,
  });

  @override
  State<StarlightRelationshipAnalysisPage> createState() =>
      _StarlightRelationshipAnalysisPageState();

  /// 獲取關係分析項目
  static List<Map<String, dynamic>> getRelationshipAnalysisItems() {
    return [
      {
        'title': '配對分析',
        // 'subtitle': '比較盤分析',
        'subtitle': '看看在性格、價值觀、生活方式上的契合度。了解彼此的優勢互補和潛在挑戰。',
        'icon': Icons.people,
        'color': AppColors.cosmicPurple,
        'chartType': ChartType.synastry,
        'features': ['性格契合度', '價值觀比較', '溝通方式分析', '相處建議'],
      },
      {
        'title': '關係深度分析',
        // 'subtitle': '組合盤分析',
        'subtitle': '揭示兩人在一起的整體能量、發展方向和化學反應。',
        'icon': Icons.favorite_border,
        'color': AppColors.starlightAccent,
        'chartType': ChartType.composite,
        'features': ['關係本質', '共同目標', '挑戰與機會', '長期發展'],
      },
      {
        'title': '還原關係真實樣貌',
        // 'subtitle': '時空中點盤',
        'subtitle': '還原關係本身的真實樣貌。看關係未來發展，矛盾衝突的原因是什麼。',
        'icon': Icons.timeline,
        'color': AppColors.solarAmber,
        'chartType': ChartType.davison,
        'features': ['相遇意義', '關係使命', '時機分析', '命運連結'],
      },
      {
        'title': '揭示雙方真實感受',
        // 'subtitle': '馬盤分析',
        'subtitle': '細緻地描繪出關係中雙方的心理感受，深入探索彼此內心的想法和情緒。',
        'icon': Icons.psychology,
        'color': AppColors.royalIndigo,
        'chartType': ChartType.marks,
        'features': ['靈魂連結', '內心需求', '深層動力', '精神契合'],
      },
    ];
  }
}

class _StarlightRelationshipAnalysisPageState
    extends State<StarlightRelationshipAnalysisPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.lightCornsilk,
      appBar: AppBar(
        title: const Text(
          '關係分析',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: AppColors.textDark,
          ),
        ),
        backgroundColor: AppColors.lightCornsilk,
        elevation: 0,
        iconTheme: const IconThemeData(color: AppColors.textDark),
      ),
      body: ResponsivePageWrapper(
        maxWidth: 800.0, // 關係分析頁面適合中等寬度
        child: SingleChildScrollView(
          padding: ResponsiveUtils.getResponsivePadding(context),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 頁面介紹
              ResponsiveCardWrapper(
                maxWidth: 700.0,
                child: _buildIntroductionSection(),
              ),
              const SizedBox(height: 24),

              // 關係分析類型
              _buildAnalysisTypesSection(),
              const SizedBox(height: 24),

              // 使用說明
              ResponsiveCardWrapper(
                maxWidth: 700.0,
                child: _buildInstructionsSection(),
              ),

              const SizedBox(height: 40),
            ],
          ),
        ),
      ),
    );
  }

  /// 構建介紹區塊
  Widget _buildIntroductionSection() {
    return UnifiedCard(
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.cosmicPurple.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.favorite,
              color: AppColors.cosmicPurple,
              size: 28,
            ),
          ),
          const SizedBox(width: 16),
          const Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '探索你的關係密碼',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textDark,
                  ),
                ),
                SizedBox(height: 6),
                Text(
                  '透過占星學了解你與他人的緣分與相處之道',
                  style: TextStyle(
                    fontSize: 14,
                    color: AppColors.textSecondary,
                    height: 1.4,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 構建分析類型區塊
  Widget _buildAnalysisTypesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 4.0),
          child: const Text(
            '關係分析類型',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.textDark,
            ),
          ),
        ),
        const SizedBox(height: 16),
        ListView.separated(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: StarlightRelationshipAnalysisPage.getRelationshipAnalysisItems().length,
          separatorBuilder: (context, index) => const SizedBox(height: 16),
          itemBuilder: (context, index) {
            final item = StarlightRelationshipAnalysisPage.getRelationshipAnalysisItems()[index];
            return ResponsiveCardWrapper(
              maxWidth: 700.0,
              child: _buildAnalysisTypeCard(item),
            );
          },
        ),
      ],
    );
  }

  /// 構建使用說明區塊
  Widget _buildInstructionsSection() {
    return UnifiedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.solarAmber.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.info_outline,
                  color: AppColors.solarAmber,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                '使用說明',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textDark,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          const Text(
            '1. 選擇主要人物（通常是你自己）\n'
            '2. 點擊想要的分析類型\n'
            '3. 選擇第二個人物進行比較\n'
            '4. 查看詳細的關係分析報告',
            style: TextStyle(
              fontSize: 14,
              color: AppColors.textSecondary,
              height: 1.6,
            ),
          ),
        ],
      ),
    );
  }


  /// 構建分析類型卡片
  Widget _buildAnalysisTypeCard(Map<String, dynamic> item) {
    return UnifiedCard(
      child: InkWell(
        onTap: () => _navigateToAnalysis(item),
        borderRadius: BorderRadius.circular(16),
        child: Row(
          children: [
            // 圖標區域
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: item['color'].withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                item['icon'],
                color: item['color'],
                size: 24,
              ),
            ),
            const SizedBox(width: 16),

            // 內容區域
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          item['title'],
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: AppColors.textDark,
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 3,
                        ),
                        decoration: BoxDecoration(
                          color: item['color'].withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          item['subtitle'],
                          style: TextStyle(
                            fontSize: 11,
                            color: item['color'],
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    item['description'],
                    style: const TextStyle(
                      fontSize: 13,
                      color: AppColors.textSecondary,
                      height: 1.4,
                    ),
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),

            const SizedBox(width: 12),
            // 箭頭圖標
            Container(
              padding: const EdgeInsets.all(4),
              child: const Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 導航到分析
  void _navigateToAnalysis(Map<String, dynamic> item) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => StarlightChartSelectionPage(
          primaryPerson: widget.primaryPerson,
          chartType: item['chartType'],
          analysisTitle: item['title'],
          fromAnalysisPage: true,
        ),
      ),
    );
  }
}
