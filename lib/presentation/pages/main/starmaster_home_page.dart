import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../astreal.dart';
import '../../../core/utils/web_fullscreen_helper.dart'
    if (dart.library.io) '../../../core/utils/web_fullscreen_helper_stub.dart';
import '../../../data/services/notification/notification_service.dart';
import '../../../shared/widgets/common/responsive_wrapper.dart';
import '../../../shared/widgets/person_info_card.dart';
import '../../../shared/widgets/unified_card.dart';
import '../ai_interpretation_result_page.dart';
import '../analysis/divination_analysis_page.dart';
import '../astrology/astro_event_detection_page.dart';
import '../birth_data_form_page.dart';
import '../celebrity_examples_page.dart';
import '../chart_selection_page.dart';
import '../chart_types_page.dart';
import '../daily_astrology_page.dart';
import '../eclipse_setup_page.dart';
import '../equinox_solstice_page.dart';
import '../learning/astrology_faq_page.dart';
import '../notification/notification_page.dart';
import '../onboarding/user_mode_selection_page.dart';
import '../video_list_page.dart';

/// Starmaster 專業占星師模式主頁
class StarmasterHomePage extends StatefulWidget {
  const StarmasterHomePage({super.key});

  @override
  State<StarmasterHomePage> createState() => _StarmasterHomePageState();
}

class _StarmasterHomePageState extends State<StarmasterHomePage> {
  @override
  void initState() {
    super.initState();

    // 設置全螢幕狀態變化回調
    if (kIsWeb) {
      WebFullscreenHelper.setOnFullscreenChanged(() {
        if (mounted) {
          setState(() {});
        }
      });
    }
  }

  @override
  void dispose() {
    // 移除全螢幕狀態變化回調
    if (kIsWeb) {
      WebFullscreenHelper.removeOnFullscreenChanged();
    }
    super.dispose();
  }

  /// 編輯選中的人物
  Future<void> _editSelectedPerson(HomeViewModel viewModel) async {
    if (viewModel.selectedPerson == null) return;

    final result = await Navigator.push<BirthData>(
      context,
      MaterialPageRoute(
        builder: (context) => BirthDataFormPage(
          initialData: viewModel.selectedPerson,
        ),
      ),
    );

    if (result != null && mounted) {
      viewModel.setSelectedPerson(result);
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('客戶資料已更新'),
          backgroundColor: AppColors.successGreen,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => HomeViewModel(),
      child: Consumer<HomeViewModel>(
        builder: (context, viewModel, child) {
          return Scaffold(
            backgroundColor: AppColors.pastelSkyBlue,
            appBar: _buildProfessionalAppBar(),
            body: SafeArea(
              child: RefreshIndicator(
                onRefresh: () async {
                  // 刷新客戶資料
                  await viewModel.loadBirthData();
                },
                child: ResponsivePageWrapper(
                  maxWidth: 1000.0, // 專業模式可以稍微寬一些
                  child: ListView(
                    padding: ResponsiveUtils.getResponsivePadding(context),
                    children: [
                      // 專業歡迎卡片
                      ResponsiveCardWrapper(
                        maxWidth: 800.0,
                        child: _buildProfessionalWelcomeCard(viewModel),
                      ),

                      const SizedBox(height: 20),

                      // 客戶管理區域
                      ResponsiveCardWrapper(
                        maxWidth: 800.0,
                        child: _buildClientManagementSection(viewModel),
                      ),

                      const SizedBox(height: 20),

                      // 星盤分析快速入口
                      ResponsiveCardWrapper(
                        maxWidth: 800.0,
                        child: _buildChartTypesSection(viewModel),
                      ),

                      const SizedBox(height: 20),

                      // 每日星相區域
                      ResponsiveCardWrapper(
                        maxWidth: 800.0,
                        child: _buildDailyAstrologySection(viewModel),
                      ),

                      const SizedBox(height: 20),

                      // 時事占星工具區域
                      ResponsiveCardWrapper(
                        maxWidth: 800.0,
                        child: _buildCurrentEventsAstrologySection(),
                      ),

                      const SizedBox(height: 20),

                      // 高級功能區域
                      ResponsiveCardWrapper(
                        maxWidth: 800.0,
                        child: _buildAdvancedFeaturesSection(),
                      ),

                      const SizedBox(height: 20),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  /// 構建專業 AppBar
  PreferredSizeWidget _buildProfessionalAppBar() {
    return AppBar(
      backgroundColor: AppColors.pastelSkyBlue,
      elevation: 0,
      title: const Text(
        'Starmaster',
        style: TextStyle(
          fontSize: 24,
          fontWeight: FontWeight.bold,
          color: AppColors.royalIndigo,
        ),
      ),
      actions: [
        // 全螢幕按鈕（僅在網頁平台顯示，但排除iOS設備）
        if (kIsWeb && !WebFullscreenHelper.isIOSDevice)
          IconButton(
            onPressed: () async {
              // 調試：打印點擊前的狀態
              WebFullscreenHelper.debugPrintState();

              if (WebFullscreenHelper.isFullscreenEnabled) {
                await WebFullscreenHelper.exitFullscreen();
              } else {
                await WebFullscreenHelper.enterFullscreen();
              }

              // 調試：打印點擊後的狀態
              WebFullscreenHelper.debugPrintState();

              // 強制更新 UI（備用機制）
              if (mounted) {
                setState(() {});
              }
            },
            icon: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: AppColors.royalIndigo.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                WebFullscreenHelper.isFullscreenEnabled
                    ? Icons.fullscreen_exit
                    : Icons.fullscreen,
                color: AppColors.royalIndigo,
                size: 20,
              ),
            ),
            tooltip:
                WebFullscreenHelper.isFullscreenEnabled ? '退出全螢幕' : '進入全螢幕',
          ),
        // 通知按鈕
        FutureBuilder<int>(
          future: NotificationService.getUnreadNotificationCount(),
          builder: (context, snapshot) {
            final unreadCount = snapshot.data ?? 0;
            return IconButton(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const NotificationPage(),
                  ),
                );
              },
              icon: Stack(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: AppColors.royalIndigo.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Icon(
                      Icons.notifications,
                      color: AppColors.royalIndigo,
                      size: 20,
                    ),
                  ),
                  if (unreadCount > 0)
                    Positioned(
                      right: 0,
                      top: 0,
                      child: Container(
                        padding: const EdgeInsets.all(2),
                        decoration: const BoxDecoration(
                          color: Colors.red,
                          shape: BoxShape.circle,
                        ),
                        constraints: const BoxConstraints(
                          minWidth: 16,
                          minHeight: 16,
                        ),
                        child: Text(
                          unreadCount > 99 ? '99+' : unreadCount.toString(),
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                ],
              ),
              tooltip: '通知中心',
            );
          },
        ),

        IconButton(
          onPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) =>
                    const UserModeSelectionPage(isModeSwitch: true),
              ),
            );
          },
          icon: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppColors.royalIndigo.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.swap_horiz,
              color: AppColors.royalIndigo,
              size: 20,
            ),
          ),
          tooltip: '切換模式',
        ),
        const SizedBox(width: 8),
      ],
    );
  }

  /// 構建專業歡迎卡片
  Widget _buildProfessionalWelcomeCard(HomeViewModel viewModel) {
    return UnifiedCard(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppColors.royalIndigo.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.psychology,
                  color: AppColors.royalIndigo,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '專業占星師工作台',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '為您的客戶提供專業占星服務',
                      style: TextStyle(
                        fontSize: 14,
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          if (viewModel.selectedPerson != null) ...[
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.cosmicPurple.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.person,
                    color: AppColors.cosmicPurple,
                    size: 16,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    '當前客戶：${viewModel.selectedPerson!.name}',
                    style: TextStyle(
                      fontSize: 14,
                      color: AppColors.cosmicPurple,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// 構建客戶管理區域
  Widget _buildClientManagementSection(HomeViewModel viewModel) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionHeader('客戶管理', Icons.business_center),
        const SizedBox(height: 16),

        // 當前客戶資訊卡片
        UnifiedCard(
          padding: EdgeInsets.zero,
          child: PersonInfoCard(
            selectedPerson: viewModel.selectedPerson,
            personList: viewModel.birthDataList,
            onPersonSelected: (person) {
              viewModel.setSelectedPerson(person);
            },
            onEditPerson: (person) {
              _editSelectedPerson(viewModel);
            },
            formatDateTime: viewModel.formatDateTime,
            isLoading: viewModel.isLoadingBirthData,
            title: '當前客戶',
            icon: Icons.business_center,
            iconColor: AppColors.royalIndigo,
            dialogTitle: '選擇客戶',
            dialogButtonColor: AppColors.royalIndigo,
          ),
        ),
      ],
    );
  }

  /// 構建星盤類型分析區域
  Widget _buildChartTypesSection(HomeViewModel viewModel) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Flexible(
              child: _buildSectionHeader('星盤分析', Icons.auto_awesome),
            ),
            if (viewModel.selectedPerson != null) ...[
              GestureDetector(
                onTap: () => _navigateToAllChartTypes(viewModel),
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  decoration: BoxDecoration(
                    color: AppColors.royalIndigo.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: AppColors.royalIndigo.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Text(
                        '查看全部',
                        style: TextStyle(
                          fontSize: 12,
                          color: AppColors.royalIndigo,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(width: 4),
                      const Icon(
                        Icons.arrow_forward_ios,
                        size: 10,
                        color: AppColors.royalIndigo,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ],
        ),

        const SizedBox(height: 16),

        // 常用星盤類型
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            crossAxisSpacing: 8,
            mainAxisSpacing: 8,
            mainAxisExtent: 120, // 固定高度，與本命分析保持一致
          ),
          itemCount: 6,
          itemBuilder: (context, index) {
            final commonChartTypes = _getCommonChartTypes();
            final chartType = commonChartTypes[index];
            final info = _getChartTypeInfo(chartType);
            // _navigateToChartType(chartType, viewModel);
            // _navigateToAnalysisDetail(info['title'] as String,
            //     info['subtitle'] as String, viewModel.selectedPerson!);
            return buildAnalysisCard(
              title: info['title'],
              subtitle: info['subtitle'],
              icon: info['icon'],
              color: info['color'],
              onTap: () => _navigateToChartType(chartType, viewModel),
            );
          },
        ),
      ],
    );
  }

  /// 導航到分析詳情
  void _navigateToAnalysisDetail(
      String title, String subtitle, BirthData birthData) {
    final chartData = ChartData(
      chartType: ChartType.natal,
      primaryPerson: birthData,
    );

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AIInterpretationResultPage(
          chartData: chartData,
          interpretationTitle: title,
          subtitle: subtitle,
          suggestedQuestions: [
            // _buildPlanetInterpretationPrompt(),
          ],
          autoExecuteFirstQuestion: false,
        ),
      ),
    );
  }

  /// 構建分析卡片
  Widget buildAnalysisCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    icon,
                    color: color,
                    size: 18,
                  ),
                ),
                const Spacer(),
                Icon(
                  Icons.arrow_forward_ios,
                  color: Colors.grey[400],
                  size: 12,
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              title,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: AppColors.textDark,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  /// 導航到星盤類型
  void _navigateToChartType(ChartType chartType, HomeViewModel viewModel) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ChartSelectionPage(
          initialChartType: chartType,
          primaryPerson: viewModel.selectedPerson,
        ),
      ),
    );
  }

  /// 獲取星盤類型資訊
  Map<String, dynamic> _getChartTypeInfo(ChartType chartType) {
    switch (chartType) {
      case ChartType.natal:
        return {
          'title': '本命盤',
          'subtitle': '個人性格與命運',
          'color': const Color(0xFF6366F1),
          'icon': Icons.person,
          'shortDesc': '個人性格與命運',
        };
      case ChartType.transit:
        return {
          'title': '行運盤',
          'subtitle': '當前運勢預測',
          'color': const Color(0xFF10B981),
          'icon': Icons.timeline,
          'shortDesc': '當前運勢預測',
        };
      case ChartType.synastry:
        return {
          'title': '比較盤',
          'subtitle': '關係相容性分析',
          'color': const Color(0xFFEC4899),
          'icon': Icons.favorite,
          'shortDesc': '關係相容性分析',
        };
      case ChartType.composite:
        return {
          'title': '組合盤',
          'subtitle': '關係本質探索',
          'color': const Color(0xFF8B5CF6),
          'icon': Icons.group,
          'shortDesc': '關係本質探索',
        };
      case ChartType.solarReturn:
        return {
          'title': '太陽返照盤',
          'subtitle': '年度運勢分析',
          'color': const Color(0xFFF59E0B),
          'icon': Icons.wb_sunny,
          'shortDesc': '年度運勢分析',
        };
      case ChartType.solarArcDirection:
        return {
          'title': '太陽弧正向盤',
          'subtitle': '重大人生變化',
          'color': const Color(0xFF6366F1),
          'icon': Icons.brightness_2,
          'shortDesc': '重大人生變化',
        };
      default:
        return {
          'title': '星盤分析',
          'subtitle': '占星分析',
          'color': Colors.grey,
          'icon': Icons.circle,
          'shortDesc': '星盤分析',
        };
    }
  }

  /// 獲取常用星盤類型
  List<ChartType> _getCommonChartTypes() {
    return [
      ChartType.natal,
      ChartType.transit,
      ChartType.synastry,
      ChartType.composite,
      ChartType.solarReturn,
      ChartType.solarArcDirection,
    ];
  }

  /// 導航到所有星盤類型頁面
  void _navigateToAllChartTypes(HomeViewModel viewModel) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ChartTypesPage(
          selectedPerson: viewModel.selectedPerson,
        ),
      ),
    );
  }

  /// 構建高級功能區域
  Widget _buildAdvancedFeaturesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '學習資源',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppColors.textDark,
          ),
        ),
        const SizedBox(height: 12),

        // 影片教學卡片
        UnifiedFeatureCard(
          title: '影片教學',
          subtitle: '觀看專業占星學習影片與應用教學',
          icon: Icons.video_library,
          color: AppColors.royalIndigo,
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const VideoListPage(),
              ),
            );
          },
        ),

        const SizedBox(height: 12),

        // 占星常見問題卡片
        UnifiedFeatureCard(
          title: '占星常見問題',
          subtitle: '專業占星知識解答與學習指南',
          icon: Icons.help_outline,
          color: AppColors.royalIndigo,
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const AstrologyFaqPage(),
              ),
            );
          },
        ),

        const SizedBox(height: 12),

        if (!kReleaseMode) ...[
          // 名人解讀範例卡片
          UnifiedFeatureCard(
            title: '名人解讀範例',
            subtitle: '透過知名人物學習專業占星分析技巧',
            icon: Icons.star,
            color: AppColors.solarAmber,
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const CelebrityExamplesPage(),
                ),
              );
            },
          ),
          const SizedBox(height: 12),
        ],

        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppColors.royalIndigo.withValues(alpha: 0.05),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: AppColors.royalIndigo.withValues(alpha: 0.2),
            ),
          ),
          child: Column(
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: AppColors.royalIndigo.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.auto_awesome,
                      color: AppColors.royalIndigo,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  const Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '深入剖析',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: AppColors.textDark,
                          ),
                        ),
                        Text(
                          '輔助星盤解讀',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: AppColors.royalIndigo,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Text(
                      'PRO',
                      style: TextStyle(
                        fontSize: 10,
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              const Text(
                '結合傳統占星學與現代技術，為您提供更深入、更準確的星盤解讀服務。',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey,
                  height: 1.4,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 構建每日星相區域
  Widget _buildDailyAstrologySection(HomeViewModel viewModel) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionHeader('每日星象', Icons.stars_outlined),
        const SizedBox(height: 16),

        // 每日星相卡片
        UnifiedFeatureCard(
          title: '今日星象',
          subtitle: '掌握宇宙能量變化，提供專業占星分析',
          icon: Icons.stars_outlined,
          color: AppColors.solarAmber,
          onTap: () => _navigateToDailyAstrology(viewModel),
        ),

        const SizedBox(height: 12),

        if (!kReleaseMode) ...[
          // 事件偵測卡片
          UnifiedFeatureCard(
            title: '事件偵測分析',
            subtitle: '專業占星事件偵測，精準掌握時機變化',
            icon: Icons.timeline,
            color: const Color(0xFF6200EA),
            onTap: () => _navigateToEventDetection(viewModel),
          ),
        ]
      ],
    );
  }

  /// 導航到每日星相頁面
  void _navigateToDailyAstrology(HomeViewModel homeViewModel) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ChangeNotifierProvider.value(
          value: homeViewModel,
          child: const DailyAstrologyPage(),
        ),
      ),
    );
  }

  /// 導航到事件偵測頁面
  void _navigateToEventDetection(HomeViewModel homeViewModel) {
    final selectedPerson = homeViewModel.selectedPerson;

    if (selectedPerson == null) {
      // 如果沒有選中的人物，提示用戶先選擇
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('請先選擇一個人物來進行專業事件偵測分析'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AstroEventDetectionPage(
          birthData: selectedPerson,
        ),
      ),
    );
  }

  /// 構建時事占星工具區域
  Widget _buildCurrentEventsAstrologySection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionHeader('時事占星工具', Icons.timeline),
        const SizedBox(height: 16),

        // 卜卦分析卡片
        UnifiedFeatureCard(
          title: '卜卦分析',
          subtitle: '占星卜卦解答疑問',
          icon: Icons.auto_awesome,
          color: AppColors.solarAmber,
          onTap: _navigateToDivination,
        ),

        const SizedBox(height: 12),

        // 二分二至盤和日月蝕盤
        Row(
          children: [
            Expanded(
              child: _buildModernToolCard(
                title: '二分二至盤',
                subtitle: '春分、夏至、秋分、冬至',
                icon: Icons.wb_sunny,
                color: Colors.orange,
                onTap: () => _navigateToEquinoxSolstice(),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildModernToolCard(
                title: '日月蝕盤',
                subtitle: '日蝕、月蝕分析',
                icon: Icons.brightness_2,
                color: Colors.deepPurple,
                onTap: () => _navigateToEclipseChart(),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 導航到卜卦分析頁面
  void _navigateToDivination() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const DivinationAnalysisPage(
          title: '卜卦分析',
          description: '占星卜卦、周易卜卦和塔羅牌占卜',
        ),
      ),
    );
  }

  /// 導航到二分二至盤頁面
  void _navigateToEquinoxSolstice() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const EquinoxSolsticePage(),
      ),
    );
  }

  /// 導航到日月蝕盤頁面
  void _navigateToEclipseChart() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const EclipseSetupPage(),
      ),
    );
  }

  /// 構建區域標題
  Widget _buildSectionHeader(String title, IconData icon) {
    return UnifiedSectionHeader(
      title: title,
      icon: icon,
      iconColor: AppColors.royalIndigo,
    );
  }

  /// 構建現代化工具卡片
  Widget _buildModernToolCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    icon,
                    color: color,
                    size: 20,
                  ),
                ),
                const SizedBox(height: 12),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: 12,
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 構建功能卡片
  Widget _buildFeatureCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    icon,
                    color: color,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: AppColors.textPrimary,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        subtitle,
                        style: TextStyle(
                          fontSize: 14,
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  color: AppColors.textSecondary,
                  size: 16,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
