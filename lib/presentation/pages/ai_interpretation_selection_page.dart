import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../astreal.dart';
import '../../data/services/api/ai_api_service.dart';
import '../../data/services/api/interpretation_config_service.dart';
import '../../shared/widgets/common/responsive_wrapper.dart';
import '../../shared/widgets/custom_question_fab.dart';
import 'ai_interpretation_result_page.dart';
import 'chart_page.dart';
import 'interpretation_records_page.dart';

/// AI 解讀類型選擇頁面
class AIInterpretationSelectionPage extends StatefulWidget {
  final ChartData chartData;

  const AIInterpretationSelectionPage({
    super.key,
    required this.chartData,
  });

  @override
  State<AIInterpretationSelectionPage> createState() =>
      _AIInterpretationSelectionPageState();
}

class _AIInterpretationSelectionPageState
    extends State<AIInterpretationSelectionPage> {
  // 用戶模式狀態
  String _userMode = 'starmaster';
  bool _isLoadingMode = true;

  // 當前的星盤資料（可變更）
  late ChartData _currentChartData;

  @override
  void initState() {
    super.initState();
    _currentChartData = widget.chartData; // 初始化當前星盤資料
    _loadUserMode();
  }

  /// 載入用戶模式
  Future<void> _loadUserMode() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userMode = prefs.getString('user_mode') ?? 'starmaster';

      if (mounted) {
        setState(() {
          _userMode = userMode;
          _isLoadingMode = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _userMode = 'starmaster';
          _isLoadingMode = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_getAppBarTitle()),
        backgroundColor: AppColors.royalIndigo,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.visibility),
            onPressed: () => _navigateToChartPage(context),
            tooltip: _userMode == 'starlight' ? '查看圖表' : '查看星盤',
          ),
          IconButton(
            icon: const Icon(Icons.history),
            onPressed: () => Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const InterpretationRecordsPage(),
              ),
            ),
            tooltip: '查看解讀紀錄',
          ),
        ],
      ),
      body: ResponsivePageWrapper(
        maxWidth: 800.0, // 選擇分析內容頁面適合中等寬度
        child: ListView(
          padding: ResponsiveUtils.getResponsivePadding(context),
          children: [
          // 頁面說明
          _buildPageDescription(),
          const SizedBox(height: 16),

          // 查看星盤按鈕
          _buildViewChartCard(context),

          // 根據星盤類型顯示不同的解讀選項
          ..._buildInterpretationOptions(context),
          ],
        ),
      ),
      // 客製化問題分析浮動按鈕
      floatingActionButton: CustomQuestionFAB(
        chartData: widget.chartData,
        customTitle: _getCustomQuestionTitle(),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
    );
  }

  /// 構建頁面說明
  Widget _buildPageDescription() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.royalIndigo.withValues(alpha: 0.08),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.royalIndigo.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.psychology,
                color: AppColors.royalIndigo,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                _getMainTitle(),
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppColors.royalIndigo,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            _getChartTypeDescription(),
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade700,
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  /// 根據星盤類型獲取描述
  String _getChartTypeDescription() {
    switch (widget.chartData.chartType) {
      case ChartType.natal:
        return '本命盤是您出生時天體的配置，反映了您的性格特質、天賦才能和人生課題。選擇您想了解的面向，將為您提供深入的分析。';
      case ChartType.synastry:
        return '合盤分析兩人星盤的互動關係，揭示彼此的相容性、挑戰和成長機會。選擇您想探索的關係面向。';
      case ChartType.composite:
        return '組合盤代表兩人關係的本質和發展方向，顯示關係的核心主題和共同目標。';
      case ChartType.transit:
        return '推運盤顯示當前行星對您本命盤的影響，幫助您了解當下的機會、挑戰和最佳行動時機。';
      case ChartType.solarReturn:
        return '太陽回歸盤預測您未來一年的整體運勢和重要主題，幫助您規劃年度目標。';
      case ChartType.lunarReturn:
        return '月亮回歸盤分析當月的情緒波動、直覺洞察和短期發展趨勢。';
      case ChartType.firdaria:
        return '法達盤揭示人生不同階段的主導行星和發展重點，幫助您了解生命的節奏和時機。';
      default:
        return '選擇您想了解的解讀面向，將根據星盤配置為您提供專業的占星分析。';
    }
  }

  /// 根據星盤類型構建解讀選項
  List<Widget> _buildInterpretationOptions(BuildContext context) {
    return [_buildConfigBasedOptions(context)];
  }

  /// 基於配置檔案構建解讀選項
  Widget _buildConfigBasedOptions(BuildContext context) {
    return FutureBuilder<InterpretationConfig>(
      future: InterpretationConfigService.instance
          .loadConfig(widget.chartData.chartType),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(
            child: Padding(
              padding: EdgeInsets.all(16.0),
              child: CircularProgressIndicator(),
            ),
          );
        }

        if (snapshot.hasError) {
          return Center(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Text(
                '載入配置失敗: ${snapshot.error}',
                style: const TextStyle(color: Colors.red),
              ),
            ),
          );
        }

        final config = snapshot.data;
        if (config == null || config.options.isEmpty) {
          return const Center(
            child: Padding(
              padding: EdgeInsets.all(16.0),
              child: Text('沒有可用的解讀選項'),
            ),
          );
        }

        // 根據配置生成選項卡片
        return Column(
          children: config.enabledOptions.map((option) {
            return Padding(
              padding: const EdgeInsets.only(bottom: 0.0),
              child: _buildOptionCard(
                context,
                option: option,
                title: option.title,
                subtitle: option.subtitle,
                icon: option.icon,
                color: option.color,
                questions: option.questions,
                keyPoint: option.keyPoint,
              ),
            );
          }).toList(),
        );
      },
    );
  }

  /// 構建選項卡片
  Widget _buildOptionCard(
    BuildContext context, {
    required InterpretationOption option,
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required List<String> questions,
    String? keyPoint,
  }) {

    return StyledCard(
      elevation: 2,
      onTap: () => _navigateToInterpretation(
        context,
        option,
        title,
        subtitle,
        questions,
        keyPoint,
      ),
      child: Padding(
        padding: const EdgeInsets.all(8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    icon,
                    color: color,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              title,
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: AppColors.textDark,
                              ),
                            ),
                          ),
                          // 複製按鈕
                          if (kDebugMode) ...[
                            IconButton(
                              icon: Icon(
                                Icons.copy,
                                size: 18,
                                color: Colors.grey.shade600,
                              ),
                              onPressed: () => _copyChartInfoAndQuestions(
                                context,
                                title,
                                subtitle,
                                questions,
                                keyPoint,
                              ),
                              tooltip: '複製星盤資訊與問題',
                              padding: EdgeInsets.zero,
                              constraints: const BoxConstraints(
                                minWidth: 32,
                                minHeight: 32,
                              ),
                            ),
                          ],
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        subtitle,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade600,
                          height: 1.3,
                        ),
                      ),
                      // 如果使用系統設定，顯示標識
                      if (option.useSystemHouseSystem) ...[
                        const SizedBox(height: 6),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 3),
                          decoration: BoxDecoration(
                            color: AppColors.royalIndigo.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(10),
                            border: Border.all(
                              color: AppColors.royalIndigo.withValues(alpha: 0.3),
                              width: 1,
                            ),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.settings,
                                size: 12,
                                color: AppColors.royalIndigo,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                '使用您的宮位制設定',
                                style: TextStyle(
                                  fontSize: 11,
                                  color: AppColors.royalIndigo,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  color: color,
                  size: 16,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 複製星盤資訊與問題
  Future<void> _copyChartInfoAndQuestions(
    BuildContext context,
    String title,
    String subtitle,
    List<String> questions,
    String? keyPoint,
  ) async {
    // 顯示載入提示
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Row(
          children: [
            SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            ),
            SizedBox(width: 12),
            Text('正在計算星盤資料...'),
          ],
        ),
        backgroundColor: AppColors.royalIndigo,
        duration: Duration(seconds: 10), // 給足夠時間計算
      ),
    );

    try {
      // 組合星盤資訊和問題
      final buffer = StringBuffer()
        ..writeln(title)
        ..writeln("解讀主題：")
        ..writeln(subtitle);

      if (keyPoint != null && keyPoint.isNotEmpty) {
        buffer
          ..writeln()
          ..writeln("分析重點：")
          ..writeln(keyPoint);
      }
      String prompt = buffer.toString();

      // 構建星盤數據摘要
      final chartSummary =
          await AIApiService.buildChartSummary(widget.chartData);
      final fullPrompt =
          await AIApiService.buildFullPrompt(prompt, chartSummary);

      // 複製到剪貼板
      await Clipboard.setData(ClipboardData(text: fullPrompt));

      // 清除載入提示並顯示成功提示
      if (context.mounted) {
        ScaffoldMessenger.of(context).clearSnackBars();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('已複製「$title」的星盤資訊與問題到剪貼板'),
            backgroundColor: AppColors.royalIndigo,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      // 清除載入提示並顯示錯誤提示
      if (context.mounted) {
        ScaffoldMessenger.of(context).clearSnackBars();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('複製失敗：$e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  /// 導航到 AI 解讀結果頁面
  Future<void> _navigateToInterpretation(
    BuildContext context,
    InterpretationOption option,
    String title,
    String subtitle,
    List<String> questions,
    String? keyPoint,
  ) async {
    // 保存 context 以避免異步間隙問題
    final navigator = Navigator.of(context);
    ChartData chartDataToUse = _currentChartData;

    // 如果解讀選項指定了宮位制或使用系統設定，需要重新計算星盤
    if (option.houseSystem != null || option.useSystemHouseSystem) {
      try {
        HouseSystem? houseSystemToUse;

        if (option.useSystemHouseSystem) {
          // 使用系統設定的宮位制
          final chartSettings = await ChartSettings.loadFromPrefs();
          houseSystemToUse = chartSettings.houseSystem;
          logger.d('解讀選項 ${option.title} 使用系統設定的宮位制: $houseSystemToUse');
        } else {
          // 使用選項指定的宮位制
          houseSystemToUse = option.houseSystem;
          logger.d('解讀選項 ${option.title} 指定了宮位制: $houseSystemToUse');
        }

        // 使用 ChartViewModel 重新計算星盤
        final chartViewModel = ChartViewModel.withChartData(
          initialChartData: _currentChartData,
        );

        // 異步計算星盤
        await chartViewModel.calculateChart(houseSystem: houseSystemToUse);

        // 更新當前的星盤資料
        chartDataToUse = chartViewModel.chartData;

        // 更新狀態中的星盤資料
        if (mounted) {
          setState(() {
            _currentChartData = chartDataToUse;
          });
        }

        debugPrint('星盤重新計算完成，使用宮位制: $houseSystemToUse');
      } catch (e) {
        debugPrint('重新計算星盤時出錯: $e');
        // 如果計算失敗，使用原始資料
        chartDataToUse = _currentChartData;
      }
    }

    if (!mounted) return;

    navigator.push(
      MaterialPageRoute(
        builder: (context) => AIInterpretationResultPage(
          chartData: chartDataToUse,
          interpretationTitle: title,
          subtitle: subtitle,
          suggestedQuestions: questions,
          keyPoint: keyPoint,
        ),
      ),
    );
  }

  /// 構建查看星盤卡片
  Widget _buildViewChartCard(BuildContext context) {
    return StyledCard(
      elevation: 2,
      onTap: () => _navigateToChartPage(context),
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              AppColors.solarAmber.withValues(alpha: 0.1),
              AppColors.royalIndigo.withValues(alpha: 0.1),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: AppColors.solarAmber.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Icon(
                Icons.visibility,
                color: AppColors.solarAmber,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    '查看星盤',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textDark,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '在進行解讀前，先查看完整的星盤配置',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            const Icon(
              Icons.arrow_forward_ios,
              color: AppColors.solarAmber,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  /// 導航到星盤頁面
  void _navigateToChartPage(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ChangeNotifierProvider(
          create: (_) =>
              ChartViewModel.withChartData(initialChartData: _currentChartData),
          child: ChartPage(chartData: _currentChartData),
        ),
      ),
    );
  }

  /// 獲取 AppBar 標題
  String _getAppBarTitle() {
    if (_isLoadingMode) return '分析選擇';

    if (_userMode == 'starlight') {
      return '選擇分析內容';
    }
    return '星盤解讀';
  }

  /// 獲取主標題
  String _getMainTitle() {
    if (_isLoadingMode) return '載入中...';

    if (_userMode == 'starlight') {
      return _getStarlightMainTitle();
    }
    return '${widget.chartData.chartType.displayName}解讀';
  }

  /// 獲取初心者模式主標題
  String _getStarlightMainTitle() {
    final chartType = widget.chartData.chartType;

    switch (chartType) {
      case ChartType.natal:
        return '我的性格分析';
      case ChartType.transit:
        return '當前運勢分析';
      case ChartType.synastry:
        return '配對分析';
      case ChartType.composite:
        return '關係深度分析';
      case ChartType.davison:
        return '關係時空分析';
      case ChartType.marks:
        return '內心感受分析';
      case ChartType.secondaryProgression:
        return '人生發展趨勢';
      case ChartType.tertiaryProgression:
        return '短期心理變化';
      case ChartType.solarReturn:
        return '年度運勢';
      case ChartType.lunarReturn:
        return '月度情緒週期';
      case ChartType.eclipse:
        return '重要天象影響';
      case ChartType.equinoxSolstice:
        return '季節能量分析';
      case ChartType.firdaria:
        return '古典時序分析';
      case ChartType.profection:
        return '年度預測分析';
      case ChartType.mundane:
        return '社會事件分析';
      case ChartType.horary:
        return '問題解答分析';
      case ChartType.event:
        return '事件時機分析';
      default:
        return '個人分析';
    }
  }

  /// 獲取自定義問題標題
  String _getCustomQuestionTitle() {
    if (_isLoadingMode) return '自定義問題分析';

    if (_userMode == 'starlight') {
      return '我想問的問題';
    }
    return '${widget.chartData.chartType.displayName}客製化問題分析';
  }
}
