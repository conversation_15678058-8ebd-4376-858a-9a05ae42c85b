import 'package:flutter/material.dart';

import '../../astreal.dart';
import '../../features/astrology/constants/aspect_definitions.dart';
import 'ai_interpretation_result_page.dart';


/// 界主星股市分析頁面
class TermRulerStockAnalysisPage extends StatefulWidget {
  final ChartViewModel chartViewModel;
  final TermRulerTimelineItem termRulerItem;
  final List<AspectInfo> aspects;

  const TermRulerStockAnalysisPage({
    super.key,
    required this.chartViewModel,
    required this.termRulerItem,
    required this.aspects,
  });

  @override
  State<TermRulerStockAnalysisPage> createState() => _TermRulerStockAnalysisPageState();
}

class _TermRulerStockAnalysisPageState extends State<TermRulerStockAnalysisPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('界主星股市分析'),
        backgroundColor: AppColors.royalIndigo,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: _navigateToAIAnalysis,
            icon: const Icon(Icons.psychology),
            tooltip: 'AI 深度分析',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 界主星基本資訊
            _buildTermRulerInfo(),
            // const SizedBox(height: 16),
            
            // 相位分析
            _buildAspectAnalysis(),
            // const SizedBox(height: 16),
            
            // 股市分析
            _buildStockMarketAnalysis(),
            // const SizedBox(height: 16),
            
            // 時間週期分析
            _buildTimeCycleAnalysis(),
            // const SizedBox(height: 16),
            
            // 投資建議
            _buildInvestmentAdvice(),
          ],
        ),
      ),
    );
  }

  /// 構建界主星基本資訊
  Widget _buildTermRulerInfo() {
    final term = widget.termRulerItem;
    
    return StyledCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.star, color: AppColors.royalIndigo),
                const SizedBox(width: 8),
                Text(
                  '界主星資訊',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.royalIndigo,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            
            _buildInfoRow('界主星', '${term.termRulerName}${ZodiacSymbols.getPlanetSymbol(term.termRulerName)}'),
            _buildInfoRow('所在星座', '${term.sign}${ZodiacSymbols.getZodiacSymbol(term.sign)}'),
            _buildInfoRow('經度位置', '${term.longitude.toStringAsFixed(2)}°'),
            _buildInfoRow('開始時間', widget.chartViewModel.formatDateTime(term.startDateTime)),
            _buildInfoRow('結束時間', widget.chartViewModel.formatDateTime(term.endDateTime)),
            _buildInfoRow('持續天數', '${(term.durationDays as double? ?? 0.0).toStringAsFixed(1)} 天'),
          ],
        ),
      ),
    );
  }

  /// 構建相位分析
  Widget _buildAspectAnalysis() {
    return StyledCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.compare_arrows, color: AppColors.royalIndigo),
                const SizedBox(width: 8),
                Text(
                  '相位分析',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.royalIndigo,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            
            if (widget.aspects.isEmpty)
              const Text('此界主星與其他行星無主要相位')
            else
              ...widget.aspects.map((aspect) => _buildAspectAnalysisItem(aspect)),
          ],
        ),
      ),
    );
  }

  /// 構建股市分析
  Widget _buildStockMarketAnalysis() {
    return StyledCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.trending_up, color: AppColors.royalIndigo),
                const SizedBox(width: 8),
                Text(
                  '股市趨勢分析',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.royalIndigo,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            
            _buildStockAnalysisContent(),
          ],
        ),
      ),
    );
  }

  /// 構建時間週期分析
  Widget _buildTimeCycleAnalysis() {
    return StyledCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.schedule, color: AppColors.royalIndigo),
                const SizedBox(width: 8),
                Text(
                  '時間週期分析',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.royalIndigo,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            
            _buildTimeCycleContent(),
          ],
        ),
      ),
    );
  }

  /// 構建投資建議
  Widget _buildInvestmentAdvice() {
    return StyledCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.lightbulb, color: AppColors.solarAmber),
                const SizedBox(width: 8),
                Text(
                  '投資建議',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.solarAmber,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            
            _buildInvestmentAdviceContent(),
          ],
        ),
      ),
    );
  }

  /// 構建資訊行
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.grey[700],
              ),
            ),
          ),
          const Text(': '),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 14,
                fontFamily: 'astro_one_font',
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 構建相位分析項目
  Widget _buildAspectAnalysisItem(AspectInfo aspect) {
    final aspectDef = AspectDefinitions.getAspectDefinition(aspect.aspectType);
    final aspectColor = aspectDef?['color'] as Color? ?? Colors.grey;
    
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: aspectColor.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: aspectColor.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                '${aspect.planet2.name}${ZodiacSymbols.getPlanetSymbol(aspect.planet2.name)}',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: aspect.planet2.color,
                  fontFamily: 'astro_one_font',
                ),
              ),
              const SizedBox(width: 8),
              Text(
                aspect.symbol,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: aspectColor,
                  fontFamily: 'astro_one_font',
                ),
              ),
              const SizedBox(width: 8),
              Text(
                '${widget.termRulerItem.termRulerName}${ZodiacSymbols.getPlanetSymbol(widget.termRulerItem.termRulerName)}',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  fontFamily: 'astro_one_font',
                ),
              ),
              const Spacer(),
              Text(
                '${aspect.orb.toStringAsFixed(1)}°',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            _getAspectInterpretation(aspect),
            style: const TextStyle(fontSize: 14),
          ),
        ],
      ),
    );
  }

  /// 構建股市分析內容
  Widget _buildStockAnalysisContent() {
    final term = widget.termRulerItem;
    final analysis = _analyzeStockMarketTrend(term, widget.aspects);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 總體趨勢
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: analysis['trendColor'].withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: analysis['trendColor'].withValues(alpha: 0.3)),
          ),
          child: Row(
            children: [
              Icon(
                analysis['trendIcon'],
                color: analysis['trendColor'],
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                '總體趨勢: ${analysis['trend']}',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: analysis['trendColor'],
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 12),

        // 詳細分析
        Text(
          analysis['analysis'],
          style: const TextStyle(fontSize: 14, height: 1.5),
        ),

        const SizedBox(height: 12),

        // 關鍵因素
        Text(
          '關鍵因素:',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: AppColors.royalIndigo,
          ),
        ),
        const SizedBox(height: 8),
        ...analysis['keyFactors'].map<Widget>((factor) => Padding(
          padding: const EdgeInsets.only(bottom: 4),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('• ', style: TextStyle(fontSize: 16)),
              Expanded(child: Text(factor, style: const TextStyle(fontSize: 14))),
            ],
          ),
        )),
      ],
    );
  }

  /// 構建時間週期內容
  Widget _buildTimeCycleContent() {
    final term = widget.termRulerItem;
    final cycleAnalysis = _analyzeTimeCycle(term);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildInfoRow('週期長度', '${(term.durationDays as double? ?? 0.0).toStringAsFixed(1)} 天'),
        _buildInfoRow('每度時間', '${(term.timePerDegree as double? ?? 0.0).toStringAsFixed(2)} 天/度'),
        _buildInfoRow('週期階段', cycleAnalysis['phase']),

        const SizedBox(height: 12),
        Text(
          '週期特徵:',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: AppColors.royalIndigo,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          cycleAnalysis['characteristics'],
          style: const TextStyle(fontSize: 14, height: 1.5),
        ),
      ],
    );
  }

  /// 構建投資建議內容
  Widget _buildInvestmentAdviceContent() {
    final advice = _generateInvestmentAdvice(widget.termRulerItem, widget.aspects);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 風險等級
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: advice['riskColor'].withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: advice['riskColor'].withValues(alpha: 0.3)),
          ),
          child: Row(
            children: [
              Icon(
                advice['riskIcon'],
                color: advice['riskColor'],
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                '風險等級: ${advice['riskLevel']}',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: advice['riskColor'],
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 12),

        // 投資策略
        Text(
          '建議策略:',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: AppColors.royalIndigo,
          ),
        ),
        const SizedBox(height: 8),
        ...advice['strategies'].map<Widget>((strategy) => Padding(
          padding: const EdgeInsets.only(bottom: 8),
          child: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.blue.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(6),
              border: Border.all(color: Colors.blue.withValues(alpha: 0.2)),
            ),
            child: Text(strategy, style: const TextStyle(fontSize: 14)),
          ),
        )),

        const SizedBox(height: 12),

        // 注意事項
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.orange.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(Icons.warning, color: Colors.orange[700], size: 20),
                  const SizedBox(width: 8),
                  Text(
                    '注意事項',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.orange[700],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                advice['warnings'],
                style: const TextStyle(fontSize: 14, height: 1.5),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 分析股市趨勢
  Map<String, dynamic> _analyzeStockMarketTrend(TermRulerTimelineItem term, List<AspectInfo> aspects) {
    final planetName = term.termRulerName;

    // 基於界主星的基本分析
    String trend = '中性';
    Color trendColor = Colors.grey;
    IconData trendIcon = Icons.trending_flat;
    String analysis = '';
    List<String> keyFactors = [];

    // 界主星特性分析
    switch (planetName) {
      case '太陽':
        trend = '穩定上升';
        trendColor = Colors.orange;
        trendIcon = Icons.trending_up;
        analysis = '太陽作為界主星通常帶來穩定的成長動能，市場信心較強，適合長期投資。';
        keyFactors.add('領導力和權威性增強');
        keyFactors.add('市場信心和穩定性提升');
        break;
      case '月亮':
        trend = '波動較大';
        trendColor = Colors.blue;
        trendIcon = Icons.waves;
        analysis = '月亮界主星期間市場情緒波動較大，短期內可能出現較大起伏，需要謹慎操作。';
        keyFactors.add('市場情緒和心理因素影響大');
        keyFactors.add('短期波動性增加');
        break;
      case '水星':
        trend = '快速變化';
        trendColor = Colors.green;
        trendIcon = Icons.speed;
        analysis = '水星界主星帶來快速的資訊流動和交易活動，適合短線操作和技術分析。';
        keyFactors.add('資訊流動加速');
        keyFactors.add('交易活動頻繁');
        break;
      case '金星':
        trend = '溫和上升';
        trendColor = Colors.pink;
        trendIcon = Icons.trending_up;
        analysis = '金星界主星通常帶來和諧的市場環境，消費相關產業可能表現較佳。';
        keyFactors.add('消費和奢侈品行業利好');
        keyFactors.add('市場氛圍相對和諧');
        break;
      case '火星':
        trend = '激烈波動';
        trendColor = Colors.red;
        trendIcon = Icons.whatshot;
        analysis = '火星界主星期間市場可能出現激烈波動，風險和機會並存，需要積極的風險管理。';
        keyFactors.add('市場活動激烈');
        keyFactors.add('風險和機會並存');
        break;
      case '木星':
        trend = '樂觀成長';
        trendColor = Colors.purple;
        trendIcon = Icons.trending_up;
        analysis = '木星界主星通常帶來樂觀的市場前景和擴張機會，適合投資成長型股票。';
        keyFactors.add('市場樂觀情緒高漲');
        keyFactors.add('擴張和成長機會增加');
        break;
      case '土星':
        trend = '謹慎保守';
        trendColor = Colors.brown;
        trendIcon = Icons.trending_down;
        analysis = '土星界主星期間市場趨於保守，可能面臨結構性調整，適合價值投資策略。';
        keyFactors.add('市場結構性調整');
        keyFactors.add('保守和謹慎情緒主導');
        break;
    }

    // 相位影響分析
    for (final aspect in aspects) {
      final aspectType = aspect.aspectType;
      final planet2 = aspect.planet2.name;

      switch (aspectType) {
        case '合相':
          keyFactors.add('與$planet2形成合相，能量集中');
          break;
        case '對沖':
          keyFactors.add('與$planet2形成對沖，可能出現對立或平衡');
          if (trend == '穩定上升') trend = '震盪上升';
          break;
        case '三分相':
          keyFactors.add('與$planet2形成三分相，帶來和諧支持');
          if (trendColor == Colors.grey) {
            trendColor = Colors.green;
            trendIcon = Icons.trending_up;
          }
          break;
        case '四分相':
          keyFactors.add('與$planet2形成四分相，可能面臨挑戰');
          if (trend == '穩定上升') trend = '謹慎觀望';
          break;
        case '六分相':
          keyFactors.add('與$planet2形成六分相，帶來機會');
          break;
      }
    }

    return {
      'trend': trend,
      'trendColor': trendColor,
      'trendIcon': trendIcon,
      'analysis': analysis,
      'keyFactors': keyFactors,
    };
  }

  /// 分析時間週期
  Map<String, dynamic> _analyzeTimeCycle(TermRulerTimelineItem term) {
    final duration = term.durationDays as double? ?? 0.0;
    String phase = '';
    String characteristics = '';

    if (duration < 10) {
      phase = '短期週期';
      characteristics = '這是一個短期週期，適合短線交易和快速決策。市場變化較為迅速，需要密切關注日內走勢。';
    } else if (duration < 30) {
      phase = '中短期週期';
      characteristics = '這是一個中短期週期，適合波段操作。可以關注技術指標的變化，把握中期趨勢。';
    } else if (duration < 90) {
      phase = '中期週期';
      characteristics = '這是一個中期週期，適合中長期投資策略。可以關注基本面的變化和行業趨勢。';
    } else {
      phase = '長期週期';
      characteristics = '這是一個長期週期，適合長期投資和資產配置。應該關注宏觀經濟環境和長期趨勢。';
    }

    return {
      'phase': phase,
      'characteristics': characteristics,
    };
  }

  /// 生成投資建議
  Map<String, dynamic> _generateInvestmentAdvice(TermRulerTimelineItem term, List<AspectInfo> aspects) {
    final planetName = term.termRulerName;
    String riskLevel = '中等';
    Color riskColor = Colors.orange;
    IconData riskIcon = Icons.warning;
    List<String> strategies = [];
    String warnings = '';

    // 基於界主星的風險評估
    switch (planetName) {
      case '太陽':
      case '木星':
        riskLevel = '較低';
        riskColor = Colors.green;
        riskIcon = Icons.check_circle;
        strategies.add('適合長期持有優質股票');
        strategies.add('可以考慮增加投資比重');
        warnings = '雖然風險較低，但仍需注意市場整體環境變化。';
        break;
      case '月亮':
      case '火星':
        riskLevel = '較高';
        riskColor = Colors.red;
        riskIcon = Icons.error;
        strategies.add('建議採用分批進場策略');
        strategies.add('設置嚴格的止損點');
        strategies.add('避免過度槓桿操作');
        warnings = '市場波動較大，需要密切關注風險控制，避免情緒化交易。';
        break;
      case '水星':
        riskLevel = '中等';
        riskColor = Colors.blue;
        riskIcon = Icons.info;
        strategies.add('適合短線交易和技術分析');
        strategies.add('關注資訊面的變化');
        warnings = '資訊變化快速，需要及時調整策略，避免資訊滯後。';
        break;
      case '金星':
        riskLevel = '較低';
        riskColor = Colors.green;
        riskIcon = Icons.favorite;
        strategies.add('關注消費和服務業股票');
        strategies.add('適合穩健型投資策略');
        warnings = '市場相對穩定，但需注意消費趨勢的變化。';
        break;
      case '土星':
        riskLevel = '中等';
        riskColor = Colors.brown;
        riskIcon = Icons.security;
        strategies.add('採用價值投資策略');
        strategies.add('關注基本面分析');
        strategies.add('耐心等待合適的進場時機');
        warnings = '市場可能面臨調整，需要有足夠的耐心和長期視角。';
        break;
    }

    // 相位影響調整
    bool hasChallengingAspects = aspects.any((aspect) =>
        aspect.aspectType == '對沖' || aspect.aspectType == '四分相');
    bool hasSupportiveAspects = aspects.any((aspect) =>
        aspect.aspectType == '三分相' || aspect.aspectType == '六分相');

    if (hasChallengingAspects) {
      if (riskLevel == '較低') riskLevel = '中等';
      if (riskLevel == '中等') riskLevel = '較高';
      strategies.add('特別注意相位帶來的挑戰');
    }

    if (hasSupportiveAspects) {
      strategies.add('善用相位帶來的機會');
    }

    return {
      'riskLevel': riskLevel,
      'riskColor': riskColor,
      'riskIcon': riskIcon,
      'strategies': strategies,
      'warnings': warnings,
    };
  }

  /// 獲取相位解釋
  String _getAspectInterpretation(AspectInfo aspect) {
    final aspectType = aspect.aspectType;
    final planet2 = aspect.planet2.name;
    final termRuler = widget.termRulerItem.termRulerName;

    switch (aspectType) {
      case '合相':
        return '$termRuler與$planet2的能量融合，可能帶來集中的影響力，需要注意能量的平衡使用。';
      case '對沖':
        return '$termRuler與$planet2形成對立，可能出現拉鋸戰或需要在兩者間找到平衡點。';
      case '三分相':
        return '$termRuler與$planet2形成和諧相位，通常帶來正面的支持和機會。';
      case '四分相':
        return '$termRuler與$planet2形成挑戰相位，可能面臨困難但也是成長的機會。';
      case '六分相':
        return '$termRuler與$planet2形成機會相位，通過努力可以獲得良好的結果。';
      default:
        return '$termRuler與$planet2形成$aspectType相位，需要綜合考慮其影響。';
    }
  }

  /// 導航到 AI 分析頁面
  void _navigateToAIAnalysis() {
    final term = widget.termRulerItem;
    final aspects = widget.aspects;

    // 構建相位資訊字符串
    String aspectsInfo = '';
    if (aspects.isNotEmpty) {
      aspectsInfo = '相位資訊：\n';
      for (final aspect in aspects) {
        aspectsInfo += '• ${term.termRulerName} ${aspect.symbol} ${aspect.planet2.name} (容許度: ${aspect.orb.toStringAsFixed(1)}°)\n';
      }
    } else {
      aspectsInfo = '此界主星與其他行星無主要相位。';
    }

    // 構建建議問題
    final suggestedQuestions = [
      '請分析${term.termRulerName}在${term.sign}作為界主星對股市的影響',
      '這個界主星週期（${widget.chartViewModel.formatDateTime(term.startDateTime)} 到 ${widget.chartViewModel.formatDateTime(term.endDateTime)}）的投資策略建議',
      '基於當前的相位配置，股市可能出現什麼趨勢？',
      '這個時期適合投資哪些類型的股票或行業？',
      '需要特別注意哪些風險因素？',
      '如何利用這個界主星週期進行資產配置？',
    ];

    // 先關閉當前的界主星股市分析頁面，回到星盤頁面
    Navigator.popUntil(context, (route) {
      // 如果是主頁面或星盤頁面，停止 pop
      return route.isFirst ||
             (route.settings.name?.contains('chart') ?? false) ||
             (route.settings.name?.contains('home') ?? false);
    });

    // 然後導航到 AI 分析頁面
    Navigator.push(
      context,
      MaterialPageRoute(
        settings: const RouteSettings(name: 'term_ruler_ai_analysis'),
        builder: (context) => AIInterpretationResultPage(
          chartData: widget.chartViewModel.chartData,
          interpretationTitle: '界主星股市分析',
          subtitle: '${term.termRulerName}在${term.sign} - 趨勢分析',
          suggestedQuestions: suggestedQuestions,
          keyPoint: aspectsInfo,
          autoExecuteFirstQuestion: false,
          enableRating: true,
        ),
      ),
    );
  }
}
