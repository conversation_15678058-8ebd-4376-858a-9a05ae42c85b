import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../core/utils/logger_utils.dart';
import '../../data/models/astrology/chart_data.dart';
import '../../data/models/astrology/chart_type.dart';
import '../../data/models/user/birth_data.dart';
import '../../data/services/api/astrology_service.dart';
import '../../data/services/api/birth_data_service.dart';
import '../../features/astrology/models/chart_filter.dart';
import '../../features/astrology/services/chart_filter_service.dart';

/// 星盤篩選器ViewModel
///
/// 管理篩選器的狀態和業務邏輯
class ChartFilterViewModel extends ChangeNotifier {
  static const String _savedFiltersKey = 'saved_chart_filters';
  static const String _cachedChartsKey = 'cached_chart_data';
  static const String _cacheTimestampKey = 'cache_timestamp';
  static const int _cacheValidityHours = 24; // 快取有效期24小時

  // 資料狀態
  List<ChartData> _allCharts = [];
  List<ChartData> _filteredCharts = [];
  List<ChartFilter> _savedFilters = [];
  
  // 當前篩選器
  ChartFilter? _currentFilter;
  
  // UI狀態
  bool _isLoading = false;
  String? _errorMessage;
  
  // 服務
  final BirthDataService _birthDataService = BirthDataService();
  final AstrologyService _astrologyService = AstrologyService();

  // Getters
  List<ChartData> get allCharts => _allCharts;
  List<ChartData> get filteredCharts => _filteredCharts;
  List<ChartFilter> get savedFilters => _savedFilters;
  ChartFilter? get currentFilter => _currentFilter;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  /// 初始化ViewModel
  ChartFilterViewModel() {
    _loadSavedFilters();
  }

  /// 設定載入狀態
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// 設定錯誤訊息
  void _setError(String? error) {
    _errorMessage = error;
    notifyListeners();
  }

  /// 設定星盤資料
  void setCharts(List<ChartData> charts) {
    _allCharts = charts;
    _filteredCharts = charts;
    notifyListeners();
  }

  /// 從出生資料載入星盤
  Future<void> loadChartsFromBirthData() async {
    try {
      _setLoading(true);
      _setError(null);

      // 首先嘗試從快取載入
      final cachedCharts = await _loadChartsFromCache();
      if (cachedCharts != null) {
        _allCharts = cachedCharts;
        _filteredCharts = cachedCharts;
        logger.i('從快取載入 ${cachedCharts.length} 個星盤');
        return;
      }

      // 快取無效或不存在，重新計算
      logger.i('快取無效，開始重新計算星盤資料');

      // 獲取所有出生資料
      final birthDataList = await _birthDataService.getAllBirthData();

      // 為每個出生資料計算星盤
      final charts = <ChartData>[];

      for (final birthData in birthDataList) {
        try {
          final chartData = await _calculateChartData(birthData);
          if (chartData != null) {
            charts.add(chartData);
          }
        } catch (e) {
          logger.w('計算星盤失敗: ${birthData.name}, 錯誤: $e');
          // 繼續處理其他資料，不中斷整個流程
        }
      }

      _allCharts = charts;
      _filteredCharts = charts;

      // 將結果存入快取
      await _saveChartsToCache(charts);

      logger.i('成功載入並快取 ${charts.length} 個星盤');
    } catch (e) {
      logger.e('載入星盤資料時出錯: $e');
      _setError('載入星盤資料失敗：$e');
    } finally {
      _setLoading(false);
    }
  }

  /// 計算星盤資料
  Future<ChartData?> _calculateChartData(BirthData birthData) async {
    try {
      // 計算行星位置
      final planets = await _astrologyService.calculatePlanetPositions(
        birthData.dateTime,
        latitude: birthData.latitude,
        longitude: birthData.longitude,
      );

      // 計算宮位
      final houses = await _astrologyService.calculateHouses(
        birthData.dateTime,
        birthData.latitude,
        birthData.longitude,
      );

      // 計算相位
      final aspects = _astrologyService.calculateAspects(planets);

      return ChartData(
        chartType: ChartType.natal,
        primaryPerson: birthData,
        planets: planets,
        houses: houses,
        aspects: aspects,
      );
    } catch (e) {
      logger.e('計算星盤資料時出錯: $e');
      return null;
    }
  }

  /// 創建新篩選器
  void createNewFilter() {
    _currentFilter = ChartFilter(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      name: '新篩選器',
      groups: [],
    );
    notifyListeners();
  }

  /// 設定當前篩選器
  void setCurrentFilter(ChartFilter filter) {
    _currentFilter = filter;
    notifyListeners();
  }

  /// 更新當前篩選器
  void updateCurrentFilter(ChartFilter filter) {
    _currentFilter = filter;
    notifyListeners();
  }

  /// 應用篩選器
  void applyFilter() {
    if (_currentFilter == null || _currentFilter!.isEmpty) {
      _filteredCharts = _allCharts;
    } else {
      _filteredCharts = ChartFilterService.applyFilter(_allCharts, _currentFilter!);
    }
    
    logger.i('篩選完成：${_filteredCharts.length}/${_allCharts.length} 個星盤符合條件');
    notifyListeners();
  }

  /// 重置篩選器（創建空的篩選器，而不是設為null）
  void resetFilter() {
    _currentFilter = ChartFilter(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      name: '新篩選器',
      groups: [],
    );
    _filteredCharts = _allCharts;
    notifyListeners();
    logger.i('篩選器已重置為預設狀態');
  }

  /// 保存當前篩選器
  Future<void> saveCurrentFilter() async {
    // 檢查篩選器是否為空（沒有設定任何條件）
    if (_currentFilter!.isEmpty) {
      logger.w('無法保存空的篩選器');
      return;
    }

    try {
      // 檢查是否已存在同名篩選器
      final existingIndex = _savedFilters.indexWhere(
        (filter) => filter.name == _currentFilter!.name,
      );

      if (existingIndex >= 0) {
        // 更新現有篩選器
        _savedFilters[existingIndex] = _currentFilter!;
      } else {
        // 添加新篩選器
        _savedFilters.add(_currentFilter!);
      }

      await _saveSavedFilters();
      logger.i('篩選器已保存: ${_currentFilter!.name}');
      notifyListeners();
    } catch (e) {
      logger.e('保存篩選器時出錯: $e');
      _setError('保存篩選器失敗：$e');
    }
  }

  /// 保存篩選器
  Future<void> saveFilter(ChartFilter filter) async {
    try {
      // 檢查是否已存在同名篩選器
      final existingIndex = _savedFilters.indexWhere(
        (f) => f.name == filter.name,
      );

      if (existingIndex >= 0) {
        // 更新現有篩選器
        _savedFilters[existingIndex] = filter;
      } else {
        // 添加新篩選器
        _savedFilters.add(filter);
      }

      await _saveSavedFilters();
      logger.i('篩選器已保存: ${filter.name}');
      notifyListeners();
    } catch (e) {
      logger.e('保存篩選器時出錯: $e');
      _setError('保存篩選器失敗：$e');
    }
  }

  /// 載入篩選器
  void loadFilter(ChartFilter filter) {
    _currentFilter = filter.copyWith(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
    );
    notifyListeners();
  }

  /// 複製篩選器
  void duplicateFilter(ChartFilter filter) {
    final duplicatedFilter = filter.copyWith(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      name: '${filter.name} (副本)',
    );
    
    _savedFilters.add(duplicatedFilter);
    _saveSavedFilters();
    notifyListeners();
  }

  /// 重新命名篩選器
  Future<void> renameFilter(ChartFilter filter, String newName) async {
    try {
      final index = _savedFilters.indexWhere((f) => f.id == filter.id);
      if (index >= 0) {
        _savedFilters[index] = filter.copyWith(name: newName);
        await _saveSavedFilters();
        notifyListeners();
      }
    } catch (e) {
      logger.e('重新命名篩選器時出錯: $e');
      _setError('重新命名篩選器失敗：$e');
    }
  }

  /// 刪除篩選器
  Future<void> deleteFilter(ChartFilter filter) async {
    try {
      _savedFilters.removeWhere((f) => f.id == filter.id);
      await _saveSavedFilters();
      logger.i('篩選器已刪除: ${filter.name}');
      notifyListeners();
    } catch (e) {
      logger.e('刪除篩選器時出錯: $e');
      _setError('刪除篩選器失敗：$e');
    }
  }

  /// 更新篩選器
  Future<void> updateFilter(ChartFilter filter) async {
    try {
      final index = _savedFilters.indexWhere((f) => f.id == filter.id);
      if (index >= 0) {
        _savedFilters[index] = filter;
        await _saveSavedFilters();
        logger.i('篩選器已更新: ${filter.name}');
        notifyListeners();
      } else {
        logger.w('找不到要更新的篩選器: ${filter.id}');
        _setError('找不到要更新的篩選器');
      }
    } catch (e) {
      logger.e('更新篩選器時出錯: $e');
      _setError('更新篩選器失敗：$e');
    }
  }

  /// 清除當前篩選器的所有條件（保留篩選器結構，只清除條件）
  void clearCurrentFilter() {
    // 保留篩選器的基本結構，只清除所有群組和條件
    _currentFilter = _currentFilter!.copyWith(
      groups: [], // 清空所有篩選群組
      updatedAt: DateTime.now(),
    );

    // 重置篩選結果為所有星盤
    _filteredCharts = _allCharts;
    notifyListeners();
    logger.i('已清除篩選條件，保留篩選器結構');
  }

  /// 載入預設篩選器
  void loadDefaultFilters() {
    final defaultFilters = ChartFilterService.getDefaultFilters();
    
    for (final filter in defaultFilters) {
      final existingIndex = _savedFilters.indexWhere(
        (f) => f.name == filter.name,
      );
      
      if (existingIndex < 0) {
        _savedFilters.add(filter);
      }
    }
    
    _saveSavedFilters();
    notifyListeners();
  }

  /// 清除所有篩選器
  Future<void> clearAllFilters() async {
    try {
      _savedFilters.clear();
      await _saveSavedFilters();
      logger.i('所有篩選器已清除');
      notifyListeners();
    } catch (e) {
      logger.e('清除篩選器時出錯: $e');
      _setError('清除篩選器失敗：$e');
    }
  }

  /// 匯出篩選結果
  Map<String, dynamic> exportFilterResults() {
    return {
      'filter': _currentFilter?.toJson(),
      'totalCharts': _allCharts.length,
      'filteredCharts': _filteredCharts.length,
      'results': _filteredCharts.map((chart) => {
        'name': chart.primaryPerson.name,
        'birthPlace': chart.primaryPerson.birthPlace,
        'birthDate': chart.primaryPerson.dateTime.toIso8601String(),
        'planetsCount': chart.planets?.length ?? 0,
      }).toList(),
      'exportTime': DateTime.now().toIso8601String(),
    };
  }

  /// 載入已保存的篩選器
  Future<void> _loadSavedFilters() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final filtersJson = prefs.getString(_savedFiltersKey);
      
      if (filtersJson != null) {
        final filtersList = json.decode(filtersJson) as List;
        _savedFilters = filtersList
            .map((filterJson) => ChartFilter.fromJson(filterJson as Map<String, dynamic>))
            .toList();
        
        logger.i('載入了 ${_savedFilters.length} 個已保存的篩選器');
      }
    } catch (e) {
      logger.e('載入已保存篩選器時出錯: $e');
      _savedFilters = [];
    }
  }

  /// 保存篩選器到本地存儲
  Future<void> _saveSavedFilters() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final filtersJson = json.encode(
        _savedFilters.map((filter) => filter.toJson()).toList(),
      );
      
      await prefs.setString(_savedFiltersKey, filtersJson);
      logger.i('篩選器已保存到本地存儲');
    } catch (e) {
      logger.e('保存篩選器到本地存儲時出錯: $e');
      throw e;
    }
  }

  /// 獲取篩選統計資訊
  Map<String, dynamic> getFilterStatistics() {
    if (_currentFilter == null || _currentFilter!.isEmpty) {
      return {
        'hasFilter': false,
        'totalCharts': _allCharts.length,
        'filteredCharts': _filteredCharts.length,
        'filterRate': 1.0,
      };
    }

    final filterRate = _allCharts.isEmpty 
        ? 0.0 
        : _filteredCharts.length / _allCharts.length;

    return {
      'hasFilter': true,
      'filterName': _currentFilter!.name,
      'totalCharts': _allCharts.length,
      'filteredCharts': _filteredCharts.length,
      'filterRate': filterRate,
      'groupsCount': _currentFilter!.groups.length,
      'conditionsCount': _currentFilter!.groups
          .map((g) => g.conditions.length)
          .fold(0, (sum, count) => sum + count),
    };
  }

  /// 清除錯誤訊息
  void clearError() {
    _setError(null);
  }

  /// 從快取載入星盤資料
  Future<List<ChartData>?> _loadChartsFromCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // 檢查快取時間戳
      final timestamp = prefs.getInt(_cacheTimestampKey);
      if (timestamp == null) {
        return null;
      }

      final cacheTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
      final now = DateTime.now();
      final difference = now.difference(cacheTime);

      // 檢查快取是否過期
      if (difference.inHours > _cacheValidityHours) {
        logger.i('快取已過期，需要重新計算');
        return null;
      }

      // 載入快取資料（現在只是基本資訊，無法重建完整星盤）
      final cachedData = prefs.getString(_cachedChartsKey);
      if (cachedData == null) {
        return null;
      }

      // 由於現在只快取基本資訊，無法重建完整的 ChartData
      // 暫時停用快取功能，避免記憶體問題
      logger.i('發現快取資料，但為避免記憶體問題，暫時停用快取功能');
      return null;
    } catch (e) {
      logger.w('載入快取失敗: $e');
      return null;
    }
  }

  /// 將星盤資料存入快取（優化版本 - 只快取基本資訊）
  Future<void> _saveChartsToCache(List<ChartData> charts) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // 只快取基本資訊，避免記憶體問題
      final lightweightData = charts.map((chart) => {
        'id': chart.primaryPerson?.id,
        'name': chart.primaryPerson?.name,
        'dateTime': chart.primaryPerson?.dateTime.toIso8601String(),
        'latitude': chart.primaryPerson?.latitude,
        'longitude': chart.primaryPerson?.longitude,
        'chartType': chart.chartType.toString(),
        'specificDate': chart.specificDate?.toIso8601String(),
      }).toList();

      final jsonString = jsonEncode(lightweightData);

      // 檢查資料大小，如果超過 1MB 則不快取
      if (jsonString.length > 1024 * 1024) {
        logger.w('快取資料過大 (${(jsonString.length / 1024 / 1024).toStringAsFixed(2)}MB)，跳過快取');
        return;
      }

      // 存儲資料和時間戳
      await prefs.setString(_cachedChartsKey, jsonString);
      await prefs.setInt(_cacheTimestampKey, DateTime.now().millisecondsSinceEpoch);

      logger.i('成功快取 ${charts.length} 個星盤基本資訊 (${(jsonString.length / 1024).toStringAsFixed(1)}KB)');
    } catch (e) {
      logger.w('快取星盤資料失敗: $e');
    }
  }

  /// 清除快取
  Future<void> clearCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_cachedChartsKey);
      await prefs.remove(_cacheTimestampKey);
      logger.i('已清除星盤快取');
    } catch (e) {
      logger.w('清除快取失敗: $e');
    }
  }

  @override
  void dispose() {
    super.dispose();
  }
}
