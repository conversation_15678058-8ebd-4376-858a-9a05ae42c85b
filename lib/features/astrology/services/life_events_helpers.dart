import 'package:flutter/material.dart';

/// 人生重大事件偵測輔助方法
class LifeEventsHelpers {
  
  /// 計算相位
  static Map<String, dynamic>? calculateAspect(double longitude1, double longitude2) {
    final angleDiff = (longitude1 - longitude2).abs();
    final normalizedAngle = angleDiff > 180 ? 360 - angleDiff : angleDiff;
    
    // 檢查主要相位
    if ((normalizedAngle - 0).abs() <= 8) {
      return {'type': '合相', 'orb': (normalizedAngle - 0).abs()};
    } else if ((normalizedAngle - 180).abs() <= 8) {
      return {'type': '對沖', 'orb': (normalizedAngle - 180).abs()};
    } else if ((normalizedAngle - 90).abs() <= 6) {
      return {'type': '四分相', 'orb': (normalizedAngle - 90).abs()};
    } else if ((normalizedAngle - 120).abs() <= 6) {
      return {'type': '三分相', 'orb': (normalizedAngle - 120).abs()};
    } else if ((normalizedAngle - 60).abs() <= 4) {
      return {'type': '六分相', 'orb': (normalizedAngle - 60).abs()};
    }
    
    return null;
  }

  /// 計算事件分數
  static double calculateEventScore(String planet, String aspect, int house, String eventType) {
    double baseScore = 50.0;
    
    // 行星權重
    final planetWeights = {
      'relationship': {'Venus': 20, 'Mars': 15, 'Moon': 10},
      'career': {'Sun': 25, 'Jupiter': 20, 'Saturn': 15},
      'financial': {'Jupiter': 25, 'Venus': 20, 'Saturn': 15},
      'health': {'Mars': 20, 'Saturn': 15, 'Uranus': 15, 'Pluto': 10},
      'education': {'Mercury': 20, 'Jupiter': 15, 'Uranus': 10},
      'relocation': {'Uranus': 20, 'Jupiter': 15},
      'spiritual': {'Pluto': 25, 'Neptune': 20, 'Saturn': 15},
    };
    
    if (planetWeights[eventType]?.containsKey(planet) == true) {
      baseScore += planetWeights[eventType]![planet]!;
    }
    
    // 相位權重
    switch (aspect) {
      case '合相':
        baseScore += 25;
        break;
      case '對沖':
        baseScore += 20;
        break;
      case '三分相':
        baseScore += 15;
        break;
      case '四分相':
        baseScore += 10;
        break;
      case '六分相':
        baseScore += 5;
        break;
    }
    
    // 宮位權重
    final houseWeights = {
      'relationship': {7: 25, 5: 20, 4: 15},
      'career': {10: 30, 6: 20},
      'financial': {2: 25, 8: 20},
      'health': {6: 25, 8: 20},
      'education': {3: 20, 9: 25},
      'relocation': {4: 25, 9: 20},
      'spiritual': {8: 30, 12: 25},
    };
    
    if (houseWeights[eventType]?.containsKey(house) == true) {
      baseScore += houseWeights[eventType]![house]!;
    }
    
    return baseScore;
  }

  /// 分數轉重要性等級
  static int scoreToImportance(double score) {
    if (score >= 90) return 5;
    if (score >= 75) return 4;
    if (score >= 60) return 3;
    if (score >= 45) return 2;
    return 1;
  }

  /// 獲取事件標題
  static String getEventTitle(String category, String planet, String aspect, int house) {
    final houseNames = {
      // 感情
      7: '伴侶關係', 5: '愛情運勢', 4: '家庭和諧',
      // 事業
      10: '事業發展', 6: '工作狀況',
      // 財務
      2: '個人財務', 8: '投資理財',
      // 健康
      // 學習
      3: '學習交流', 9: '高等教育',
      // 其他
    };
    
    final houseName = houseNames[house] ?? '人生轉折';
    
    final planetTitles = {
      'Venus': '金星', 'Mars': '火星', 'Moon': '月亮',
      'Sun': '太陽', 'Jupiter': '木星', 'Saturn': '土星',
      'Mercury': '水星', 'Uranus': '天王星', 'Pluto': '冥王星',
      'Neptune': '海王星',
    };
    
    final planetName = planetTitles[planet] ?? planet;
    
    return '$planetName$aspect - $houseName轉機';
  }

  /// 獲取事件描述
  static String getEventDescription(String category, String transitPlanet, String natalPlanet, String aspect, int house) {
    final houseDescriptions = {
      7: '伴侶關係和婚姻狀況',
      5: '愛情運勢和浪漫關係',
      4: '家庭和諧和親情關係',
      10: '事業發展和社會地位',
      6: '工作環境和日常職務',
      2: '個人收入和財務管理',
      8: '投資理財和資源共享',
      3: '學習交流和溝通能力',
      9: '哲學思考和高等教育',
      12: '潛意識和靈性覺醒',
    };
    
    final houseDesc = houseDescriptions[house] ?? '人生發展';
    
    final planetNames = {
      'Venus': '金星', 'Mars': '火星', 'Moon': '月亮',
      'Sun': '太陽', 'Jupiter': '木星', 'Saturn': '土星',
      'Mercury': '水星', 'Uranus': '天王星', 'Pluto': '冥王星',
      'Neptune': '海王星',
    };
    
    final transitName = planetNames[transitPlanet] ?? transitPlanet;
    final natalName = planetNames[natalPlanet] ?? natalPlanet;
    
    return '行運$transitName與本命$natalName形成$aspect，可能影響您的$houseDesc。'
        '這是一個重要的$category轉折期，建議關注相關領域的變化。';
  }

  /// 獲取事件顏色
  static Color getEventColor(String eventType, int house) {
    final colorMap = {
      'relationship': {
        7: Colors.pink,
        5: Colors.red,
        4: Colors.orange,
      },
      'career': {
        10: Colors.amber,
        6: Colors.blue,
      },
      'financial': {
        2: Colors.green,
        8: Colors.teal,
      },
      'health': {
        6: Colors.red.shade300,
        8: Colors.purple.shade300,
      },
      'education': {
        3: Colors.blue.shade300,
        9: Colors.indigo,
      },
      'relocation': {
        4: Colors.orange.shade300,
        9: Colors.blue.shade400,
      },
      'spiritual': {
        8: Colors.purple,
        12: Colors.deepPurple,
      },
    };
    
    return colorMap[eventType]?[house] ?? Colors.grey;
  }

  /// 獲取事件圖標
  static IconData getEventIcon(String eventType, int house) {
    final iconMap = {
      'relationship': {
        7: Icons.favorite,
        5: Icons.favorite_border,
        4: Icons.home,
      },
      'career': {
        10: Icons.business,
        6: Icons.work,
      },
      'financial': {
        2: Icons.account_balance_wallet,
        8: Icons.trending_up,
      },
      'health': {
        6: Icons.local_hospital,
        8: Icons.healing,
      },
      'education': {
        3: Icons.school,
        9: Icons.menu_book,
      },
      'relocation': {
        4: Icons.home_work,
        9: Icons.flight,
      },
      'spiritual': {
        8: Icons.psychology,
        12: Icons.self_improvement,
      },
    };
    
    return iconMap[eventType]?[house] ?? Icons.star;
  }
}
