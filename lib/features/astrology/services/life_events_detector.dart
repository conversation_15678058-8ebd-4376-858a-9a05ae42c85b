import 'package:flutter/material.dart';

import '../../../data/models/astrology/astro_event.dart';
import '../../../data/models/user/birth_data.dart';
import '../astrology_service.dart';
import 'life_events_helpers.dart';

/// 人生重大事件偵測器（簡化版）
class LifeEventsDetector {
  
  /// 偵測人生重大事件
  static Future<List<AstroEvent>> detectMajorLifeEvents(
    BirthData birthData,
    DateTime date,
  ) async {
    final events = <AstroEvent>[];
    
    try {
      // 計算本命行星位置
      final natalPlanets = await AstrologyService.calculatePlanetPositions(
        birthData.dateTime,
        birthData.latitude,
        birthData.longitude,
      );
      
      // 計算行運行星位置
      final transitPlanets = await AstrologyService.calculatePlanetPositions(
        date,
        birthData.latitude,
        birthData.longitude,
      );
      
      // 偵測各類重大事件
      events.addAll(_detectRelationshipEvents(natalPlanets, transitPlanets, date));
      events.addAll(_detectCareerEvents(natalPlanets, transitPlanets, date));
      events.addAll(_detectFinancialEvents(natalPlanets, transitPlanets, date));
      events.addAll(_detectHealthEvents(natalPlanets, transitPlanets, date));
      events.addAll(_detectEducationEvents(natalPlanets, transitPlanets, date));
      events.addAll(_detectRelocationEvents(natalPlanets, transitPlanets, date));
      events.addAll(_detectSpiritualEvents(natalPlanets, transitPlanets, date));
      
    } catch (e) {
      print('偵測人生重大事件時發生錯誤: $e');
    }
    
    return events;
  }

  /// 偵測感情與人際關係事件
  static List<AstroEvent> _detectRelationshipEvents(
    List<dynamic> natalPlanets,
    List<dynamic> transitPlanets,
    DateTime date,
  ) {
    final events = <AstroEvent>[];
    
    // 關鍵行星和宮位
    final keyPlanets = ['Venus', 'Moon', 'Mars'];
    final keyHouses = [5, 7, 4]; // 愛情、伴侶、家庭
    
    for (final transitPlanet in transitPlanets) {
      if (!keyPlanets.contains(transitPlanet.name)) continue;
      
      for (final natalPlanet in natalPlanets) {
        if (!keyHouses.contains(natalPlanet.house)) continue;
        
        final aspect = _calculateAspect(transitPlanet.longitude, natalPlanet.longitude);
        if (aspect != null && aspect['orb'] <= 2.0) {
          final score = _calculateEventScore(transitPlanet.name, aspect['type'], natalPlanet.house, 'relationship');
          
          if (score >= 60) {
            events.add(AstroEvent(
              id: 'relationship_${date.millisecondsSinceEpoch}_${transitPlanet.name}_${natalPlanet.name}',
              title: _getEventTitle('感情', transitPlanet.name, aspect['type'], natalPlanet.house),
              eventTypeName: '',
              description: _getEventDescription('感情與人際關係', transitPlanet.name, natalPlanet.name, aspect['type'], natalPlanet.house),
              dateTime: date,
              type: AstroEventType.transitAspect,
              importance: _scoreToImportance(score),
              isVisible: true,
              color: _getEventColor('relationship', natalPlanet.house),
              icon: _getEventIcon('relationship', natalPlanet.house),
              score: score,
              eventImportance: EventImportanceExtension.fromScore(score),
              involvedPlanets: [transitPlanet.name, natalPlanet.name],
              involvedHouses: [natalPlanet.house],
              aspectType: aspect['type'],
              orb: aspect['orb'],
              additionalData: {
                'category': '感情與人際關係',
                'keyHouse': natalPlanet.house,
                'isRelationshipEvent': true,
              },
            ));
          }
        }
      }
    }
    
    return events;
  }

  /// 偵測事業與工作轉折事件
  static List<AstroEvent> _detectCareerEvents(
    List<dynamic> natalPlanets,
    List<dynamic> transitPlanets,
    DateTime date,
  ) {
    final events = <AstroEvent>[];
    
    final keyPlanets = ['Sun', 'Jupiter', 'Saturn'];
    final keyHouses = [10, 6]; // 事業、工作
    
    for (final transitPlanet in transitPlanets) {
      if (!keyPlanets.contains(transitPlanet.name)) continue;
      
      for (final natalPlanet in natalPlanets) {
        if (!keyHouses.contains(natalPlanet.house)) continue;
        
        final aspect = _calculateAspect(transitPlanet.longitude, natalPlanet.longitude);
        if (aspect != null && aspect['orb'] <= 2.0) {
          final score = _calculateEventScore(transitPlanet.name, aspect['type'], natalPlanet.house, 'career');
          
          if (score >= 65) {
            events.add(AstroEvent(
              id: 'career_${date.millisecondsSinceEpoch}_${transitPlanet.name}_${natalPlanet.name}',
              title: _getEventTitle('事業', transitPlanet.name, aspect['type'], natalPlanet.house),
              eventTypeName: '',
              description: _getEventDescription('事業與工作轉折', transitPlanet.name, natalPlanet.name, aspect['type'], natalPlanet.house),
              dateTime: date,
              type: AstroEventType.transitAspect,
              importance: _scoreToImportance(score),
              isVisible: true,
              color: _getEventColor('career', natalPlanet.house),
              icon: _getEventIcon('career', natalPlanet.house),
              score: score,
              eventImportance: EventImportanceExtension.fromScore(score),
              involvedPlanets: [transitPlanet.name, natalPlanet.name],
              involvedHouses: [natalPlanet.house],
              aspectType: aspect['type'],
              orb: aspect['orb'],
              additionalData: {
                'category': '事業與工作轉折',
                'keyHouse': natalPlanet.house,
                'isCareerEvent': true,
              },
            ));
          }
        }
      }
    }
    
    return events;
  }

  /// 偵測財務狀況事件
  static List<AstroEvent> _detectFinancialEvents(
    List<dynamic> natalPlanets,
    List<dynamic> transitPlanets,
    DateTime date,
  ) {
    final events = <AstroEvent>[];
    
    final keyPlanets = ['Venus', 'Jupiter', 'Saturn'];
    final keyHouses = [2, 8]; // 財務、資源
    
    for (final transitPlanet in transitPlanets) {
      if (!keyPlanets.contains(transitPlanet.name)) continue;
      
      for (final natalPlanet in natalPlanets) {
        if (!keyHouses.contains(natalPlanet.house)) continue;
        
        final aspect = _calculateAspect(transitPlanet.longitude, natalPlanet.longitude);
        if (aspect != null && aspect['orb'] <= 2.0) {
          final score = _calculateEventScore(transitPlanet.name, aspect['type'], natalPlanet.house, 'financial');
          
          if (score >= 60) {
            events.add(AstroEvent(
              id: 'financial_${date.millisecondsSinceEpoch}_${transitPlanet.name}_${natalPlanet.name}',
              title: _getEventTitle('財務', transitPlanet.name, aspect['type'], natalPlanet.house),
              eventTypeName: '',
              description: _getEventDescription('財務狀況', transitPlanet.name, natalPlanet.name, aspect['type'], natalPlanet.house),
              dateTime: date,
              type: AstroEventType.transitAspect,
              importance: _scoreToImportance(score),
              isVisible: true,
              color: _getEventColor('financial', natalPlanet.house),
              icon: _getEventIcon('financial', natalPlanet.house),
              score: score,
              eventImportance: EventImportanceExtension.fromScore(score),
              involvedPlanets: [transitPlanet.name, natalPlanet.name],
              involvedHouses: [natalPlanet.house],
              aspectType: aspect['type'],
              orb: aspect['orb'],
              additionalData: {
                'category': '財務狀況',
                'keyHouse': natalPlanet.house,
                'isFinancialEvent': true,
              },
            ));
          }
        }
      }
    }
    
    return events;
  }

  /// 偵測健康與身體狀況事件
  static List<AstroEvent> _detectHealthEvents(
    List<dynamic> natalPlanets,
    List<dynamic> transitPlanets,
    DateTime date,
  ) {
    final events = <AstroEvent>[];
    
    final keyPlanets = ['Mars', 'Saturn', 'Uranus', 'Pluto'];
    final keyHouses = [6, 8]; // 健康、醫療
    
    for (final transitPlanet in transitPlanets) {
      if (!keyPlanets.contains(transitPlanet.name)) continue;
      
      for (final natalPlanet in natalPlanets) {
        if (!keyHouses.contains(natalPlanet.house)) continue;
        
        final aspect = _calculateAspect(transitPlanet.longitude, natalPlanet.longitude);
        if (aspect != null && aspect['orb'] <= 2.0) {
          final score = _calculateEventScore(transitPlanet.name, aspect['type'], natalPlanet.house, 'health');
          
          if (score >= 70) {
            events.add(AstroEvent(
              id: 'health_${date.millisecondsSinceEpoch}_${transitPlanet.name}_${natalPlanet.name}',
              title: _getEventTitle('健康', transitPlanet.name, aspect['type'], natalPlanet.house),
              eventTypeName: '',
              description: _getEventDescription('健康與身體狀況', transitPlanet.name, natalPlanet.name, aspect['type'], natalPlanet.house),
              dateTime: date,
              type: AstroEventType.transitAspect,
              importance: _scoreToImportance(score),
              isVisible: true,
              color: _getEventColor('health', natalPlanet.house),
              icon: _getEventIcon('health', natalPlanet.house),
              score: score,
              eventImportance: EventImportanceExtension.fromScore(score),
              involvedPlanets: [transitPlanet.name, natalPlanet.name],
              involvedHouses: [natalPlanet.house],
              aspectType: aspect['type'],
              orb: aspect['orb'],
              additionalData: {
                'category': '健康與身體狀況',
                'keyHouse': natalPlanet.house,
                'isHealthEvent': true,
              },
            ));
          }
        }
      }
    }
    
    return events;
  }

  /// 偵測學習與成長事件
  static List<AstroEvent> _detectEducationEvents(
    List<dynamic> natalPlanets,
    List<dynamic> transitPlanets,
    DateTime date,
  ) {
    final events = <AstroEvent>[];
    
    final keyPlanets = ['Mercury', 'Jupiter', 'Uranus'];
    final keyHouses = [3, 9]; // 學習、高等教育
    
    for (final transitPlanet in transitPlanets) {
      if (!keyPlanets.contains(transitPlanet.name)) continue;
      
      for (final natalPlanet in natalPlanets) {
        if (!keyHouses.contains(natalPlanet.house)) continue;
        
        final aspect = _calculateAspect(transitPlanet.longitude, natalPlanet.longitude);
        if (aspect != null && aspect['orb'] <= 2.0) {
          final score = _calculateEventScore(transitPlanet.name, aspect['type'], natalPlanet.house, 'education');
          
          if (score >= 55) {
            events.add(AstroEvent(
              id: 'education_${date.millisecondsSinceEpoch}_${transitPlanet.name}_${natalPlanet.name}',
              title: _getEventTitle('學習', transitPlanet.name, aspect['type'], natalPlanet.house),
              eventTypeName: '',
              description: _getEventDescription('學習與成長', transitPlanet.name, natalPlanet.name, aspect['type'], natalPlanet.house),
              dateTime: date,
              type: AstroEventType.transitAspect,
              importance: _scoreToImportance(score),
              isVisible: true,
              color: _getEventColor('education', natalPlanet.house),
              icon: _getEventIcon('education', natalPlanet.house),
              score: score,
              eventImportance: EventImportanceExtension.fromScore(score),
              involvedPlanets: [transitPlanet.name, natalPlanet.name],
              involvedHouses: [natalPlanet.house],
              aspectType: aspect['type'],
              orb: aspect['orb'],
              additionalData: {
                'category': '學習與成長',
                'keyHouse': natalPlanet.house,
                'isEducationEvent': true,
              },
            ));
          }
        }
      }
    }
    
    return events;
  }

  /// 偵測搬遷與環境變動事件
  static List<AstroEvent> _detectRelocationEvents(
    List<dynamic> natalPlanets,
    List<dynamic> transitPlanets,
    DateTime date,
  ) {
    final events = <AstroEvent>[];

    final keyPlanets = ['Uranus', 'Jupiter'];
    final keyHouses = [4, 9]; // 家庭、遠行

    for (final transitPlanet in transitPlanets) {
      if (!keyPlanets.contains(transitPlanet.name)) continue;

      for (final natalPlanet in natalPlanets) {
        if (!keyHouses.contains(natalPlanet.house)) continue;

        final aspect = _calculateAspect(transitPlanet.longitude, natalPlanet.longitude);
        if (aspect != null && aspect['orb'] <= 2.0) {
          final score = _calculateEventScore(transitPlanet.name, aspect['type'], natalPlanet.house, 'relocation');

          if (score >= 65) {
            events.add(AstroEvent(
              id: 'relocation_${date.millisecondsSinceEpoch}_${transitPlanet.name}_${natalPlanet.name}',
              title: _getEventTitle('搬遷', transitPlanet.name, aspect['type'], natalPlanet.house),
              eventTypeName: '',
              description: _getEventDescription('搬遷與環境變動', transitPlanet.name, natalPlanet.name, aspect['type'], natalPlanet.house),
              dateTime: date,
              type: AstroEventType.transitAspect,
              importance: _scoreToImportance(score),
              isVisible: true,
              color: _getEventColor('relocation', natalPlanet.house),
              icon: _getEventIcon('relocation', natalPlanet.house),
              score: score,
              eventImportance: EventImportanceExtension.fromScore(score),
              involvedPlanets: [transitPlanet.name, natalPlanet.name],
              involvedHouses: [natalPlanet.house],
              aspectType: aspect['type'],
              orb: aspect['orb'],
              additionalData: {
                'category': '搬遷與環境變動',
                'keyHouse': natalPlanet.house,
                'isRelocationEvent': true,
              },
            ));
          }
        }
      }
    }

    return events;
  }

  /// 偵測心靈與命運轉折事件
  static List<AstroEvent> _detectSpiritualEvents(
    List<dynamic> natalPlanets,
    List<dynamic> transitPlanets,
    DateTime date,
  ) {
    final events = <AstroEvent>[];

    final keyPlanets = ['Pluto', 'Neptune', 'Saturn'];
    final keyHouses = [8, 12]; // 轉化、潛意識

    for (final transitPlanet in transitPlanets) {
      if (!keyPlanets.contains(transitPlanet.name)) continue;

      for (final natalPlanet in natalPlanets) {
        if (!keyHouses.contains(natalPlanet.house)) continue;

        final aspect = _calculateAspect(transitPlanet.longitude, natalPlanet.longitude);
        if (aspect != null && aspect['orb'] <= 1.5) { // 更嚴格的容許度
          final score = _calculateEventScore(transitPlanet.name, aspect['type'], natalPlanet.house, 'spiritual');

          if (score >= 75) {
            events.add(AstroEvent(
              id: 'spiritual_${date.millisecondsSinceEpoch}_${transitPlanet.name}_${natalPlanet.name}',
              title: _getEventTitle('心靈', transitPlanet.name, aspect['type'], natalPlanet.house),
              eventTypeName: '',
              description: _getEventDescription('心靈與命運轉折', transitPlanet.name, natalPlanet.name, aspect['type'], natalPlanet.house),
              dateTime: date,
              type: AstroEventType.transitAspect,
              importance: _scoreToImportance(score),
              isVisible: true,
              color: _getEventColor('spiritual', natalPlanet.house),
              icon: _getEventIcon('spiritual', natalPlanet.house),
              score: score,
              eventImportance: EventImportanceExtension.fromScore(score),
              involvedPlanets: [transitPlanet.name, natalPlanet.name],
              involvedHouses: [natalPlanet.house],
              aspectType: aspect['type'],
              orb: aspect['orb'],
              additionalData: {
                'category': '心靈與命運轉折',
                'keyHouse': natalPlanet.house,
                'isSpiritualEvent': true,
              },
            ));
          }
        }
      }
    }

    return events;
  }

  // ==================== 輔助方法 ====================

  /// 計算相位
  static Map<String, dynamic>? _calculateAspect(double longitude1, double longitude2) {
    return LifeEventsHelpers.calculateAspect(longitude1, longitude2);
  }

  /// 計算事件分數
  static double _calculateEventScore(String planet, String aspect, int house, String eventType) {
    return LifeEventsHelpers.calculateEventScore(planet, aspect, house, eventType);
  }

  /// 分數轉重要性等級
  static int _scoreToImportance(double score) {
    return LifeEventsHelpers.scoreToImportance(score);
  }

  /// 獲取事件標題
  static String _getEventTitle(String category, String planet, String aspect, int house) {
    return LifeEventsHelpers.getEventTitle(category, planet, aspect, house);
  }

  /// 獲取事件描述
  static String _getEventDescription(String category, String transitPlanet, String natalPlanet, String aspect, int house) {
    return LifeEventsHelpers.getEventDescription(category, transitPlanet, natalPlanet, aspect, house);
  }

  /// 獲取事件顏色
  static Color _getEventColor(String eventType, int house) {
    return LifeEventsHelpers.getEventColor(eventType, house);
  }

  /// 獲取事件圖標
  static IconData _getEventIcon(String eventType, int house) {
    return LifeEventsHelpers.getEventIcon(eventType, house);
  }
}
