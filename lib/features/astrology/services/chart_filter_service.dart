import '../../../core/constants/astrology_constants.dart';
import '../../../core/utils/logger_utils.dart';
import '../../../data/models/astrology/chart_data.dart';
import '../../../shared/utils/chart_info_generators/chart_info_calculator.dart';
import '../models/chart_filter.dart';

/// 星盤篩選服務
/// 
/// 提供星盤資料的篩選功能，支援多種篩選條件和邏輯組合
class ChartFilterService {
  
  /// 應用篩選器到星盤資料列表
  /// 
  /// [charts] 要篩選的星盤資料列表
  /// [filter] 篩選器配置
  /// 
  /// 返回符合篩選條件的星盤資料列表
  static List<ChartData> applyFilter(List<ChartData> charts, ChartFilter filter) {
    if (filter.isEmpty) {
      return charts;
    }

    return charts.where((chart) => _evaluateChart(chart, filter)).toList();
  }

  /// 評估單個星盤是否符合篩選條件
  /// 
  /// [chart] 要評估的星盤資料
  /// [filter] 篩選器配置
  /// 
  /// 返回是否符合條件
  static bool _evaluateChart(ChartData chart, ChartFilter filter) {
    if (filter.groups.isEmpty) return true;

    // 評估所有篩選組
    final groupResults = filter.groups.map((group) => _evaluateGroup(chart, group)).toList();

    // 根據組邏輯操作符合併結果
    return _combineResults(groupResults, filter.groupLogicalOperator);
  }

  /// 評估篩選組
  /// 
  /// [chart] 星盤資料
  /// [group] 篩選組
  /// 
  /// 返回組評估結果
  static bool _evaluateGroup(ChartData chart, FilterGroup group) {
    if (group.conditions.isEmpty) return true;

    // 評估組內所有條件
    final conditionResults = group.conditions.map((condition) => 
        _evaluateCondition(chart, condition)).toList();

    // 根據邏輯操作符合併結果
    return _combineResults(conditionResults, group.logicalOperator);
  }

  /// 評估單個篩選條件
  /// 
  /// [chart] 星盤資料
  /// [condition] 篩選條件
  /// 
  /// 返回條件評估結果
  static bool _evaluateCondition(ChartData chart, FilterCondition condition) {
    try {
      switch (condition.type) {
        case FilterType.planetInSign:
          return _evaluatePlanetInSign(chart, condition);
        case FilterType.planetInHouse:
          return _evaluatePlanetInHouse(chart, condition);
        case FilterType.houseInSign:
          return _evaluateHouseInSign(chart, condition);
        case FilterType.planetAspect:
          return _evaluatePlanetAspect(chart, condition);
      }
    } catch (e) {
      logger.e('評估篩選條件時出錯: $e');
      return false;
    }
  }

  /// 評估行星在星座條件
  /// 
  /// [chart] 星盤資料
  /// [condition] 篩選條件
  /// 
  /// 返回評估結果
  static bool _evaluatePlanetInSign(ChartData chart, FilterCondition condition) {
    if (chart.planets == null || condition.planetName == null) {
      return false;
    }

    // 找到指定行星
    final planet = chart.planets!.firstWhere(
      (p) => p.name == condition.planetName,
      orElse: () => throw Exception('找不到行星: ${condition.planetName}'),
    );

    final planetSign = planet.sign;

    switch (condition.operator) {
      case FilterOperator.equals:
        if (condition.signName != null) {
          return planetSign == condition.signName;
        }
        if (condition.signNames != null) {
          return condition.signNames!.contains(planetSign);
        }
        return false;

      case FilterOperator.notEquals:
        if (condition.signName != null) {
          return planetSign != condition.signName;
        }
        if (condition.signNames != null) {
          return !condition.signNames!.contains(planetSign);
        }
        return false;

      case FilterOperator.contains:
        // 用於元素篩選等
        if (condition.signNames != null) {
          return condition.signNames!.contains(planetSign);
        }
        return false;

      case FilterOperator.notContains:
        if (condition.signNames != null) {
          return !condition.signNames!.contains(planetSign);
        }
        return false;
    }
  }

  /// 評估行星在宮位條件
  /// 
  /// [chart] 星盤資料
  /// [condition] 篩選條件
  /// 
  /// 返回評估結果
  static bool _evaluatePlanetInHouse(ChartData chart, FilterCondition condition) {
    if (chart.planets == null || condition.planetName == null) {
      return false;
    }

    // 找到指定行星
    final planet = chart.planets!.firstWhere(
      (p) => p.name == condition.planetName,
      orElse: () => throw Exception('找不到行星: ${condition.planetName}'),
    );

    final planetHouse = planet.house;

    switch (condition.operator) {
      case FilterOperator.equals:
        if (condition.houseNumber != null) {
          return planetHouse == condition.houseNumber;
        }
        if (condition.houseNumbers != null) {
          return condition.houseNumbers!.contains(planetHouse);
        }
        return false;

      case FilterOperator.notEquals:
        if (condition.houseNumber != null) {
          return planetHouse != condition.houseNumber;
        }
        if (condition.houseNumbers != null) {
          return !condition.houseNumbers!.contains(planetHouse);
        }
        return false;

      case FilterOperator.contains:
        if (condition.houseNumbers != null) {
          return condition.houseNumbers!.contains(planetHouse);
        }
        return false;

      case FilterOperator.notContains:
        if (condition.houseNumbers != null) {
          return !condition.houseNumbers!.contains(planetHouse);
        }
        return false;
    }
  }

  /// 評估宮位在星座條件
  /// 
  /// [chart] 星盤資料
  /// [condition] 篩選條件
  /// 
  /// 返回評估結果
  static bool _evaluateHouseInSign(ChartData chart, FilterCondition condition) {
    if (chart.houses == null || condition.houseNumber == null) {
      return false;
    }

    // 獲取宮位頭的度數
    final houseCusp = chart.houses!.cusps[condition.houseNumber!];
    
    // 根據度數獲取星座
    final houseSign = ChartInfoCalculator.getZodiacSign(houseCusp);

    switch (condition.operator) {
      case FilterOperator.equals:
        return houseSign == condition.signName;

      case FilterOperator.notEquals:
        return houseSign != condition.signName;

      case FilterOperator.contains:
        if (condition.signNames != null) {
          return condition.signNames!.contains(houseSign);
        }
        return false;

      case FilterOperator.notContains:
        if (condition.signNames != null) {
          return !condition.signNames!.contains(houseSign);
        }
        return false;
    }
  }

  /// 合併評估結果
  /// 
  /// [results] 結果列表
  /// [operator] 邏輯操作符
  /// 
  /// 返回合併後的結果
  static bool _combineResults(List<bool> results, LogicalOperator operator) {
    if (results.isEmpty) return true;

    switch (operator) {
      case LogicalOperator.and:
        return results.every((result) => result);
      case LogicalOperator.or:
        return results.any((result) => result);
    }
  }

  /// 獲取星座的元素分類
  /// 
  /// [signName] 星座名稱
  /// 
  /// 返回元素名稱
  static String getSignElement(String signName) {
    return AstrologyConstants.SIGN_ELEMENTS[signName] ?? '未知';
  }

  /// 獲取指定元素的所有星座
  /// 
  /// [element] 元素名稱（火、土、風、水）
  /// 
  /// 返回該元素的星座列表
  static List<String> getSignsByElement(String element) {
    return AstrologyConstants.SIGN_ELEMENTS.entries
        .where((entry) => entry.value == element)
        .map((entry) => entry.key)
        .toList();
  }

  /// 獲取指定性質的所有星座
  /// 
  /// [quality] 性質（基本、固定、變動）
  /// 
  /// 返回該性質的星座列表
  static List<String> getSignsByQuality(String quality) {
    final qualityMap = {
      '基本': ['牡羊座', '巨蟹座', '天秤座', '摩羯座'],
      '固定': ['金牛座', '獅子座', '天蠍座', '水瓶座'],
      '變動': ['雙子座', '處女座', '射手座', '雙魚座'],
    };
    
    return qualityMap[quality] ?? [];
  }

  /// 獲取指定極性的所有星座
  /// 
  /// [polarity] 極性（陽性、陰性）
  /// 
  /// 返回該極性的星座列表
  static List<String> getSignsByPolarity(String polarity) {
    final polarityMap = {
      '陽性': ['牡羊座', '雙子座', '獅子座', '天秤座', '射手座', '水瓶座'],
      '陰性': ['金牛座', '巨蟹座', '處女座', '天蠍座', '摩羯座', '雙魚座'],
    };
    
    return polarityMap[polarity] ?? [];
  }

  /// 獲取指定類型的所有宮位
  /// 
  /// [houseType] 宮位類型（始宮、續宮、果宮）
  /// 
  /// 返回該類型的宮位列表
  static List<int> getHousesByType(String houseType) {
    final houseTypeMap = {
      '始宮': [1, 4, 7, 10],      // 角宮
      '續宮': [2, 5, 8, 11],      // 續宮
      '果宮': [3, 6, 9, 12],      // 果宮
    };
    
    return houseTypeMap[houseType] ?? [];
  }

  /// 創建預設篩選器範例
  /// 
  /// 返回一些常用的篩選器範例
  static List<ChartFilter> getDefaultFilters() {
    return [
      // 火象星座太陽
      ChartFilter(
        id: 'fire_sun',
        name: '火象太陽',
        groups: [
          FilterGroup(
            id: 'group1',
            conditions: [
              FilterCondition(
                id: 'cond1',
                type: FilterType.planetInSign,
                operator: FilterOperator.contains,
                planetName: '太陽',
                signNames: getSignsByElement('火'),
              ),
            ],
          ),
        ],
      ),
      
      // 水星在風象星座且在第3或第9宮
      ChartFilter(
        id: 'mercury_air_communication',
        name: '溝通型水星',
        groups: [
          FilterGroup(
            id: 'group1',
            conditions: [
              FilterCondition(
                id: 'cond1',
                type: FilterType.planetInSign,
                operator: FilterOperator.contains,
                planetName: '水星',
                signNames: getSignsByElement('風'),
              ),
              FilterCondition(
                id: 'cond2',
                type: FilterType.planetInHouse,
                operator: FilterOperator.contains,
                planetName: '水星',
                houseNumbers: [3, 9],
              ),
            ],
            logicalOperator: LogicalOperator.and,
          ),
        ],
      ),
      
      // 第一宮在火象星座
      ChartFilter(
        id: 'fire_ascendant',
        name: '火象上升',
        groups: [
          FilterGroup(
            id: 'group1',
            conditions: [
              FilterCondition(
                id: 'cond1',
                type: FilterType.houseInSign,
                operator: FilterOperator.contains,
                houseNumber: 1,
                signNames: getSignsByElement('火'),
              ),
            ],
          ),
        ],
      ),
    ];
  }

  /// 評估行星相位條件
  ///
  /// [chart] 星盤資料
  /// [condition] 篩選條件
  ///
  /// 返回評估結果
  static bool _evaluatePlanetAspect(ChartData chart, FilterCondition condition) {
    if (chart.aspects == null ||
        condition.planetName == null ||
        condition.planet2Name == null) {
      return false;
    }

    // 查找兩個行星之間的相位
    final relevantAspects = chart.aspects!.where((aspect) {
      return (aspect.planet1.name == condition.planetName &&
              aspect.planet2.name == condition.planet2Name) ||
             (aspect.planet1.name == condition.planet2Name &&
              aspect.planet2.name == condition.planetName);
    }).toList();

    if (relevantAspects.isEmpty) {
      // 沒有找到相位，根據操作符返回結果
      return condition.operator == FilterOperator.notEquals ||
             condition.operator == FilterOperator.notContains;
    }

    switch (condition.operator) {
      case FilterOperator.equals:
        if (condition.aspectType != null) {
          return relevantAspects.any((aspect) => aspect.aspectType == condition.aspectType);
        }
        if (condition.aspectTypes != null) {
          return relevantAspects.any((aspect) => condition.aspectTypes!.contains(aspect.aspectType));
        }
        return false;

      case FilterOperator.notEquals:
        if (condition.aspectType != null) {
          return !relevantAspects.any((aspect) => aspect.aspectType == condition.aspectType);
        }
        if (condition.aspectTypes != null) {
          return !relevantAspects.any((aspect) => condition.aspectTypes!.contains(aspect.aspectType));
        }
        return true;

      case FilterOperator.contains:
        if (condition.aspectTypes != null) {
          return condition.aspectTypes!.any((aspectType) =>
              relevantAspects.any((aspect) => aspect.aspectType == aspectType));
        }
        return false;

      case FilterOperator.notContains:
        if (condition.aspectTypes != null) {
          return !condition.aspectTypes!.any((aspectType) =>
              relevantAspects.any((aspect) => aspect.aspectType == aspectType));
        }
        return true;
    }
  }
}
