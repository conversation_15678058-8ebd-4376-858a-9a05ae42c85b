import 'package:flutter/material.dart';

/// 人生重大事件偵測輔助方法
class MajorLifeEventsHelpers {
  
  /// 計算相位
  static Map<String, dynamic>? calculateAspect(double longitude1, double longitude2) {
    final angleDiff = (longitude1 - longitude2).abs();
    final normalizedAngle = angleDiff > 180 ? 360 - angleDiff : angleDiff;
    
    // 檢查主要相位
    if ((normalizedAngle - 0).abs() <= 8) {
      return {'type': '合相', 'orb': (normalizedAngle - 0).abs()};
    } else if ((normalizedAngle - 180).abs() <= 8) {
      return {'type': '對沖', 'orb': (normalizedAngle - 180).abs()};
    } else if ((normalizedAngle - 90).abs() <= 6) {
      return {'type': '四分相', 'orb': (normalizedAngle - 90).abs()};
    } else if ((normalizedAngle - 120).abs() <= 6) {
      return {'type': '三分相', 'orb': (normalizedAngle - 120).abs()};
    } else if ((normalizedAngle - 60).abs() <= 4) {
      return {'type': '六分相', 'orb': (normalizedAngle - 60).abs()};
    }
    
    return null;
  }

  /// 分數轉重要性等級
  static int scoreToImportance(double score) {
    if (score >= 90) return 5;
    if (score >= 75) return 4;
    if (score >= 60) return 3;
    if (score >= 45) return 2;
    return 1;
  }

  // ==================== 感情與人際關係事件 ====================
  
  /// 計算感情事件分數
  static double calculateRelationshipEventScore(
    String transitPlanet,
    String natalPlanet,
    String aspectType,
    int house,
  ) {
    double baseScore = 50.0;
    
    // 行星權重
    switch (transitPlanet) {
      case 'Venus':
        baseScore += 20;
        break;
      case 'Mars':
        baseScore += 15;
        break;
      case 'Moon':
        baseScore += 10;
        break;
    }
    
    // 相位權重
    switch (aspectType) {
      case '合相':
        baseScore += 25;
        break;
      case '對沖':
        baseScore += 20;
        break;
      case '三分相':
        baseScore += 15;
        break;
      case '四分相':
        baseScore += 10;
        break;
      case '六分相':
        baseScore += 5;
        break;
    }
    
    // 宮位權重
    switch (house) {
      case 7: // 伴侶宮
        baseScore += 25;
        break;
      case 5: // 愛情宮
        baseScore += 20;
        break;
      case 4: // 家庭宮
        baseScore += 15;
        break;
    }
    
    return baseScore;
  }

  /// 獲取感情事件標題
  static String getRelationshipEventTitle(String planet, String aspect, int house) {
    final houseNames = {7: '伴侶關係', 5: '愛情運勢', 4: '家庭和諧'};
    final houseName = houseNames[house] ?? '人際關係';
    
    switch (planet) {
      case 'Venus':
        return '金星$aspect - $houseName轉機';
      case 'Mars':
        return '火星$aspect - $houseName變動';
      case 'Moon':
        return '月亮$aspect - $houseName情感';
      default:
        return '$planet$aspect - $houseName影響';
    }
  }

  /// 獲取感情事件描述
  static String getRelationshipEventDescription(
    String transitPlanet,
    String natalPlanet,
    String aspect,
    int house,
  ) {
    final houseDescriptions = {
      7: '伴侶關係和婚姻狀況',
      5: '愛情運勢和浪漫關係',
      4: '家庭和諧和親情關係',
    };
    
    final houseDesc = houseDescriptions[house] ?? '人際關係';
    
    return '行運$transitPlanet與本命$natalPlanet形成$aspect，可能影響您的$houseDesc。'
        '這是一個重要的感情轉折期，建議關注相關領域的變化。';
  }

  /// 獲取感情事件顏色
  static Color getRelationshipEventColor(int house) {
    switch (house) {
      case 7:
        return Colors.pink;
      case 5:
        return Colors.red;
      case 4:
        return Colors.orange;
      default:
        return Colors.purple;
    }
  }

  /// 獲取感情事件圖標
  static IconData getRelationshipEventIcon(int house) {
    switch (house) {
      case 7:
        return Icons.favorite;
      case 5:
        return Icons.favorite_border;
      case 4:
        return Icons.home;
      default:
        return Icons.people;
    }
  }

  // ==================== 事業與工作轉折事件 ====================
  
  /// 計算事業事件分數
  static double _calculateCareerEventScore(
    String transitPlanet,
    String natalPlanet,
    String aspectType,
    int house,
  ) {
    double baseScore = 55.0;
    
    // 行星權重
    switch (transitPlanet) {
      case 'Sun':
        baseScore += 25;
        break;
      case 'Jupiter':
        baseScore += 20;
        break;
      case 'Saturn':
        baseScore += 15;
        break;
    }
    
    // 相位權重
    switch (aspectType) {
      case '合相':
        baseScore += 25;
        break;
      case '對沖':
        baseScore += 20;
        break;
      case '三分相':
        baseScore += 15;
        break;
      case '四分相':
        baseScore += 10;
        break;
    }
    
    // 宮位權重
    switch (house) {
      case 10: // 事業宮
        baseScore += 30;
        break;
      case 6: // 工作宮
        baseScore += 20;
        break;
    }
    
    return baseScore;
  }

  /// 獲取事業事件標題
  static String _getCareerEventTitle(String planet, String aspect, int house) {
    final houseNames = {10: '事業發展', 6: '工作狀況'};
    final houseName = houseNames[house] ?? '職業運勢';
    
    switch (planet) {
      case 'Sun':
        return '太陽$aspect - $houseName突破';
      case 'Jupiter':
        return '木星$aspect - $houseName擴展';
      case 'Saturn':
        return '土星$aspect - $houseName考驗';
      default:
        return '$planet$aspect - $houseName變化';
    }
  }

  /// 獲取事業事件描述
  static String _getCareerEventDescription(
    String transitPlanet,
    String natalPlanet,
    String aspect,
    int house,
  ) {
    final houseDescriptions = {
      10: '事業發展和社會地位',
      6: '工作環境和日常職務',
    };
    
    final houseDesc = houseDescriptions[house] ?? '職業發展';
    
    return '行運$transitPlanet與本命$natalPlanet形成$aspect，預示著$houseDesc的重要轉折。'
        '這可能是升職、轉職或事業突破的關鍵時期。';
  }

  /// 獲取事業事件顏色
  static Color _getCareerEventColor(String planet) {
    switch (planet) {
      case 'Sun':
        return Colors.amber;
      case 'Jupiter':
        return Colors.blue;
      case 'Saturn':
        return Colors.brown;
      default:
        return Colors.indigo;
    }
  }

  /// 獲取事業事件圖標
  static IconData _getCareerEventIcon(int house) {
    switch (house) {
      case 10:
        return Icons.business;
      case 6:
        return Icons.work;
      default:
        return Icons.trending_up;
    }
  }

  // ==================== 財務狀況事件 ====================
  
  /// 計算財務事件分數
  static double _calculateFinancialEventScore(
    String transitPlanet,
    String natalPlanet,
    String aspectType,
    int house,
  ) {
    double baseScore = 50.0;
    
    // 行星權重
    switch (transitPlanet) {
      case 'Jupiter':
        baseScore += 25;
        break;
      case 'Venus':
        baseScore += 20;
        break;
      case 'Saturn':
        baseScore += 15;
        break;
    }
    
    // 相位權重
    switch (aspectType) {
      case '合相':
        baseScore += 25;
        break;
      case '三分相':
        baseScore += 20;
        break;
      case '對沖':
        baseScore += 15;
        break;
      case '四分相':
        baseScore += 10;
        break;
    }
    
    // 宮位權重
    switch (house) {
      case 2: // 財務宮
        baseScore += 25;
        break;
      case 8: // 資源共享宮
        baseScore += 20;
        break;
    }
    
    return baseScore;
  }

  /// 獲取財務事件標題
  static String _getFinancialEventTitle(String planet, String aspect, int house) {
    final houseNames = {2: '個人財務', 8: '投資理財'};
    final houseName = houseNames[house] ?? '財務狀況';
    
    switch (planet) {
      case 'Jupiter':
        return '木星$aspect - $houseName機會';
      case 'Venus':
        return '金星$aspect - $houseName和諧';
      case 'Saturn':
        return '土星$aspect - $houseName考驗';
      default:
        return '$planet$aspect - $houseName變化';
    }
  }

  /// 獲取財務事件描述
  static String _getFinancialEventDescription(
    String transitPlanet,
    String natalPlanet,
    String aspect,
    int house,
  ) {
    final houseDescriptions = {
      2: '個人收入和財務管理',
      8: '投資理財和資源共享',
    };
    
    final houseDesc = houseDescriptions[house] ?? '財務狀況';
    
    return '行運$transitPlanet與本命$natalPlanet形成$aspect，可能影響您的$houseDesc。'
        '建議關注財務規劃和投資決策。';
  }

  /// 獲取財務事件顏色
  static Color _getFinancialEventColor(String planet) {
    switch (planet) {
      case 'Jupiter':
        return Colors.green;
      case 'Venus':
        return Colors.teal;
      case 'Saturn':
        return Colors.grey;
      default:
        return Colors.lightGreen;
    }
  }

  /// 獲取財務事件圖標
  static IconData _getFinancialEventIcon(int house) {
    switch (house) {
      case 2:
        return Icons.account_balance_wallet;
      case 8:
        return Icons.trending_up;
      default:
        return Icons.attach_money;
    }
  }
}
