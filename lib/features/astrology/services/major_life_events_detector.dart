import 'package:flutter/material.dart';

import '../../../data/models/astrology/astro_event.dart';

/// 人生重大事件偵測器
class MajorLifeEventsDetector {

  /// 計算相位
  static Map<String, dynamic>? _calculateAspect(double longitude1, double longitude2) {
    final angleDiff = (longitude1 - longitude2).abs();
    final normalizedAngle = angleDiff > 180 ? 360 - angleDiff : angleDiff;

    // 檢查主要相位
    if ((normalizedAngle - 0).abs() <= 8) {
      return {'type': '合相', 'orb': (normalizedAngle - 0).abs()};
    } else if ((normalizedAngle - 180).abs() <= 8) {
      return {'type': '對沖', 'orb': (normalizedAngle - 180).abs()};
    } else if ((normalizedAngle - 90).abs() <= 6) {
      return {'type': '四分相', 'orb': (normalizedAngle - 90).abs()};
    } else if ((normalizedAngle - 120).abs() <= 6) {
      return {'type': '三分相', 'orb': (normalizedAngle - 120).abs()};
    } else if ((normalizedAngle - 60).abs() <= 4) {
      return {'type': '六分相', 'orb': (normalizedAngle - 60).abs()};
    }

    return null;
  }

  /// 分數轉重要性等級
  static int _scoreToImportance(double score) {
    if (score >= 90) return 5;
    if (score >= 75) return 4;
    if (score >= 60) return 3;
    if (score >= 45) return 2;
    return 1;
  }

  /// 計算健康事件分數
  static double _calculateHealthEventScore(
    String transitPlanet,
    String natalPlanet,
    String aspect,
    int house,
  ) {
    double baseScore = 30.0;

    // 行星權重
    final planetWeights = {
      'Mars': 25,
      'Saturn': 30,
      'Uranus': 20,
      'Pluto': 35,
    };

    baseScore += planetWeights[transitPlanet] ?? 10;

    // 相位權重
    switch (aspect) {
      case '合相':
        baseScore += 25;
        break;
      case '對沖':
        baseScore += 20;
        break;
      case '三分相':
        baseScore += 15;
        break;
      case '四分相':
        baseScore += 10;
        break;
      case '六分相':
        baseScore += 5;
        break;
    }

    // 宮位權重
    if (house == 6) baseScore += 25; // 健康宮
    if (house == 8) baseScore += 20; // 醫療與康復

    return baseScore;
  }

  /// 獲取健康事件標題
  static String _getHealthEventTitle(String transitPlanet, String aspect, int house) {
    final houseNames = {6: '健康', 8: '醫療'};
    final houseName = houseNames[house] ?? '身體';

    return '$transitPlanet $aspect 影響$houseName狀況';
  }

  /// 獲取健康事件描述
  static String _getHealthEventDescription(
    String transitPlanet,
    String natalPlanet,
    String aspect,
    int house,
  ) {
    final houseNames = {6: '健康宮', 8: '醫療宮'};
    final houseName = houseNames[house] ?? '相關宮位';

    return '行運$transitPlanet與本命$natalPlanet形成$aspect相位，影響$houseName，需要特別關注身體健康狀況。';
  }

  /// 獲取健康事件顏色
  static Color _getHealthEventColor(String transitPlanet) {
    switch (transitPlanet) {
      case 'Mars':
        return Colors.red;
      case 'Saturn':
        return Colors.brown;
      case 'Uranus':
        return Colors.blue;
      case 'Pluto':
        return Colors.purple;
      default:
        return Colors.orange;
    }
  }

  /// 獲取健康事件圖標
  static IconData _getHealthEventIcon(int house) {
    switch (house) {
      case 6:
        return Icons.health_and_safety;
      case 8:
        return Icons.medical_services;
      default:
        return Icons.favorite;
    }
  }

  /// 計算教育事件分數
  static double _calculateEducationEventScore(
    String transitPlanet,
    String natalPlanet,
    String aspect,
    int house,
  ) {
    double baseScore = 25.0;

    // 行星權重
    final planetWeights = {
      'Mercury': 30,
      'Jupiter': 25,
      'Uranus': 20,
    };

    baseScore += planetWeights[transitPlanet] ?? 10;

    // 相位權重
    switch (aspect) {
      case '合相':
        baseScore += 25;
        break;
      case '對沖':
        baseScore += 15;
        break;
      case '三分相':
        baseScore += 20;
        break;
      case '四分相':
        baseScore += 10;
        break;
      case '六分相':
        baseScore += 15;
        break;
    }

    // 宮位權重
    if (house == 3) baseScore += 20; // 學習交流
    if (house == 9) baseScore += 25; // 高等教育

    return baseScore;
  }

  /// 獲取教育事件標題
  static String _getEducationEventTitle(String transitPlanet, String aspect, int house) {
    final houseNames = {3: '學習', 9: '教育'};
    final houseName = houseNames[house] ?? '成長';

    return '$transitPlanet $aspect 促進$houseName發展';
  }

  /// 獲取教育事件描述
  static String _getEducationEventDescription(
    String transitPlanet,
    String natalPlanet,
    String aspect,
    int house,
  ) {
    final houseNames = {3: '學習交流宮', 9: '高等教育宮'};
    final houseName = houseNames[house] ?? '相關宮位';

    return '行運$transitPlanet與本命$natalPlanet形成$aspect相位，影響$houseName，是學習成長的良好時機。';
  }

  /// 獲取教育事件顏色
  static Color _getEducationEventColor(String transitPlanet) {
    switch (transitPlanet) {
      case 'Mercury':
        return Colors.green;
      case 'Jupiter':
        return Colors.blue;
      case 'Uranus':
        return Colors.cyan;
      default:
        return Colors.teal;
    }
  }

  /// 獲取教育事件圖標
  static IconData _getEducationEventIcon(int house) {
    switch (house) {
      case 3:
        return Icons.school;
      case 9:
        return Icons.menu_book;
      default:
        return Icons.lightbulb;
    }
  }

  /// 計算搬遷事件分數
  static double _calculateRelocationEventScore(
    String transitPlanet,
    String natalPlanet,
    String aspect,
    int house,
  ) {
    double baseScore = 35.0;

    // 行星權重
    final planetWeights = {
      'Uranus': 30,
      'Jupiter': 25,
    };

    baseScore += planetWeights[transitPlanet] ?? 15;

    // 相位權重
    switch (aspect) {
      case '合相':
        baseScore += 25;
        break;
      case '對沖':
        baseScore += 20;
        break;
      case '三分相':
        baseScore += 15;
        break;
      case '四分相':
        baseScore += 15;
        break;
      case '六分相':
        baseScore += 10;
        break;
    }

    // 宮位權重
    if (house == 4) baseScore += 25; // 家庭宮
    if (house == 9) baseScore += 20; // 遠行宮

    return baseScore;
  }

  /// 獲取搬遷事件標題
  static String _getRelocationEventTitle(String transitPlanet, String aspect, int house) {
    final houseNames = {4: '居住', 9: '遷移'};
    final houseName = houseNames[house] ?? '環境';

    return '$transitPlanet $aspect 帶來$houseName變動';
  }

  /// 獲取搬遷事件描述
  static String _getRelocationEventDescription(
    String transitPlanet,
    String natalPlanet,
    String aspect,
    int house,
  ) {
    final houseNames = {4: '家庭宮', 9: '遠行宮'};
    final houseName = houseNames[house] ?? '相關宮位';

    return '行運$transitPlanet與本命$natalPlanet形成$aspect相位，影響$houseName，可能帶來居住環境或地點的變動。';
  }

  /// 獲取搬遷事件顏色
  static Color _getRelocationEventColor(String transitPlanet) {
    switch (transitPlanet) {
      case 'Uranus':
        return Colors.indigo;
      case 'Jupiter':
        return Colors.amber;
      default:
        return Colors.deepOrange;
    }
  }

  /// 獲取搬遷事件圖標
  static IconData _getRelocationEventIcon(int house) {
    switch (house) {
      case 4:
        return Icons.home;
      case 9:
        return Icons.flight;
      default:
        return Icons.location_on;
    }
  }

  /// 計算心靈事件分數
  static double _calculateSpiritualEventScore(
    String transitPlanet,
    String natalPlanet,
    String aspect,
    int house,
  ) {
    double baseScore = 40.0;

    // 行星權重
    final planetWeights = {
      'Pluto': 35,
      'Neptune': 30,
      'Saturn': 25,
    };

    baseScore += planetWeights[transitPlanet] ?? 15;

    // 相位權重
    switch (aspect) {
      case '合相':
        baseScore += 30;
        break;
      case '對沖':
        baseScore += 25;
        break;
      case '三分相':
        baseScore += 20;
        break;
      case '四分相':
        baseScore += 20;
        break;
      case '六分相':
        baseScore += 15;
        break;
    }

    // 宮位權重
    if (house == 8) baseScore += 30; // 死亡與重生
    if (house == 12) baseScore += 25; // 潛意識

    return baseScore;
  }

  /// 獲取心靈事件標題
  static String _getSpiritualEventTitle(String transitPlanet, String aspect, int house) {
    final houseNames = {8: '轉化', 12: '靈性'};
    final houseName = houseNames[house] ?? '心靈';

    return '$transitPlanet $aspect 帶來$houseName覺醒';
  }

  /// 獲取心靈事件描述
  static String _getSpiritualEventDescription(
    String transitPlanet,
    String natalPlanet,
    String aspect,
    int house,
  ) {
    final houseNames = {8: '轉化宮', 12: '靈性宮'};
    final houseName = houseNames[house] ?? '相關宮位';

    return '行運$transitPlanet與本命$natalPlanet形成$aspect相位，影響$houseName，是深度心靈轉化與覺醒的重要時期。';
  }

  /// 獲取心靈事件顏色
  static Color _getSpiritualEventColor(String transitPlanet) {
    switch (transitPlanet) {
      case 'Pluto':
        return Colors.deepPurple;
      case 'Neptune':
        return Colors.blueGrey;
      case 'Saturn':
        return Colors.grey;
      default:
        return Colors.purple;
    }
  }

  /// 獲取心靈事件圖標
  static IconData _getSpiritualEventIcon(int house) {
    switch (house) {
      case 8:
        return Icons.transform;
      case 12:
        return Icons.self_improvement;
      default:
        return Icons.psychology;
    }
  }

  /// 偵測健康與身體狀況事件
  static Future<List<AstroEvent>> detectHealthEvents(List<dynamic> natalPlanets,
      List<dynamic> transitPlanets,
      DateTime date,) async {
    final events = <AstroEvent>[];

    // 關鍵行星：火星、土星、天王星、冥王星
    final keyPlanets = ['Mars', 'Saturn', 'Uranus', 'Pluto'];
    // 關鍵宮位：6宮（健康）、8宮（醫療與康復）
    final keyHouses = [6, 8];

    for (final transitPlanet in transitPlanets) {
      if (!keyPlanets.contains(transitPlanet.name)) continue;

      for (final natalPlanet in natalPlanets) {
        final isKeyHouse = keyHouses.contains(natalPlanet.house);
        if (!isKeyHouse) continue;

        final aspect = _calculateAspect(
            transitPlanet.longitude, natalPlanet.longitude);
        if (aspect != null && aspect['orb'] <= 2.0) {
          final score = _calculateHealthEventScore(
            transitPlanet.name,
            natalPlanet.name,
            aspect['type'],
            natalPlanet.house,
          );

          if (score >= 70) {
            final event = AstroEvent(
              id: 'health_${date.millisecondsSinceEpoch}_${transitPlanet
                  .name}_${natalPlanet.name}',
              title: _getHealthEventTitle(
                  transitPlanet.name, aspect['type'], natalPlanet.house),
              eventTypeName: '健康與身體狀況',
              description: _getHealthEventDescription(
                  transitPlanet.name, natalPlanet.name, aspect['type'],
                  natalPlanet.house),
              dateTime: date,
              type: AstroEventType.transitAspect,
              importance: _scoreToImportance(score),
              isVisible: true,
              color: _getHealthEventColor(transitPlanet.name),
              icon: _getHealthEventIcon(natalPlanet.house),
              score: score,
              eventImportance: EventImportanceExtension.fromScore(score),
              involvedPlanets: [transitPlanet.name, natalPlanet.name],
              involvedHouses: [natalPlanet.house],
              aspectType: aspect['type'],
              orb: aspect['orb'],
              additionalData: {
                'category': '健康與身體狀況',
                'keyHouse': natalPlanet.house,
                'isHealthEvent': true,
              },
            );

            events.add(event);
          }
        }
      }
    }

    return events;
  }

  /// 偵測學習與成長事件
  static Future<List<AstroEvent>> detectEducationEvents(
      List<dynamic> natalPlanets,
      List<dynamic> transitPlanets,
      DateTime date,) async {
    final events = <AstroEvent>[];

    // 關鍵行星：水星、木星、天王星
    final keyPlanets = ['Mercury', 'Jupiter', 'Uranus'];
    // 關鍵宮位：3宮（學習交流）、9宮（哲學與高等教育）
    final keyHouses = [3, 9];

    for (final transitPlanet in transitPlanets) {
      if (!keyPlanets.contains(transitPlanet.name)) continue;

      for (final natalPlanet in natalPlanets) {
        final isKeyHouse = keyHouses.contains(natalPlanet.house);
        if (!isKeyHouse) continue;

        final aspect = _calculateAspect(
            transitPlanet.longitude, natalPlanet.longitude);
        if (aspect != null && aspect['orb'] <= 2.0) {
          final score = _calculateEducationEventScore(
            transitPlanet.name,
            natalPlanet.name,
            aspect['type'],
            natalPlanet.house,
          );

          if (score >= 55) {
            final event = AstroEvent(
              id: 'education_${date.millisecondsSinceEpoch}_${transitPlanet
                  .name}_${natalPlanet.name}',
              title: _getEducationEventTitle(
                  transitPlanet.name, aspect['type'], natalPlanet.house),
              eventTypeName: '學習與成長',
              description: _getEducationEventDescription(
                  transitPlanet.name, natalPlanet.name, aspect['type'],
                  natalPlanet.house),
              dateTime: date,
              type: AstroEventType.transitAspect,
              importance: _scoreToImportance(score),
              isVisible: true,
              color: _getEducationEventColor(transitPlanet.name),
              icon: _getEducationEventIcon(natalPlanet.house),
              score: score,
              eventImportance: EventImportanceExtension.fromScore(score),
              involvedPlanets: [transitPlanet.name, natalPlanet.name],
              involvedHouses: [natalPlanet.house],
              aspectType: aspect['type'],
              orb: aspect['orb'],
              additionalData: {
                'category': '學習與成長',
                'keyHouse': natalPlanet.house,
                'isEducationEvent': true,
              },
            );

            events.add(event);
          }
        }
      }
    }

    return events;
  }

  /// 偵測搬遷與環境變動事件
  static Future<List<AstroEvent>> detectRelocationEvents(
      List<dynamic> natalPlanets,
      List<dynamic> transitPlanets,
      DateTime date,) async {
    final events = <AstroEvent>[];

    // 關鍵行星：天王星、木星
    final keyPlanets = ['Uranus', 'Jupiter'];
    // 關鍵宮位：4宮（家）、9宮（遠行）
    final keyHouses = [4, 9];

    for (final transitPlanet in transitPlanets) {
      if (!keyPlanets.contains(transitPlanet.name)) continue;

      for (final natalPlanet in natalPlanets) {
        final isKeyHouse = keyHouses.contains(natalPlanet.house);
        if (!isKeyHouse) continue;

        final aspect = _calculateAspect(
            transitPlanet.longitude, natalPlanet.longitude);
        if (aspect != null && aspect['orb'] <= 2.0) {
          final score = _calculateRelocationEventScore(
            transitPlanet.name,
            natalPlanet.name,
            aspect['type'],
            natalPlanet.house,
          );

          if (score >= 65) {
            final event = AstroEvent(
              id: 'relocation_${date.millisecondsSinceEpoch}_${transitPlanet
                  .name}_${natalPlanet.name}',
              title: _getRelocationEventTitle(
                  transitPlanet.name, aspect['type'], natalPlanet.house),
              eventTypeName: '搬遷與環境變動',
              description: _getRelocationEventDescription(
                  transitPlanet.name, natalPlanet.name, aspect['type'],
                  natalPlanet.house),
              dateTime: date,
              type: AstroEventType.transitAspect,
              importance: _scoreToImportance(score),
              isVisible: true,
              color: _getRelocationEventColor(transitPlanet.name),
              icon: _getRelocationEventIcon(natalPlanet.house),
              score: score,
              eventImportance: EventImportanceExtension.fromScore(score),
              involvedPlanets: [transitPlanet.name, natalPlanet.name],
              involvedHouses: [natalPlanet.house],
              aspectType: aspect['type'],
              orb: aspect['orb'],
              additionalData: {
                'category': '搬遷與環境變動',
                'keyHouse': natalPlanet.house,
                'isRelocationEvent': true,
              },
            );

            events.add(event);
          }
        }
      }
    }

    return events;
  }

  /// 偵測心靈與命運轉折事件
  static Future<List<AstroEvent>> detectSpiritualEvents(
      List<dynamic> natalPlanets,
      List<dynamic> transitPlanets,
      DateTime date,) async {
    final events = <AstroEvent>[];

    // 關鍵行星：冥王星、海王星、土星
    final keyPlanets = ['Pluto', 'Neptune', 'Saturn'];
    // 關鍵宮位：8宮（死亡與重生）、12宮（潛意識）
    final keyHouses = [8, 12];

    for (final transitPlanet in transitPlanets) {
      if (!keyPlanets.contains(transitPlanet.name)) continue;

      for (final natalPlanet in natalPlanets) {
        final isKeyHouse = keyHouses.contains(natalPlanet.house);
        if (!isKeyHouse) continue;

        final aspect = _calculateAspect(
            transitPlanet.longitude, natalPlanet.longitude);
        if (aspect != null && aspect['orb'] <= 1.5) { // 更嚴格的容許度
          final score = _calculateSpiritualEventScore(
            transitPlanet.name,
            natalPlanet.name,
            aspect['type'],
            natalPlanet.house,
          );

          if (score >= 75) {
            final event = AstroEvent(
              id: 'spiritual_${date.millisecondsSinceEpoch}_${transitPlanet
                  .name}_${natalPlanet.name}',
              title: _getSpiritualEventTitle(
                  transitPlanet.name, aspect['type'], natalPlanet.house),
              eventTypeName: '心靈與命運轉折',
              description: _getSpiritualEventDescription(
                  transitPlanet.name, natalPlanet.name, aspect['type'],
                  natalPlanet.house),
              dateTime: date,
              type: AstroEventType.transitAspect,
              importance: _scoreToImportance(score),
              isVisible: true,
              color: _getSpiritualEventColor(transitPlanet.name),
              icon: _getSpiritualEventIcon(natalPlanet.house),
              score: score,
              eventImportance: EventImportanceExtension.fromScore(score),
              involvedPlanets: [transitPlanet.name, natalPlanet.name],
              involvedHouses: [natalPlanet.house],
              aspectType: aspect['type'],
              orb: aspect['orb'],
              additionalData: {
                'category': '心靈與命運轉折',
                'keyHouse': natalPlanet.house,
                'isSpiritualEvent': true,
              },
            );

            events.add(event);
          }
        }
      }
    }

    return events;
  }
}