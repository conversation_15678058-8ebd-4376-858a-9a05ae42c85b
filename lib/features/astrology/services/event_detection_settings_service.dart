import 'dart:convert';

import '../../../data/models/astrology/astro_event.dart';
import '../../../data/models/astrology/event_detection_config.dart';
import '../../../shared/utils/logger_utils.dart';
import '../../../shared/utils/user_preferences.dart';

/// 事件偵測設定服務
/// 
/// 負責管理事件偵測的各種設定，包括：
/// - 事件類型啟用狀態
/// - 最低事件分數閾值
/// - 快取有效期設定
class EventDetectionSettingsService {
  static EventDetectionSettingsService? _instance;
  
  /// 單例模式
  static EventDetectionSettingsService get instance {
    _instance ??= EventDetectionSettingsService._();
    return _instance!;
  }
  
  EventDetectionSettingsService._();

  /// 獲取當前的事件偵測配置
  Future<EventDetectionConfig> getCurrentConfig() async {
    try {
      // 載入啟用的事件類型
      final enabledTypes = await getEnabledEventTypes();
      
      // 載入最低事件分數
      final minimumScore = await UserPreferences.getEventMinimumScore();
      
      // 創建配置（使用預設的其他設定）
      final defaultConfig = EventDetectionConfig.defaultConfig();
      
      return defaultConfig.copyWith(
        enabledEventTypes: enabledTypes,
        minimumEventScore: minimumScore,
      );
    } catch (e) {
      logger.e('獲取事件偵測配置失敗: $e');
      return EventDetectionConfig.defaultConfig();
    }
  }

  /// 獲取啟用的事件類型
  Future<Set<AstroEventType>> getEnabledEventTypes() async {
    try {
      final enabledTypesJson = await UserPreferences.getEventEnabledTypes();
      
      if (enabledTypesJson != null) {
        final enabledTypesList = jsonDecode(enabledTypesJson) as List;
        return enabledTypesList
            .map((index) => AstroEventType.values[index as int])
            .toSet();
      } else {
        // 預設啟用的事件類型
        return {
          AstroEventType.transitAspect,
          AstroEventType.progressionAspect,
        };
      }
    } catch (e) {
      logger.e('獲取啟用事件類型失敗: $e');
      return {
        AstroEventType.transitAspect,
        AstroEventType.progressionAspect,
      };
    }
  }

  /// 設定啟用的事件類型
  Future<bool> setEnabledEventTypes(Set<AstroEventType> eventTypes) async {
    try {
      final enabledTypesIndices = eventTypes.map((type) => type.index).toList();
      final result = await UserPreferences.saveEventEnabledTypes(
        jsonEncode(enabledTypesIndices),
      );
      
      if (result) {
        logger.d('事件類型設定已更新: ${eventTypes.length} 個類型');
      }
      
      return result;
    } catch (e) {
      logger.e('設定啟用事件類型失敗: $e');
      return false;
    }
  }

  /// 切換單個事件類型的啟用狀態
  Future<bool> toggleEventType(AstroEventType eventType, bool enabled) async {
    try {
      final currentTypes = await getEnabledEventTypes();
      
      if (enabled) {
        currentTypes.add(eventType);
      } else {
        currentTypes.remove(eventType);
      }
      
      return await setEnabledEventTypes(currentTypes);
    } catch (e) {
      logger.e('切換事件類型失敗: $e');
      return false;
    }
  }

  /// 檢查事件類型是否啟用
  Future<bool> isEventTypeEnabled(AstroEventType eventType) async {
    try {
      final enabledTypes = await getEnabledEventTypes();
      return enabledTypes.contains(eventType);
    } catch (e) {
      logger.e('檢查事件類型啟用狀態失敗: $e');
      return false;
    }
  }

  /// 獲取最低事件分數
  Future<double> getMinimumEventScore() async {
    return await UserPreferences.getEventMinimumScore();
  }

  /// 設定最低事件分數
  Future<bool> setMinimumEventScore(double score) async {
    try {
      final result = await UserPreferences.saveEventMinimumScore(score);
      
      if (result) {
        logger.d('最低事件分數已更新: $score');
      }
      
      return result;
    } catch (e) {
      logger.e('設定最低事件分數失敗: $e');
      return false;
    }
  }

  /// 獲取快取有效期（天數）
  Future<int> getCacheExpiryDays() async {
    return await UserPreferences.getEventCacheExpiryDays();
  }

  /// 設定快取有效期（天數）
  Future<bool> setCacheExpiryDays(int days) async {
    try {
      final result = await UserPreferences.saveEventCacheExpiryDays(days);
      
      if (result) {
        logger.d('快取有效期已更新: $days 天');
      }
      
      return result;
    } catch (e) {
      logger.e('設定快取有效期失敗: $e');
      return false;
    }
  }

  /// 重置所有設定為預設值
  Future<bool> resetToDefaults() async {
    try {
      // 重置事件類型為預設值
      final defaultTypes = {
        AstroEventType.transitAspect,
        AstroEventType.progressionAspect,
      };
      
      final typeResult = await setEnabledEventTypes(defaultTypes);
      final scoreResult = await setMinimumEventScore(20.0);
      final cacheResult = await setCacheExpiryDays(7);
      
      final success = typeResult && scoreResult && cacheResult;
      
      if (success) {
        logger.i('事件偵測設定已重置為預設值');
      }
      
      return success;
    } catch (e) {
      logger.e('重置設定失敗: $e');
      return false;
    }
  }

  /// 獲取事件類型的顯示名稱
  String getEventTypeName(AstroEventType eventType) {
    switch (eventType) {
      case AstroEventType.transitAspect:
        return '行運相位';
      case AstroEventType.progressionAspect:
        return '推運相位';
      case AstroEventType.solarArcAspect:
        return '太陽弧推運';
      case AstroEventType.planetSignChange:
        return '行星換座';
      case AstroEventType.planetHouseChange:
        return '行星換宮';
      case AstroEventType.moonPhase:
        return '月相';
      case AstroEventType.planetRetrograde:
        return '行星逆行';
      case AstroEventType.solarReturn:
        return '太陽返照';
      case AstroEventType.lunarReturn:
        return '月亮返照';
      case AstroEventType.eclipse:
        return '日月蝕';
      case AstroEventType.seasonChange:
        return '節氣變化';
      case AstroEventType.planetAspect:
        return '行星相位';
      default:
        return eventType.toString();
    }
  }

  /// 獲取事件類型的圖標
  String getEventTypeIcon(AstroEventType eventType) {
    switch (eventType) {
      case AstroEventType.transitAspect:
        return '🔄';
      case AstroEventType.progressionAspect:
        return '📈';
      case AstroEventType.solarArcAspect:
        return '☀️';
      case AstroEventType.planetSignChange:
        return '↔️';
      case AstroEventType.planetHouseChange:
        return '🏠';
      case AstroEventType.moonPhase:
        return '🌙';
      case AstroEventType.planetRetrograde:
        return '⏪';
      case AstroEventType.solarReturn:
        return '🎂';
      case AstroEventType.lunarReturn:
        return '🌕';
      case AstroEventType.eclipse:
        return '🌑';
      case AstroEventType.seasonChange:
        return '🍂';
      case AstroEventType.planetAspect:
        return '⭐';
      default:
        return '❓';
    }
  }

  /// 驗證設定的有效性
  Future<Map<String, dynamic>> validateSettings() async {
    final validation = <String, dynamic>{
      'isValid': true,
      'errors': <String>[],
      'warnings': <String>[],
    };

    try {
      // 檢查啟用的事件類型
      final enabledTypes = await getEnabledEventTypes();
      if (enabledTypes.isEmpty) {
        validation['errors'].add('至少需要啟用一種事件類型');
        validation['isValid'] = false;
      }

      // 檢查最低分數設定
      final minimumScore = await getMinimumEventScore();
      if (minimumScore < 0 || minimumScore > 100) {
        validation['errors'].add('最低事件分數必須在 0-100 之間');
        validation['isValid'] = false;
      }

      // 檢查快取有效期
      final cacheExpiry = await getCacheExpiryDays();
      if (cacheExpiry < 1 || cacheExpiry > 365) {
        validation['warnings'].add('建議快取有效期設定在 1-365 天之間');
      }

      // 效能警告
      if (enabledTypes.length > 3 && minimumScore < 10) {
        validation['warnings'].add('啟用多種事件類型且分數閾值較低可能影響效能');
      }

    } catch (e) {
      validation['errors'].add('驗證設定時發生錯誤: $e');
      validation['isValid'] = false;
    }

    return validation;
  }
}
