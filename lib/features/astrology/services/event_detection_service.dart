import 'package:flutter/material.dart';

import '../../../astreal.dart';
import '../../../data/models/astrology/categorized_event_score.dart';
import '../../../data/models/astrology/categorized_timeline_data.dart';
import '../../../data/models/astrology/event_detection_config.dart';
import '../../../data/models/astrology/event_score.dart';
import '../../../data/models/astrology/event_timeline_data.dart';
import '../../../data/services/api/astrology_service.dart';
import '../calculations/event_score_calculator.dart';
import 'event_cache_service.dart';

/// 事件偵測服務
///
/// 負責偵測和分析占星事件，包括：
/// - 行運事件偵測
/// - 推運事件偵測
/// - 事件評分計算
/// - 時間軸資料生成
class EventDetectionService {
  /// 事件偵測配置
  final EventDetectionConfig config;

  /// 事件評分計算器
  late final EventScoreCalculator _scoreCalculator;

  EventDetectionService({
    required this.config,
  }) {
    _scoreCalculator = EventScoreCalculator(
      config: EventScoreConfig.defaultConfig(),
    );
  }

  /// 偵測指定時間範圍內的所有事件
  ///
  /// [birthData] 出生資料
  /// [startDate] 開始日期
  /// [endDate] 結束日期
  /// [useCache] 是否使用快取
  ///
  /// 返回事件時間軸資料
  Future<EventTimelineData> detectEvents(
    BirthData birthData,
    DateTime startDate,
    DateTime endDate, {
    bool useCache = false,
  }) async {
    logger.d('開始偵測事件: ${startDate.toString()} 到 ${endDate.toString()}');

    // 嘗試從快取載入
    if (useCache) {
      final cachedData = await EventCacheService.getCachedEventData(
        birthData,
        startDate,
        endDate,
      );

      if (cachedData != null) {
        logger.d('使用快取資料');
        return cachedData;
      }
    }

    final dailyScores = <DailyEventScore>[];
    final allEvents = <AstroEvent>[];

    // 逐日偵測事件
    DateTime currentDate = startDate;
    while (currentDate.isBefore(endDate) ||
        currentDate.isAtSameMomentAs(endDate)) {
      try {
        List<AstroEvent> dayEvents =
            await _detectDailyEvents(birthData, currentDate);
        final dayScores = dayEvents.map((event) {
          // 檢查是否有 AspectInfo 數據
          final aspectInfo = event.additionalData?['aspect'] as AspectInfo?;

          if (aspectInfo != null) {
            // 使用 AspectInfo 計算評分
            return _scoreCalculator.calculateAspectScore(
              aspectInfo,
              birthData,
              isTransit: event.type == AstroEventType.transitAspect,
              isPrecise: event.isExact ?? false,
            );
          } else {
            // 使用事件本身的分數創建簡單的 EventScore
            return EventScore(
              totalScore: event.score ?? 50.0,
              planetWeight: 1.0,
              aspectStrength: 1.0,
              houseImportance: 1.0,
              timeAccuracy: 1.0,
              personalImpact: 1.0,
              explanation: '基於事件內建分數: ${event.score ?? 50.0}',
              calculatedAt: DateTime.now(),
            );
          }
        }).toList();

        final dailyScore = DailyEventScore.fromEvents(currentDate, dayScores);
        dailyScores.add(dailyScore);
        allEvents.addAll(dayEvents);

        logger.d(
            '${currentDate.toString().substring(0, 10)}: ${dayEvents.length} 個事件, 總分: ${dailyScore.totalScore.toStringAsFixed(1)}');
      } catch (e) {
        logger.e('偵測 $currentDate 事件時出錯: $e');
        // 添加空的每日評分以保持連續性
        dailyScores.add(DailyEventScore(
          date: currentDate,
          totalScore: 0,
          events: [],
          eventCount: 0,
        ));
      }

      currentDate = currentDate.add(const Duration(days: 1));
    }

    EventTimelineData timelineData = EventTimelineData.fromDailyScores(
      startDate,
      endDate,
      dailyScores,
      allEvents, // 傳遞收集到的所有事件
    );

    // 快取結果
    if (useCache) {
      await EventCacheService.cacheEventData(
        birthData,
        startDate,
        endDate,
        timelineData,
      );
    }

    logger.d('事件偵測完成: 總共 ${allEvents.length} 個事件');
    return timelineData;
  }

  /// 偵測分類事件（支援七大類事件分類）
  ///
  /// [birthData] 出生資料
  /// [startDate] 開始日期
  /// [endDate] 結束日期
  /// [useCache] 是否使用快取
  ///
  /// 返回分類事件時間軸資料
  Future<CategorizedTimelineData> detectCategorizedEvents(
    BirthData birthData,
    DateTime startDate,
    DateTime endDate, {
    bool useCache = false,
  }) async {
    logger.d('開始偵測分類事件: ${startDate.toString()} 到 ${endDate.toString()}');

    // 先獲取基本的事件資料
    final basicTimelineData = await detectEvents(
      birthData,
      startDate,
      endDate,
      useCache: useCache,
    );

    // 轉換為分類事件資料
    final categorizedDailyScores = <CategorizedEventScore>[];
    final allEvents = basicTimelineData.allEvents;

    // 為每一天創建分類評分
    for (final dailyScore in basicTimelineData.dailyScores) {
      // 獲取該天的事件
      final dayEvents = allEvents
          .where((event) => _isSameDay(event.dateTime, dailyScore.date))
          .toList();

      // 創建分類評分
      final categorizedScore = CategorizedEventScore.fromEvents(
        dailyScore.date,
        dailyScore.events,
        dayEvents,
      );

      categorizedDailyScores.add(categorizedScore);
    }

    // 創建分類時間軸資料
    final categorizedTimelineData = CategorizedTimelineData.fromDailyScores(
      startDate,
      endDate,
      categorizedDailyScores,
      allEvents,
    );

    logger.d('分類事件偵測完成: 總共 ${categorizedTimelineData.totalEventCount} 個事件');

    return categorizedTimelineData;
  }

  /// 檢查兩個日期是否為同一天
  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }

  /// 偵測單日事件（增強版）
  ///
  /// 使用多重星盤分析來偵測重大事件
  /// 1. 先分析本命盤判斷重大事件的機率
  /// 2. 再用推運相關的盤判斷重大事件發生的時間
  Future<List<AstroEvent>> _detectDailyEvents(
    BirthData birthData,
    DateTime date,
  ) async {
    final events = <AstroEvent>[];

    try {
      // 第一階段：計算各種星盤資料
      final chartDataMap = await _calculateMultipleCharts(birthData, date);

      // 第二階段：本命盤基礎分析 - 判斷重大事件機率
      final natalAnalysis = await _analyzeNatalChartForEventPotential(
        chartDataMap[ChartType.natal]!,
        birthData,
      );

      // 第三階段：推運盤分析 - 判斷事件發生時間
      final progressionAnalysis = await _analyzeProgressionChartsForTiming(
        chartDataMap,
        birthData,
        date,
        natalAnalysis,
      );

      // 第四階段：綜合分析生成事件
      final detectedEvents = await _synthesizeEventsFromAnalysis(
        chartDataMap,
        natalAnalysis,
        progressionAnalysis,
        birthData,
        date,
      );

      events.addAll(detectedEvents);

      // 第五階段：傳統事件偵測（保持向後相容）
      // final traditionalEvents = await _detectTraditionalEvents(birthData, date);
      // events.addAll(traditionalEvents);
    } catch (e) {
      logger.e('增強事件偵測失敗，回退到傳統方法: $e');
    }

    // 過濾低分事件
    return events
        .where((event) => (event.score ?? 0) >= config.minimumEventScore)
        .toList();
  }

  /// 計算多重星盤資料（根據設定選擇要計算的星盤類型）
  Future<Map<ChartType, ChartData>> _calculateMultipleCharts(
    BirthData birthData,
    DateTime date,
  ) async {
    final chartDataMap = <ChartType, ChartData>{};
    ChartSettings chartSettings = await ChartSettings.loadFromPrefs();
    chartSettings.planetVisibility = planetVisibilityHome;

    try {
      // 1. 本命盤（總是需要計算，作為基礎分析）
      final natalChart = ChartData(
        chartType: ChartType.natal,
        primaryPerson: birthData,
        specificDate: birthData.dateTime,
      );
      chartDataMap[ChartType.natal] = await AstrologyService()
          .calculateChartData(natalChart, chartSettings: chartSettings);

      // 2. 行運盤（如果啟用行運相位偵測）
      if (config.enabledEventTypes.contains(AstroEventType.transitAspect)) {
        final transitChart = ChartData(
          chartType: ChartType.transit,
          primaryPerson: birthData,
          specificDate: date,
        );
        chartDataMap[ChartType.transit] = await AstrologyService()
            .calculateChartData(transitChart, chartSettings: chartSettings);
      }

      // 3. 次限推運盤（如果啟用推運相位偵測）
      if (config.enabledEventTypes.contains(AstroEventType.progressionAspect)) {
        final secondaryChart = ChartData(
          chartType: ChartType.secondaryProgression,
          primaryPerson: birthData,
          specificDate: date,
        );
        chartDataMap[ChartType.secondaryProgression] = await AstrologyService()
            .calculateChartData(secondaryChart, chartSettings: chartSettings);
      }

      // 4. 太陽弧推運盤（如果啟用太陽弧推運偵測）
      if (config.enabledEventTypes.contains(AstroEventType.solarArcAspect)) {
        final solarArcChart = ChartData(
          chartType: ChartType.solarArcDirection,
          primaryPerson: birthData,
          specificDate: date,
        );
        chartDataMap[ChartType.solarArcDirection] = await AstrologyService()
            .calculateChartData(solarArcChart, chartSettings: chartSettings);
      }

      // 5. 太陽返照盤（如果啟用太陽返照偵測）
      if (config.enabledEventTypes.contains(AstroEventType.solarReturn)) {
        final solarReturnChart = ChartData(
          chartType: ChartType.solarReturn,
          primaryPerson: birthData,
          specificDate: date,
        );
        chartDataMap[ChartType.solarReturn] = await AstrologyService()
            .calculateChartData(solarReturnChart, chartSettings: chartSettings);
      }

      logger.d('成功計算 ${chartDataMap.length} 個星盤（根據設定選擇）');
    } catch (e) {
      logger.e('計算星盤資料時出錯: $e');
      rethrow;
    }

    return chartDataMap;
  }

  /// 分析本命盤的重大事件潛力
  Future<Map<String, dynamic>> _analyzeNatalChartForEventPotential(
    ChartData natalChart,
    BirthData birthData,
  ) async {
    final analysis = <String, dynamic>{};

    try {
      // 分析各個生活領域的敏感度
      analysis['relationship_sensitivity'] =
          _analyzeRelationshipSensitivity(natalChart);
      logger.d('分析感情敏感度: ${analysis['relationship_sensitivity']}');

      analysis['career_sensitivity'] = _analyzeCareerSensitivity(natalChart);
      logger.d('分析事業敏感度: ${analysis['career_sensitivity']}');

      analysis['financial_sensitivity'] =
          _analyzeFinancialSensitivity(natalChart);
      logger.d('分析財務敏感度: ${analysis['financial_sensitivity']}');

      analysis['health_sensitivity'] = _analyzeHealthSensitivity(natalChart);
      logger.d('分析健康敏感度: ${analysis['health_sensitivity']}');

      analysis['learning_sensitivity'] =
          _analyzeLearningGrowthSensitivity(natalChart);
      logger.d('分析學習敏感度: ${analysis['learning_sensitivity']}');

      analysis['relocation_sensitivity'] =
          _analyzeRelocationSensitivity(natalChart);
      logger.d('分析搬遷敏感度: ${analysis['relocation_sensitivity']}');

      analysis['spiritual_sensitivity'] =
          _analyzeSpiritualTransformationSensitivity(natalChart);
      logger.d('分析心靈敏感度: ${analysis['spiritual_sensitivity']}');

      // 計算整體敏感度分數
      analysis['overall_sensitivity'] = _calculateOverallSensitivity(analysis);
      logger.d('本命盤分析完成，整體敏感度: ${analysis['overall_sensitivity']}');
    } catch (e) {
      logger.e('本命盤分析失敗: $e');
      // 返回預設值
      analysis['overall_sensitivity'] = 50.0;
    }

    return analysis;
  }

  /// 分析感情與人際關係敏感度
  double _analyzeRelationshipSensitivity(ChartData natalChart) {
    double sensitivity = 0.0;

    // 檢查7宮、5宮、4宮的行星配置
    final houses = natalChart.houses;
    final planets = natalChart.planets ?? [];
    final aspects = natalChart.aspects ?? [];

    // 7宮（伴侶關係）
    if (houses != null && houses.cusps.length > 6) {
      final house7Cusp = houses.cusps[6]; // 第7宮宮頭度數
      // 檢查7宮內的行星
      for (final planet in planets) {
        if (_isPlanetInHouse(planet, 7)) {
          if (planet.name == '金星' ||
              planet.name == '月亮' ||
              planet.name == '火星') {
            sensitivity += 15.0;
          } else {
            sensitivity += 8.0;
          }
        }
      }
    }

    // 檢查金星、月亮、火星的相位
    for (final aspect in aspects) {
      if (_isRelationshipPlanet(aspect.planet1.name) ||
          _isRelationshipPlanet(aspect.planet2.name)) {
        if (_isChallengingAspect(aspect.aspectType)) {
          sensitivity += 10.0;
        } else if (_isHarmoniousAspect(aspect.aspectType)) {
          sensitivity += 5.0;
        }
      }
    }

    return sensitivity.clamp(0.0, 100.0);
  }

  /// 分析事業與工作轉折敏感度
  double _analyzeCareerSensitivity(ChartData natalChart) {
    double sensitivity = 0.0;

    final houses = natalChart.houses;
    final planets = natalChart.planets ?? [];
    final aspects = natalChart.aspects ?? [];

    // 10宮（事業）和6宮（工作）
    if (houses != null && houses.cusps.length > 9) {
      // 檢查重要行星在事業宮位
      for (final planet in planets) {
        if (_isPlanetInHouse(planet, 10)) {
          if (planet.name == '太陽' ||
              planet.name == '木星' ||
              planet.name == '土星') {
            sensitivity += 15.0;
          } else {
            sensitivity += 8.0;
          }
        }
        if (_isPlanetInHouse(planet, 6)) {
          if (planet.name == '火星' || planet.name == '水星') {
            sensitivity += 10.0;
          } else {
            sensitivity += 5.0;
          }
        }
      }
    }

    // 檢查太陽、土星、木星的相位
    for (final aspect in aspects) {
      if (_isCareerPlanet(aspect.planet1.name) ||
          _isCareerPlanet(aspect.planet2.name)) {
        if (_isChallengingAspect(aspect.aspectType)) {
          sensitivity += 12.0;
        } else if (_isHarmoniousAspect(aspect.aspectType)) {
          sensitivity += 6.0;
        }
      }
    }

    return sensitivity.clamp(0.0, 100.0);
  }

  /// 分析財務狀況敏感度
  double _analyzeFinancialSensitivity(ChartData natalChart) {
    double sensitivity = 0.0;

    final houses = natalChart.houses;
    final planets = natalChart.planets ?? [];

    // 2宮（個人財務）和8宮（共同財務）
    if (houses != null && houses.cusps.length > 7) {
      for (final planet in planets) {
        if (_isPlanetInHouse(planet, 2) || _isPlanetInHouse(planet, 8)) {
          if (planet.name == '金星' ||
              planet.name == '木星' ||
              planet.name == '土星') {
            sensitivity += 12.0;
          } else {
            sensitivity += 6.0;
          }
        }
      }
    }

    return sensitivity.clamp(0.0, 100.0);
  }

  /// 分析健康與身體狀況敏感度
  double _analyzeHealthSensitivity(ChartData natalChart) {
    double sensitivity = 0.0;

    final houses = natalChart.houses;
    final planets = natalChart.planets ?? [];

    // 6宮（健康）和8宮（生死）
    if (houses != null && houses.cusps.length > 7) {
      for (final planet in planets) {
        if (_isPlanetInHouse(planet, 6) || _isPlanetInHouse(planet, 8)) {
          if (planet.name == '火星' ||
              planet.name == '土星' ||
              planet.name == '天王星' ||
              planet.name == '冥王星') {
            sensitivity += 15.0;
          } else {
            sensitivity += 8.0;
          }
        }
      }
    }

    return sensitivity.clamp(0.0, 100.0);
  }

  /// 分析學習與成長敏感度
  double _analyzeLearningGrowthSensitivity(ChartData natalChart) {
    double sensitivity = 0.0;

    final houses = natalChart.houses;
    final planets = natalChart.planets ?? [];

    // 3宮（學習）和9宮（高等教育、哲學）
    if (houses != null && houses.cusps.length > 8) {
      for (final planet in planets) {
        if (_isPlanetInHouse(planet, 3) || _isPlanetInHouse(planet, 9)) {
          if (planet.name == '水星' ||
              planet.name == '木星' ||
              planet.name == '天王星') {
            sensitivity += 12.0;
          } else {
            sensitivity += 6.0;
          }
        }
      }
    }

    return sensitivity.clamp(0.0, 100.0);
  }

  /// 分析搬遷與環境變動敏感度
  double _analyzeRelocationSensitivity(ChartData natalChart) {
    double sensitivity = 0.0;

    final houses = natalChart.houses;
    final planets = natalChart.planets ?? [];

    // 4宮（家庭、居住）和9宮（遠行）
    if (houses != null && houses.cusps.length > 8) {
      for (final planet in planets) {
        if (_isPlanetInHouse(planet, 4) || _isPlanetInHouse(planet, 9)) {
          if (planet.name == '天王星' || planet.name == '木星') {
            sensitivity += 15.0;
          } else {
            sensitivity += 8.0;
          }
        }
      }
    }

    return sensitivity.clamp(0.0, 100.0);
  }

  /// 分析心靈與命運轉折敏感度
  double _analyzeSpiritualTransformationSensitivity(ChartData natalChart) {
    double sensitivity = 0.0;

    final houses = natalChart.houses;
    final planets = natalChart.planets ?? [];

    // 8宮（轉化）和12宮（靈性）
    if (houses != null && houses.cusps.length > 11) {
      for (final planet in planets) {
        if (_isPlanetInHouse(planet, 8) || _isPlanetInHouse(planet, 12)) {
          if (planet.name == '冥王星' ||
              planet.name == '海王星' ||
              planet.name == '土星') {
            sensitivity += 15.0;
          } else {
            sensitivity += 8.0;
          }
        }
      }
    }

    return sensitivity.clamp(0.0, 100.0);
  }

  /// 計算整體敏感度
  double _calculateOverallSensitivity(Map<String, dynamic> analysis) {
    double total = 0.0;
    int count = 0;

    for (final key in analysis.keys) {
      if (key.endsWith('_sensitivity') && analysis[key] is double) {
        total += analysis[key] as double;
        count++;
      }
    }

    return count > 0 ? total / count : 50.0;
  }

  /// 分析推運盤的時間觸發（根據設定選擇要分析的類型）
  Future<Map<String, dynamic>> _analyzeProgressionChartsForTiming(
    Map<ChartType, ChartData> chartDataMap,
    BirthData birthData,
    DateTime date,
    Map<String, dynamic> natalAnalysis,
  ) async {
    final analysis = <String, dynamic>{};

    try {
      // 行運分析（如果啟用行運相位偵測）
      if (config.enabledEventTypes.contains(AstroEventType.transitAspect) &&
          chartDataMap.containsKey(ChartType.transit)) {
        analysis['transit_triggers'] = await _analyzeTransitTriggers(
          chartDataMap[ChartType.transit]!,
          natalAnalysis,
        );
        logger.d('行運分析完成：${(analysis['transit_triggers'] as List).length} 個觸發');
      }

      // 次限推運分析（如果啟用推運相位偵測）
      if (config.enabledEventTypes.contains(AstroEventType.progressionAspect) &&
          chartDataMap.containsKey(ChartType.secondaryProgression)) {
        analysis['progression_triggers'] = await _analyzeProgressionTriggers(
          chartDataMap[ChartType.secondaryProgression]!,
          natalAnalysis,
        );
        logger.d(
            '次限推運分析完成：${(analysis['progression_triggers'] as List).length} 個觸發');
      }

      // 太陽弧推運分析（如果啟用太陽弧推運偵測）
      if (config.enabledEventTypes.contains(AstroEventType.solarArcAspect) &&
          chartDataMap.containsKey(ChartType.solarArcDirection)) {
        analysis['solar_arc_triggers'] = await _analyzeSolarArcTriggers(
          chartDataMap[ChartType.solarArcDirection]!,
          natalAnalysis,
        );
        logger.d(
            '太陽弧推運分析完成：${(analysis['solar_arc_triggers'] as List).length} 個觸發');
      }

      // 太陽返照分析（如果啟用太陽返照偵測）
      if (config.enabledEventTypes.contains(AstroEventType.solarReturn) &&
          chartDataMap.containsKey(ChartType.solarReturn)) {
        analysis['solar_return_triggers'] = await _analyzeSolarReturnTriggers(
          chartDataMap[ChartType.solarReturn]!,
          natalAnalysis,
          date,
        );
        logger.d(
            '太陽返照分析完成：${(analysis['solar_return_triggers'] as List).length} 個觸發');
      }

      logger.d('推運分析完成，總共 ${analysis.keys.length} 種分析類型');
    } catch (e) {
      logger.e('推運分析失敗: $e');
    }

    return analysis;
  }

  /// 分析行運觸發
  Future<List<Map<String, dynamic>>> _analyzeTransitTriggers(
    ChartData transitChart,
    Map<String, dynamic> natalAnalysis,
  ) async {
    final triggers = <Map<String, dynamic>>[];
    // 檢查行運行星與本命行星的相位
    transitChart.aspects?.forEach((aspect) {
      logger.d(
          '行運相位: ${aspect.planet1.name} ${aspect.aspectType} ${aspect.planet2.name}');
      if (aspect.orb <= 3.0) {
        // 3度容許度
        final aspectType = aspect.aspectType;

        if (_isSignificantAspect(aspectType)) {
          final trigger = {
            'type': 'transit_aspect',
            'aspect': aspect,
            'transit_planet': aspect.planet2.name,
            'natal_planet': aspect.planet1.name,
            'aspectType': aspectType,
            'orb': aspect.orb,
            'strength': _calculateAspectStrength(aspectType, aspect.orb),
            'event_potential': _calculateEventPotential(
              aspect.planet2.name,
              aspect.planet1.name,
              aspectType,
              natalAnalysis,
              planet1House: aspect.planet1.house,
              planet2House: aspect.planet2.house,
            ),
          };
          triggers.add(trigger);
        }
      }
    });
    return triggers;
  }

  /// 分析推運觸發
  Future<List<Map<String, dynamic>>> _analyzeProgressionTriggers(
    ChartData progressionChart,
    Map<String, dynamic> natalAnalysis,
  ) async {
    final triggers = <Map<String, dynamic>>[];
    // 檢查推運行星與本命行星的相位
    progressionChart.aspects?.forEach((aspect) {
      logger.d(
          '次限推運相位: ${aspect.planet1.name} ${aspect.aspectType} ${aspect.planet2.name}');
      if (aspect.orb <= 1.0) {
        // 1度容許度
        final aspectType = aspect.aspectType;
        if (_isSignificantAspect(aspectType)) {
          final trigger = {
            'type': 'progression_aspect',
            'progression_planet': aspect.planet1.name,
            'natal_planet': aspect.planet2.name,
            'aspect': aspectType,
            'orb': aspect.orb,
            'strength': _calculateAspectStrength(aspectType, aspect.orb),
            'event_potential': _calculateEventPotential(
              aspect.planet1.name,
              aspect.planet2.name,
              aspectType,
              natalAnalysis,
              planet1House: aspect.planet1.house,
              planet2House: aspect.planet2.house,
            ),
          };
          triggers.add(trigger);
        }
      }
    });

    return triggers;
  }

  /// 分析太陽弧推運觸發
  Future<List<Map<String, dynamic>>> _analyzeSolarArcTriggers(
    ChartData solarArcChart,
    Map<String, dynamic> natalAnalysis,
  ) async {
    final triggers = <Map<String, dynamic>>[];

    // 檢查太陽弧推運行星與本命行星的相位
    solarArcChart.aspects?.forEach((aspect) {
      logger.d(
          '太陽弧推運相位: ${aspect.planet1.name} ${aspect.aspectType} ${aspect.planet2.name}');
      if (aspect.orb <= 1.0) {
        // 1度容許度
        final aspectType = aspect.aspectType;
        if (_isSignificantAspect(aspectType)) {
          final trigger = {
            'type': 'solar_arc_aspect',
            'solar_arc_planet': aspect.planet1.name,
            'natal_planet': aspect.planet2.name,
            'aspect': aspectType,
            'orb': aspect.orb,
            'strength': _calculateAspectStrength(aspectType, aspect.orb),
            'event_potential': _calculateEventPotential(
              aspect.planet1.name,
              aspect.planet2.name,
              aspectType,
              natalAnalysis,
              planet1House: aspect.planet1.house,
              planet2House: aspect.planet2.house,
            ),
          };
          triggers.add(trigger);
        }
      }
    });
    return triggers;
  }

  /// 分析太陽返照觸發
  Future<List<Map<String, dynamic>>> _analyzeSolarReturnTriggers(
    ChartData solarReturnChart,
    Map<String, dynamic> natalAnalysis,
    DateTime date,
  ) async {
    final triggers = <Map<String, dynamic>>[];

    try {
      // 檢查太陽返照盤中的重要相位
      solarReturnChart.aspects?.forEach((aspect) {
        logger.d(
            '太陽返照相位: ${aspect.planet1.name} ${aspect.aspectType} ${aspect.planet2.name}');
        if (aspect.orb <= 3.0) {
          // 3度容許度
          final aspectType = aspect.aspectType;
          if (_isSignificantAspect(aspectType)) {
            final trigger = {
              'type': 'solar_return_aspect',
              'planet1': aspect.planet1.name,
              'planet2': aspect.planet2.name,
              'aspect': aspectType,
              'orb': aspect.orb,
              'strength': _calculateAspectStrength(aspectType, aspect.orb),
              'event_potential': _calculateEventPotential(
                aspect.planet1.name,
                aspect.planet2.name,
                aspectType,
                natalAnalysis,
                planet1House: aspect.planet1.house,
                planet2House: aspect.planet2.house,
              ),
            };
            triggers.add(trigger);
          }
        }
      });

      // 檢查太陽返照盤中行星的宮位位置（重要的生活領域指示）
      final planets = solarReturnChart.planets ?? [];
      for (final planet in planets) {
        // 檢查重要行星在重要宮位的配置
        if (_isImportantPlanetForSolarReturn(planet.name) &&
            _isImportantHouseForSolarReturn(planet.house)) {
          final trigger = {
            'type': 'solar_return_placement',
            'planet': planet.name,
            'house': planet.house,
            'sign': planet.sign,
            'strength':
                _calculatePlanetHouseStrength(planet.name, planet.house),
            'event_potential': _calculateHousePlacementEventPotential(
              planet.name,
              planet.house,
              natalAnalysis,
            ),
          };
          triggers.add(trigger);
        }
      }
    } catch (e) {
      logger.e('分析太陽返照觸發失敗: $e');
    }

    return triggers;
  }

  /// 綜合分析生成事件
  Future<List<AstroEvent>> _synthesizeEventsFromAnalysis(
    Map<ChartType, ChartData> chartDataMap,
    Map<String, dynamic> natalAnalysis,
    Map<String, dynamic> progressionAnalysis,
    BirthData birthData,
    DateTime date,
  ) async {
    final events = <AstroEvent>[];

    try {
      // 處理行運觸發
      if (progressionAnalysis.containsKey('transit_triggers')) {
        final transitTriggers = progressionAnalysis['transit_triggers']
            as List<Map<String, dynamic>>;
        for (final trigger in transitTriggers) {
          final event =
              await _createEventFromTrigger(trigger, date, natalAnalysis);
          if (event != null) events.add(event);
        }
      }

      // 處理推運觸發
      if (progressionAnalysis.containsKey('progression_triggers')) {
        final progressionTriggers = progressionAnalysis['progression_triggers']
            as List<Map<String, dynamic>>;
        for (final trigger in progressionTriggers) {
          final event =
              await _createEventFromTrigger(trigger, date, natalAnalysis);
          if (event != null) events.add(event);
        }
      }

      // 處理太陽弧推運觸發
      if (progressionAnalysis.containsKey('solar_arc_triggers')) {
        final solarArcTriggers = progressionAnalysis['solar_arc_triggers']
            as List<Map<String, dynamic>>;
        for (final trigger in solarArcTriggers) {
          final event =
              await _createEventFromTrigger(trigger, date, natalAnalysis);
          if (event != null) events.add(event);
        }
      }

      // 處理太陽返照觸發
      if (progressionAnalysis.containsKey('solar_return_triggers')) {
        final solarReturnTriggers = progressionAnalysis['solar_return_triggers']
            as List<Map<String, dynamic>>;
        for (final trigger in solarReturnTriggers) {
          final event =
              await _createEventFromTrigger(trigger, date, natalAnalysis);
          if (event != null) events.add(event);
        }
      }
    } catch (e) {
      logger.e('綜合分析生成事件失敗: $e');
    }

    return events;
  }

  /// 從觸發器創建事件
  Future<AstroEvent?> _createEventFromTrigger(
    Map<String, dynamic> trigger,
    DateTime date,
    Map<String, dynamic> natalAnalysis,
  ) async {
    try {
      final eventPotential = trigger['event_potential'] as Map<String, dynamic>;
      final eventType = eventPotential['primary_event_type'] as String;
      final eventTypeName = _getEventTypeDisplayName(eventType);
      final score = eventPotential['score'] as double;

      if (score < config.minimumEventScore) return null;

      final title = _generateEventTitle(trigger, eventType);
      final description =
          _generateEventDescription(trigger, eventType, natalAnalysis);

      return AstroEvent(
        id: 'enhanced_${date.millisecondsSinceEpoch}_${trigger.hashCode}',
        title: title,
        eventTypeName: eventTypeName,
        description: description,
        dateTime: date,
        type: _mapEventTypeToAstroEventType(eventType),
        color: _getEventTypeColor(eventType),
        icon: _getEventTypeIcon(eventType),
        importance: _calculateImportanceLevel(score),
        isVisible: true,
        score: score,
        eventImportance: EventImportanceExtension.fromScore(score),
        involvedPlanets: _extractInvolvedPlanets(trigger),
        aspectType: trigger['aspectType'] as String?,
        orb: trigger['orb'] as double?,
        isExact: (trigger['orb'] as double?) != null &&
            (trigger['orb'] as double) < 0.5,
        additionalData: {
          'trigger_data': trigger,
          'event_category': eventType,
          'analysis_source': 'enhanced_detection',
        },
      );
    } catch (e) {
      logger.e('創建事件失敗: $e');
      return null;
    }
  }

  // 輔助方法
  bool _isPlanetInHouse(PlanetPosition planet, int houseNumber) {
    // 檢查行星是否在指定宮位
    return planet.house == houseNumber;
  }

  bool _isRelationshipPlanet(String planetName) {
    return ['金星', '月亮', '火星'].contains(planetName);
  }

  bool _isCareerPlanet(String planetName) {
    return ['太陽', '土星', '木星'].contains(planetName);
  }

  /// 判斷是否為健康相關行星
  bool _isHealthPlanet(String planetName) {
    return ['火星', '土星', '天王星', '冥王星', '月亮'].contains(planetName);
  }

  /// 判斷是否為財務相關行星
  bool _isFinancePlanet(String planetName) {
    return ['金星', '木星', '土星', '冥王星'].contains(planetName);
  }

  /// 判斷是否為學習相關行星
  bool _isEducationPlanet(String planetName) {
    return ['水星', '木星', '天王星'].contains(planetName);
  }

  /// 判斷是否為家庭相關行星
  bool _isFamilyPlanet(String planetName) {
    return ['月亮', '太陽', '土星', '冥王星'].contains(planetName);
  }

  /// 判斷是否為心靈相關行星
  bool _isSpiritualPlanet(String planetName) {
    return ['海王星', '冥王星', '木星', '月亮'].contains(planetName);
  }

  /// 判斷是否為感情相關宮位
  bool _isRelationshipHouse(int? house) {
    if (house == null) return false;
    return [5, 7, 8, 11].contains(house); // 第5宮(戀愛)、第7宮(伴侶)、第8宮(親密關係)、第11宮(友情)
  }

  /// 判斷是否為事業相關宮位
  bool _isCareerHouse(int? house) {
    if (house == null) return false;
    return [2, 6, 10].contains(house); // 第2宮(財富)、第6宮(工作)、第10宮(事業)
  }

  /// 判斷是否為健康相關宮位
  bool _isHealthHouse(int? house) {
    if (house == null) return false;
    return [1, 6, 8, 12].contains(house); // 第1宮(體質)、第6宮(健康)、第8宮(醫療)、第12宮(慢性病)
  }

  /// 判斷是否為學習相關宮位
  bool _isEducationHouse(int? house) {
    if (house == null) return false;
    return [3, 9].contains(house); // 第3宮(基礎學習)、第9宮(高等教育)
  }

  /// 判斷是否為家庭相關宮位
  bool _isFamilyHouse(int? house) {
    if (house == null) return false;
    return [4, 10].contains(house); // 第4宮(家庭)、第10宮(父母)
  }

  /// 判斷是否為財務相關宮位
  bool _isFinanceHouse(int? house) {
    if (house == null) return false;
    return [2, 8].contains(house); // 第2宮(個人財富)、第8宮(共同財產)
  }

  /// 判斷是否為心靈相關宮位
  bool _isSpiritualHouse(int? house) {
    if (house == null) return false;
    return [8, 9, 12].contains(house); // 第8宮(轉化)、第9宮(哲學)、第12宮(靈性)
  }

  /// 轉換行星名稱為中文
  String _translatePlanetName(String planetName) {
    final translations = {
      'Sun': '太陽',
      'Moon': '月亮',
      'Mercury': '水星',
      'Venus': '金星',
      'Mars': '火星',
      'Jupiter': '木星',
      'Saturn': '土星',
      'Uranus': '天王星',
      'Neptune': '海王星',
      'Pluto': '冥王星',
    };
    return translations[planetName] ?? planetName;
  }

  /// 為傳統事件判斷事件類別
  String _determineEventCategoryForTraditionalEvent(
    String transitPlanet,
    String natalPlanet,
    String aspectType,
    int? natalHouse,
  ) {
    // 轉換行星名稱為中文
    final transitPlanetCN = _translatePlanetName(transitPlanet);
    final natalPlanetCN = _translatePlanetName(natalPlanet);
    // 感情事件判斷
    if (_isRelationshipPlanet(transitPlanetCN) ||
        _isRelationshipPlanet(natalPlanetCN) ||
        _isRelationshipHouse(natalHouse)) {
      return 'relationship';
    }

    // 事業事件判斷
    if (_isCareerPlanet(transitPlanetCN) ||
        _isCareerPlanet(natalPlanetCN) ||
        _isCareerHouse(natalHouse)) {
      return 'career';
    }

    // 健康事件判斷
    if (_isHealthPlanet(transitPlanetCN) ||
        _isHealthPlanet(natalPlanetCN) ||
        _isHealthHouse(natalHouse)) {
      return 'health';
    }

    // 財務事件判斷
    if (_isFinancePlanet(transitPlanetCN) ||
        _isFinancePlanet(natalPlanetCN) ||
        _isFinanceHouse(natalHouse)) {
      return 'finance';
    }

    // 學習事件判斷
    if (_isEducationPlanet(transitPlanetCN) ||
        _isEducationPlanet(natalPlanetCN) ||
        _isEducationHouse(natalHouse)) {
      return 'education';
    }

    // 家庭事件判斷
    if (_isFamilyPlanet(transitPlanetCN) ||
        _isFamilyPlanet(natalPlanetCN) ||
        _isFamilyHouse(natalHouse)) {
      return 'family';
    }

    // 心靈事件判斷
    if (_isSpiritualPlanet(transitPlanetCN) ||
        _isSpiritualPlanet(natalPlanetCN) ||
        _isSpiritualHouse(natalHouse)) {
      return 'spiritual';
    }

    // 預設為心靈類別
    return 'spiritual';
  }

  bool _isChallengingAspect(String aspectType) {
    return ['刑', '沖', '半刑', '八分相'].contains(aspectType);
  }

  bool _isHarmoniousAspect(String aspectType) {
    return ['合', '拱', '六分相'].contains(aspectType);
  }

  double _calculateOrb(double longitude1, double longitude2) {
    double diff = (longitude1 - longitude2).abs();
    if (diff > 180) diff = 360 - diff;
    return diff;
  }

  String _determineAspectType(double longitude1, double longitude2) {
    final orb = _calculateOrb(longitude1, longitude2);

    if (orb <= 8) return '合';
    if ((orb - 60).abs() <= 6) return '六分相';
    if ((orb - 90).abs() <= 8) return '刑';
    if ((orb - 120).abs() <= 8) return '拱';
    if ((orb - 180).abs() <= 8) return '沖';

    return '無相位';
  }

  bool _isSignificantAspect(String aspectType) {
    return aspectType != '無相位';
  }

  double _calculateAspectStrength(String aspectType, double orb) {
    final baseStrength = {
          '合': 100.0,
          '沖': 90.0,
          '刑': 80.0,
          '拱': 70.0,
          '六分相': 60.0,
        }[aspectType] ??
        0.0;

    // 根據容許度調整強度
    final orbFactor = (8.0 - orb) / 8.0;
    return baseStrength * orbFactor;
  }

  Map<String, dynamic> _calculateEventPotential(
    String planet1,
    String planet2,
    String aspectType,
    Map<String, dynamic> natalAnalysis, {
    int? planet1House,
    int? planet2House,
  }) {
    // 根據行星組合、宮位和相位類型判斷事件類型和強度
    final eventTypes = <String, double>{};

    // 感情事件 - 考慮行星和宮位
    final isRelationshipEvent = _isRelationshipPlanet(planet1) ||
        _isRelationshipPlanet(planet2) ||
        _isRelationshipHouse(planet1House) ||
        _isRelationshipHouse(planet2House);

    if (isRelationshipEvent) {
      final sensitivity =
          natalAnalysis['relationship_sensitivity'] as double? ?? 50.0;
      double multiplier = 0.8;

      // 如果同時涉及感情行星和感情宮位，增加權重
      if ((_isRelationshipPlanet(planet1) || _isRelationshipPlanet(planet2)) &&
          (_isRelationshipHouse(planet1House) ||
              _isRelationshipHouse(planet2House))) {
        multiplier = 1.2;
      }

      eventTypes['relationship'] = sensitivity * multiplier;
    }

    // 事業事件 - 考慮行星和宮位
    final isCareerEvent = _isCareerPlanet(planet1) ||
        _isCareerPlanet(planet2) ||
        _isCareerHouse(planet1House) ||
        _isCareerHouse(planet2House);

    if (isCareerEvent) {
      final sensitivity =
          natalAnalysis['career_sensitivity'] as double? ?? 50.0;
      double multiplier = 0.8;

      // 如果同時涉及事業行星和事業宮位，增加權重
      if ((_isCareerPlanet(planet1) || _isCareerPlanet(planet2)) &&
          (_isCareerHouse(planet1House) || _isCareerHouse(planet2House))) {
        multiplier = 1.2;
      }

      eventTypes['career'] = sensitivity * multiplier;
    }

    // 健康事件 - 考慮行星和宮位
    final isHealthEvent = _isHealthPlanet(planet1) ||
        _isHealthPlanet(planet2) ||
        _isHealthHouse(planet1House) ||
        _isHealthHouse(planet2House);

    if (isHealthEvent) {
      final sensitivity =
          natalAnalysis['health_sensitivity'] as double? ?? 50.0;
      double multiplier = 0.7;

      // 如果同時涉及健康行星和健康宮位，增加權重
      if ((_isHealthPlanet(planet1) || _isHealthPlanet(planet2)) &&
          (_isHealthHouse(planet1House) || _isHealthHouse(planet2House))) {
        multiplier = 1.1;
      }

      eventTypes['health'] = sensitivity * multiplier;
    }

    // 財務事件 - 考慮行星和宮位
    final isFinanceEvent = _isFinancePlanet(planet1) ||
        _isFinancePlanet(planet2) ||
        _isFinanceHouse(planet1House) ||
        _isFinanceHouse(planet2House);

    if (isFinanceEvent) {
      final sensitivity =
          natalAnalysis['finance_sensitivity'] as double? ?? 50.0;
      double multiplier = 0.8;

      // 如果同時涉及財務行星和財務宮位，增加權重
      if ((_isFinancePlanet(planet1) || _isFinancePlanet(planet2)) &&
          (_isFinanceHouse(planet1House) || _isFinanceHouse(planet2House))) {
        multiplier = 1.2;
      }

      eventTypes['finance'] = sensitivity * multiplier;
    }

    // 學習事件 - 考慮行星和宮位
    final isEducationEvent = _isEducationPlanet(planet1) ||
        _isEducationPlanet(planet2) ||
        _isEducationHouse(planet1House) ||
        _isEducationHouse(planet2House);

    if (isEducationEvent) {
      final sensitivity =
          natalAnalysis['education_sensitivity'] as double? ?? 50.0;
      double multiplier = 0.7;

      // 如果同時涉及學習行星和學習宮位，增加權重
      if ((_isEducationPlanet(planet1) || _isEducationPlanet(planet2)) &&
          (_isEducationHouse(planet1House) ||
              _isEducationHouse(planet2House))) {
        multiplier = 1.1;
      }

      eventTypes['education'] = sensitivity * multiplier;
    }

    // 家庭事件 - 考慮行星和宮位
    final isFamilyEvent = _isFamilyPlanet(planet1) ||
        _isFamilyPlanet(planet2) ||
        _isFamilyHouse(planet1House) ||
        _isFamilyHouse(planet2House);

    if (isFamilyEvent) {
      final sensitivity =
          natalAnalysis['family_sensitivity'] as double? ?? 50.0;
      double multiplier = 0.8;

      // 如果同時涉及家庭行星和家庭宮位，增加權重
      if ((_isFamilyPlanet(planet1) || _isFamilyPlanet(planet2)) &&
          (_isFamilyHouse(planet1House) || _isFamilyHouse(planet2House))) {
        multiplier = 1.1;
      }

      eventTypes['family'] = sensitivity * multiplier;
    }

    // 心靈事件 - 考慮行星和宮位
    final isSpiritualEvent = _isSpiritualPlanet(planet1) ||
        _isSpiritualPlanet(planet2) ||
        _isSpiritualHouse(planet1House) ||
        _isSpiritualHouse(planet2House);

    if (isSpiritualEvent) {
      final sensitivity =
          natalAnalysis['spiritual_sensitivity'] as double? ?? 50.0;
      double multiplier = 0.9;

      // 如果同時涉及心靈行星和心靈宮位，增加權重
      if ((_isSpiritualPlanet(planet1) || _isSpiritualPlanet(planet2)) &&
          (_isSpiritualHouse(planet1House) ||
              _isSpiritualHouse(planet2House))) {
        multiplier = 1.3;
      }

      eventTypes['spiritual'] = sensitivity * multiplier;
    }

    // 找出最高分的事件類型
    String primaryEventType = 'general';
    double maxScore = 0.0;

    for (final entry in eventTypes.entries) {
      if (entry.value > maxScore) {
        maxScore = entry.value;
        primaryEventType = entry.key;
      }
    }

    return {
      'primary_event_type': primaryEventType,
      'score': maxScore,
      'all_potentials': eventTypes,
    };
  }

  String _generateEventTitle(Map<String, dynamic> trigger, String eventType) {
    try {
      final triggerType = trigger['type'] as String?;

      // 根據行星和宮位智能判斷事件類型
      final intelligentEventType =
          _determineEventTypeByPlanetsAndHouses(trigger);
      final finalEventType = intelligentEventType ?? eventType;

      // 處理太陽返照事件
      if (triggerType == 'solar_return_aspect') {
        return _generateSolarReturnAspectTitle(trigger, finalEventType);
      } else if (triggerType == 'solar_return_placement') {
        return _generateSolarReturnPlacementTitle(trigger, finalEventType);
      } else {
        // 處理其他類型事件（行運、推運、太陽弧）
        return _generateTransitProgressionTitle(trigger, finalEventType);
      }
    } catch (e) {
      logger.e('生成事件標題失敗: $e');
      return '占星事件';
    }
  }

  /// 根據行星和宮位智能判斷事件類型
  String? _determineEventTypeByPlanetsAndHouses(Map<String, dynamic> trigger) {
    try {
      // 提取行星和宮位資訊
      final planetInfo = _extractPlanetAndHouseInfo(trigger);
      if (planetInfo == null) return null;

      final planet1 = planetInfo['planet1'] as String?;
      final planet2 = planetInfo['planet2'] as String?;
      final house1 = planetInfo['house1'] as int?;
      final house2 = planetInfo['house2'] as int?;
      final aspectType = planetInfo['aspectType'] as String?;

      // 根據宮位判斷事件類型
      final houseBasedType = _determineEventTypeByHouses(house1, house2);

      // 根據行星判斷事件類型
      final planetBasedType =
          _determineEventTypeByPlanets(planet1, planet2, aspectType);

      // 綜合判斷，宮位優先
      return houseBasedType ?? planetBasedType;
    } catch (e) {
      logger.e('智能判斷事件類型失敗: $e');
      return null;
    }
  }

  /// 提取行星和宮位資訊
  Map<String, dynamic>? _extractPlanetAndHouseInfo(
      Map<String, dynamic> trigger) {
    try {
      final triggerType = trigger['type'] as String?;

      if (triggerType == 'solar_return_aspect') {
        final planet1 = trigger['planet1'] as String?;
        final planet2 = trigger['planet2'] as String?;
        final house1 = trigger['planet1_house'] as int?;
        final house2 = trigger['planet2_house'] as int?;
        final aspectType = trigger['aspect'] as String?;

        return {
          'planet1': planet1,
          'planet2': planet2,
          'house1': house1,
          'house2': house2,
          'aspectType': aspectType,
        };
      } else if (triggerType == 'solar_return_placement') {
        final planet = trigger['planet'] as String?;
        final house = trigger['house'] as int?;

        return {
          'planet1': planet,
          'planet2': null,
          'house1': house,
          'house2': null,
          'aspectType': null,
        };
      } else {
        // 行運、推運、太陽弧事件
        final transitPlanet = trigger['transit_planet'] ??
            trigger['progression_planet'] ??
            trigger['solar_arc_planet'];
        final natalPlanet = trigger['natal_planet'];
        AspectInfo aspectInfo = trigger['aspect'] ??
            trigger['progression_house'] ??
            trigger['solar_arc_house'];
        final aspectType = trigger['aspectType'] as String?;

        return {
          'planet1': transitPlanet,
          'planet2': natalPlanet,
          'house1': aspectInfo.planet2.house,
          'house2': aspectInfo.planet1.house,
          'aspectType': aspectType,
        };
      }
    } catch (e) {
      logger.e('提取行星宮位資訊失敗: $e');
      return null;
    }
  }

  /// 根據宮位判斷事件類型
  String? _determineEventTypeByHouses(int? house1, int? house2) {
    final houses =
        [house1, house2].where((h) => h != null).cast<int>().toList();
    if (houses.isEmpty) return null;

    // 宮位對應的事件類型
    final houseEventTypes = <int, String>{
      1: 'personal', // 第1宮：個人形象、身體健康
      2: 'financial', // 第2宮：財務、價值觀
      3: 'communication', // 第3宮：溝通、學習、兄弟姊妹
      4: 'family', // 第4宮：家庭、房產、根基
      5: 'creativity', // 第5宮：創意、戀愛、子女
      6: 'health', // 第6宮：健康、工作、日常
      7: 'relationship', // 第7宮：伴侶關係、合作
      8: 'transformation', // 第8宮：轉化、共同資源、神秘學
      9: 'spiritual', // 第9宮：哲學、高等教育、旅行
      10: 'career', // 第10宮：事業、聲譽、社會地位
      11: 'friendship', // 第11宮：朋友、團體、願望
      12: 'spiritual', // 第12宮：潛意識、靈性、隱藏
    };

    // 優先考慮重要宮位
    final importantHouses = [1, 4, 7, 10]; // 角宮
    for (final house in houses) {
      if (importantHouses.contains(house) &&
          houseEventTypes.containsKey(house)) {
        return houseEventTypes[house];
      }
    }

    // 其他宮位
    for (final house in houses) {
      if (houseEventTypes.containsKey(house)) {
        return houseEventTypes[house];
      }
    }

    return null;
  }

  /// 根據行星判斷事件類型
  String? _determineEventTypeByPlanets(
      String? planet1, String? planet2, String? aspectType) {
    final planets =
        [planet1, planet2].where((p) => p != null).cast<String>().toList();
    if (planets.isEmpty) return null;

    // 行星對應的事件類型
    final planetEventTypes = <String, String>{
      '太陽': 'personal', // 個人意志、生命力
      '月亮': 'emotional', // 情感、家庭
      '水星': 'communication', // 溝通、學習
      '金星': 'relationship', // 愛情、美學、財務
      '火星': 'action', // 行動、衝突、能量
      '木星': 'growth', // 成長、機會、哲學
      '土星': 'responsibility', // 責任、限制、成熟
      '天王星': 'change', // 變革、創新、突破
      '海王星': 'spiritual', // 靈性、夢想、迷惑
      '冥王星': 'transformation', // 轉化、重生、深層變化
    };

    // 特殊行星組合判斷
    if (planets.contains('金星') && planets.contains('火星')) {
      return 'relationship'; // 金火相位通常與感情相關
    }
    if (planets.contains('太陽') && planets.contains('土星')) {
      return 'career'; // 日土相位通常與事業責任相關
    }
    if (planets.contains('月亮') && planets.contains('冥王星')) {
      return 'transformation'; // 月冥相位通常與深層情感轉化相關
    }

    // 根據相位類型調整
    if (aspectType != null) {
      final hardAspects = ['對分', '四分', '半四分', '八分之三'];
      final softAspects = ['三分', '六分', '半六分'];

      if (hardAspects.contains(aspectType)) {
        // 困難相位可能帶來挑戰
        if (planets.contains('土星')) return 'responsibility';
        if (planets.contains('冥王星')) return 'transformation';
      } else if (softAspects.contains(aspectType)) {
        // 和諧相位可能帶來機會
        if (planets.contains('木星')) return 'growth';
        if (planets.contains('金星')) return 'relationship';
      }
    }

    // 單個行星判斷
    for (final planet in planets) {
      if (planetEventTypes.containsKey(planet)) {
        return planetEventTypes[planet];
      }
    }

    return null;
  }

  /// 生成太陽返照相位標題
  String _generateSolarReturnAspectTitle(
      Map<String, dynamic> trigger, String eventType) {
    final planet1 = trigger['planet1'] as String? ?? '行星';
    final planet2 = trigger['planet2'] as String? ?? '行星';
    final aspectType = trigger['aspect'] as String? ?? '相位';
    final eventTypeName = _getEventTypeDisplayName(eventType);

    return '$eventTypeName - 太陽返照 $planet1 $aspectType $planet2';
  }

  /// 生成太陽返照宮位配置標題
  String _generateSolarReturnPlacementTitle(
      Map<String, dynamic> trigger, String eventType) {
    final planet = trigger['planet'] as String? ?? '行星';
    final house = trigger['house'] as int? ?? 0;
    final eventTypeName = _getEventTypeDisplayName(eventType);

    return '$eventTypeName - 太陽返照 $planet 在第$house宮';
  }

  /// 生成行運/推運/太陽弧標題
  String _generateTransitProgressionTitle(
      Map<String, dynamic> trigger, String eventType) {
    // final triggerType = trigger['type'] as String?;
    final planet1 = trigger['transit_planet'] ??
        trigger['progression_planet'] ??
        trigger['solar_arc_planet'] ??
        '行星';
    final planet2 = trigger['natal_planet'] ?? '行星';
    final aspectType = trigger['aspectType'] as String? ?? '相位';
    final eventTypeName = _getEventTypeDisplayName(eventType);

    // String prefix = '';
    // if (triggerType?.contains('transit') == true) {
    //   prefix = '行運';
    // } else if (triggerType?.contains('progression') == true) {
    //   prefix = '推運';
    // } else if (triggerType?.contains('solar_arc') == true) {
    //   prefix = '太陽弧';
    // }

    return '$planet1 $aspectType $planet2';
  }

  /// 獲取事件類型顯示名稱
  String _getEventTypeDisplayName(String eventType) {
    final eventTypeNames = <String, String>{
      'personal': '個人發展',
      'emotional': '情感變化',
      'communication': '溝通學習',
      'family': '家庭變動',
      'creativity': '創意表達',
      'health': '健康狀況',
      'relationship': '感情關係',
      'transformation': '深層轉化',
      'spiritual': '心靈成長',
      'career': '事業發展',
      'friendship': '人際關係',
      'financial': '財務狀況',
      'growth': '成長機會',
      'responsibility': '責任承擔',
      'action': '行動力量',
      'change': '變革創新',

      // 原有的事件類型
      'learning': '學習成長',
      'relocation': '環境變動',
    };

    return eventTypeNames[eventType] ?? '重要事件';
  }

  String _generateEventDescription(
    Map<String, dynamic> trigger,
    String eventType,
    Map<String, dynamic> natalAnalysis,
  ) {
    final planet1 = trigger['transit_planet'] ??
        trigger['progression_planet'] ??
        trigger['solar_arc_planet'];
    final planet2 = trigger['natal_planet'];
    final AspectInfo aspect = trigger['aspect'];
    final double strength = trigger['strength'] as double;

    final descriptions = {
      'relationship': '感情關係可能迎來重要變化，需留意伴侶互動與人際連結。',
      'career': '事業發展正處於轉折期，適合重新評估職涯方向與目標。',
      'financial': '財務狀況可能波動，建議審慎處理投資與理財計劃。',
      'health': '需關注身體狀況，可安排檢查並調整生活作息。',
      'learning': '是進修學習的好時機，有助於個人成長與知識累積。',
      'relocation': '可能面臨搬遷或環境變動，需審慎評估決策。',
      'spiritual': '內在心靈可能有重大轉化，適合進行自我反省與內在探索。',
    };

    if (planet1 == null || planet2 == null) {
      return descriptions[eventType] ?? '這是一個重要的占星事件，值得關注。';
    }

    final baseDescription = descriptions[eventType] ?? '這是一個重要的占星事件，值得關注。';

    return '''
🔹 行星與相位：
行運 $planet1 ${aspect.shortZh} 本命 $planet2 
(容許度: ${aspect.orb.toStringAsFixed(1)}°，方向: ${aspect.getDirectionText()})

🔹 宮位影響：
${aspect.planet1.house}宮 ${aspect.shortZh} ${aspect.planet2.house}宮

🔹 事件主題：
$baseDescription

🔹 相位強度：
${strength.toStringAsFixed(1)}
''';
  }

  AstroEventType _mapEventTypeToAstroEventType(String eventType) {
    switch (eventType) {
      case 'relationship':
        return AstroEventType.transitAspect;
      case 'career':
        return AstroEventType.transitAspect;
      default:
        return AstroEventType.transitAspect;
    }
  }

  Color _getEventTypeColor(String eventType) {
    switch (eventType) {
      case 'relationship':
        return Colors.pink;
      case 'career':
        return Colors.blue;
      case 'financial':
        return Colors.green;
      case 'health':
        return Colors.red;
      case 'learning':
        return Colors.purple;
      case 'relocation':
        return Colors.orange;
      case 'spiritual':
        return Colors.indigo;
      default:
        return Colors.grey;
    }
  }

  IconData _getEventTypeIcon(String eventType) {
    switch (eventType) {
      case 'relationship':
        return Icons.favorite;
      case 'career':
        return Icons.work;
      case 'financial':
        return Icons.attach_money;
      case 'health':
        return Icons.health_and_safety;
      case 'learning':
        return Icons.school;
      case 'relocation':
        return Icons.home;
      case 'spiritual':
        return Icons.self_improvement;
      default:
        return Icons.star;
    }
  }

  int _calculateImportanceLevel(double score) {
    if (score >= 80) return 5;
    if (score >= 60) return 4;
    if (score >= 40) return 3;
    if (score >= 20) return 2;
    return 1;
  }

  List<String> _extractInvolvedPlanets(Map<String, dynamic> trigger) {
    final planets = <String>[];

    // 處理傳統事件類型
    if (trigger.containsKey('transit_planet')) {
      planets.add(trigger['transit_planet'] as String);
    }
    if (trigger.containsKey('progression_planet')) {
      planets.add(trigger['progression_planet'] as String);
    }
    if (trigger.containsKey('solar_arc_planet')) {
      planets.add(trigger['solar_arc_planet'] as String);
    }
    if (trigger.containsKey('natal_planet')) {
      planets.add(trigger['natal_planet'] as String);
    }

    // 處理太陽返照事件類型
    if (trigger.containsKey('planet1')) {
      planets.add(trigger['planet1'] as String);
    }
    if (trigger.containsKey('planet2')) {
      planets.add(trigger['planet2'] as String);
    }
    if (trigger.containsKey('planet')) {
      planets.add(trigger['planet'] as String);
    }

    return planets.toSet().toList(); // 去除重複
  }

  /// 判斷是否為太陽返照重要行星
  bool _isImportantPlanetForSolarReturn(String planetName) {
    // 太陽返照盤中特別重要的行星
    return [
      '太陽', // 核心自我和生命力
      '月亮', // 情感和日常生活
      '水星', // 溝通和思維
      '金星', // 愛情和價值觀
      '火星', // 行動力和衝突
      '木星', // 機會和擴展
      '土星', // 責任和限制
      '天王星', // 變革和突破
      '冥王星', // 轉化和重生
    ].contains(planetName);
  }

  /// 判斷是否為太陽返照重要宮位
  bool _isImportantHouseForSolarReturn(int? house) {
    if (house == null) return false;
    // 太陽返照盤中特別重要的宮位
    return [
      1, // 第1宮：個人形象和新開始
      4, // 第4宮：家庭和根基
      7, // 第7宮：伴侶關係和合作
      10, // 第10宮：事業和聲譽
      2, // 第2宮：財務和價值觀
      5, // 第5宮：創造力和戀愛
      8, // 第8宮：轉化和共同資源
      11, // 第11宮：友誼和願望
    ].contains(house);
  }

  /// 計算行星在宮位的強度
  double _calculatePlanetHouseStrength(String planetName, int? house) {
    if (house == null) return 0.0;

    // 基礎強度
    double strength = 50.0;

    // 根據行星類型調整
    final planetStrengths = {
      '太陽': 100.0,
      '月亮': 90.0,
      '水星': 70.0,
      '金星': 80.0,
      '火星': 85.0,
      '木星': 95.0,
      '土星': 90.0,
      '天王星': 75.0,
      '海王星': 70.0,
      '冥王星': 85.0,
    };

    strength = planetStrengths[planetName] ?? 50.0;

    // 根據宮位重要性調整
    final houseMultipliers = {
      1: 1.2, // 第1宮：個人形象
      4: 1.1, // 第4宮：家庭根基
      7: 1.15, // 第7宮：伴侶關係
      10: 1.2, // 第10宮：事業聲譽
      2: 1.0, // 第2宮：財務
      5: 1.05, // 第5宮：創造戀愛
      8: 1.1, // 第8宮：轉化
      11: 1.0, // 第11宮：友誼願望
    };

    final multiplier = houseMultipliers[house] ?? 0.8;
    return (strength * multiplier).clamp(0.0, 100.0);
  }

  /// 計算宮位配置的事件潛力
  Map<String, dynamic> _calculateHousePlacementEventPotential(
    String planetName,
    int? house,
    Map<String, dynamic> natalAnalysis,
  ) {
    if (house == null) {
      return {
        'primary_event_type': 'general',
        'score': 0.0,
        'all_potentials': <String, double>{},
      };
    }

    final eventTypes = <String, double>{};

    // 根據行星和宮位組合判斷事件類型
    switch (house) {
      case 1: // 第1宮：個人形象和新開始
        if (['太陽', '火星', '木星'].contains(planetName)) {
          eventTypes['spiritual'] = 80.0; // 個人成長和新開始
        }
        break;
      case 2: // 第2宮：財務和價值觀
        if (['金星', '木星', '土星'].contains(planetName)) {
          eventTypes['finance'] = 85.0; // 財務變化
        }
        break;
      case 4: // 第4宮：家庭和根基
        if (['月亮', '太陽', '土星'].contains(planetName)) {
          eventTypes['family'] = 80.0; // 家庭變化
        }
        break;
      case 5: // 第5宮：創造力和戀愛
        if (['金星', '太陽', '木星'].contains(planetName)) {
          eventTypes['relationship'] = 75.0; // 戀愛關係
        }
        break;
      case 7: // 第7宮：伴侶關係和合作
        if (['金星', '火星', '月亮'].contains(planetName)) {
          eventTypes['relationship'] = 90.0; // 伴侶關係
        }
        break;
      case 8: // 第8宮：轉化和共同資源
        if (['冥王星', '火星', '土星'].contains(planetName)) {
          eventTypes['spiritual'] = 85.0; // 深度轉化
          eventTypes['finance'] = 70.0; // 共同財務
        }
        break;
      case 10: // 第10宮：事業和聲譽
        if (['太陽', '土星', '木星'].contains(planetName)) {
          eventTypes['career'] = 90.0; // 事業發展
        }
        break;
      case 11: // 第11宮：友誼和願望
        if (['天王星', '木星', '金星'].contains(planetName)) {
          eventTypes['relationship'] = 70.0; // 友誼關係
          eventTypes['spiritual'] = 65.0; // 願望實現
        }
        break;
    }

    // 找出最高分的事件類型
    String primaryEventType = 'general';
    double maxScore = 0.0;

    for (final entry in eventTypes.entries) {
      if (entry.value > maxScore) {
        maxScore = entry.value;
        primaryEventType = entry.key;
      }
    }

    return {
      'primary_event_type': primaryEventType,
      'score': maxScore,
      'all_potentials': eventTypes,
    };
  }
}
