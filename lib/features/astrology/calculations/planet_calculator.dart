import 'package:flutter/material.dart';
import 'package:sweph/sweph.dart';

import '../../../core/constants/astrology_constants.dart';
import '../../../core/utils/logger_utils.dart';
import '../../../data/models/astrology/planet_position.dart';
import '../ephemeris/julian_date_converter.dart';
import '../utilities/angle_calculator.dart';
import '../utilities/zodiac_calculator.dart';

/// 行星位置計算器
/// 
/// 負責計算各行星在特定時間點的位置，包括：
/// - 經度、緯度、距離
/// - 運行速度
/// - 所在星座
/// - 所在宮位
class PlanetCalculator {
  /// 計算所有行星位置
  /// 
  /// [dateTime] 計算時間
  /// [latitude] 觀測地緯度
  /// [longitude] 觀測地經度
  /// [housesData] 宮位數據（用於計算行星所在宮位）
  /// [planetVisibility] 行星可見性設定
  static Future<List<PlanetPosition>> calculateAllPlanets({
    required DateTime dateTime,
    required double latitude,
    required double longitude,
    HouseCuspData? housesData,
    Map<String, bool>? planetVisibility,
  }) async {
    final List<PlanetPosition> positions = [];
    
    // 轉換為儒略日
    final julianDay = await JulianDateConverter.dateTimeToJulianDay(
      dateTime,
      latitude,
      longitude,
    );
    
    // 計算上升點度數（如果有宮位數據）
    final ascendantDegree = housesData?.cusps[1] ?? 0.0;
    
    // 計算是否為白天（用於某些計算）
    final isDaytime = _isDaytime(dateTime);
    
    // 遍歷所有行星進行計算
    for (final planetData in AstrologyConstants.PLANETS) {
      // 檢查行星是否需要顯示
      if (planetVisibility != null) {
        final planetName = planetData['name'] as String;
        if (planetVisibility[planetName] == false) {
          continue;
        }
      }
      if (planetData['id'] > 20) {
        continue;
      }
      
      try {
        final position = await _calculateSinglePlanet(
          planetData,
          julianDay,
          housesData,
          ascendantDegree,
          isDaytime,
        );
        positions.add(position);
      } catch (e) {
        logger.e('計算行星 ${planetData['name']} 位置時出錯: $e');
      }
    }
    
    return positions;
  }
  
  /// 計算單個行星位置
  static Future<PlanetPosition> _calculateSinglePlanet(
    Map<String, dynamic> planetData,
    double julianDay,
    HouseCuspData? housesData,
    double ascendantDegree,
    bool isDaytime,
  ) async {
    // 使用 Swiss Ephemeris 計算行星位置
    final result = Sweph.swe_calc_ut(
      julianDay,
      planetData['body'] as HeavenlyBody,
      SwephFlag.SEFLG_SWIEPH | SwephFlag.SEFLG_SPEED,
    );
    
    // 提取位置數據
    final longitude = result.longitude;
    final latitude = result.latitude;
    final distance = result.distance;
    final longitudeSpeed = result.speedInLongitude;
    final latitudeSpeed = result.speedInLatitude;
    final distanceSpeed = result.speedInDistance;
    
    // 計算所在星座
    final signInfo = ZodiacCalculator.getZodiacSign(longitude);
    final sign = signInfo['name'] as String;
    
    // 計算所在宮位
    final house = _calculateHouse(longitude, housesData, ascendantDegree);
    
    return PlanetPosition(
      id: planetData['id'] as int,
      name: planetData['name'] as String,
      symbol: planetData['symbol'] as String,
      color: planetData['color'] as Color,
      longitude: longitude,
      latitude: latitude,
      distance: distance,
      longitudeSpeed: longitudeSpeed,
      latitudeSpeed: latitudeSpeed,
      distanceSpeed: distanceSpeed,
      sign: sign,
      house: house,
    );
  }
  
  /// 計算行星所在宮位
  static int _calculateHouse(
    double longitude,
    HouseCuspData? housesData,
    double ascendantDegree,
  ) {
    if (housesData != null && housesData.cusps.isNotEmpty) {
      // 使用精確的宮位數據計算
      return _calculateHouseFromCusps(longitude, housesData);
    } else {
      // 使用簡化的等分宮位計算
      return _calculateHouseFromAscendant(longitude, ascendantDegree);
    }
  }
  
  /// 根據宮位界線計算行星所在宮位
  static int _calculateHouseFromCusps(double longitude, HouseCuspData housesData) {
    for (int i = 1; i <= 12; i++) {
      final currentCusp = housesData.cusps[i];
      final nextCusp = housesData.cusps[i == 12 ? 1 : i + 1];
      
      if (_isInHouse(longitude, currentCusp, nextCusp)) {
        return i;
      }
    }
    return 1; // 預設返回第一宮
  }
  
  /// 根據上升點計算行星所在宮位（等分宮位）
  static int _calculateHouseFromAscendant(double longitude, double ascendantDegree) {
    final normalizedLongitude = AngleCalculator.normalizeAngle(longitude);
    final normalizedAscendant = AngleCalculator.normalizeAngle(ascendantDegree);
    
    // 計算相對於上升點的角度
    double relativeAngle = normalizedLongitude - normalizedAscendant;
    if (relativeAngle < 0) relativeAngle += 360;
    
    // 每個宮位 30 度
    final house = (relativeAngle / 30).floor() + 1;
    return house > 12 ? house - 12 : house;
  }
  
  /// 檢查角度是否在指定宮位範圍內
  static bool _isInHouse(double longitude, double startCusp, double endCusp) {
    final normalizedLongitude = AngleCalculator.normalizeAngle(longitude);
    final normalizedStart = AngleCalculator.normalizeAngle(startCusp);
    final normalizedEnd = AngleCalculator.normalizeAngle(endCusp);
    
    if (normalizedStart <= normalizedEnd) {
      // 正常情況：宮位不跨越 0 度
      return normalizedLongitude >= normalizedStart && normalizedLongitude < normalizedEnd;
    } else {
      // 特殊情況：宮位跨越 0 度
      return normalizedLongitude >= normalizedStart || normalizedLongitude < normalizedEnd;
    }
  }
  
  /// 判斷是否為白天
  static bool _isDaytime(DateTime dateTime) {
    final hour = dateTime.hour;
    return hour >= 6 && hour < 18; // 簡化的白天判斷
  }
  
  /// 計算特殊點位（上升點、中天、下降點、天底）
  static List<PlanetPosition> calculateSpecialPoints(HouseCuspData? housesData) {
    if (housesData == null) return [];
    
    final specialPoints = <PlanetPosition>[];
    
    // 上升點 (ASC)
    if (housesData.ascmc.isNotEmpty) {
      specialPoints.add(PlanetPosition(
        id: 100,
        name: '上升點',
        symbol: 'ASC',
        color: Colors.red,
        longitude: housesData.ascmc[0],
        latitude: 0.0,
        distance: 0.0,
        longitudeSpeed: 0.0,
        latitudeSpeed: 0.0,
        distanceSpeed: 0.0,
        sign: ZodiacCalculator.getZodiacSign(housesData.ascmc[0])['name'] as String,
        house: 1,
      ));
    }
    
    // 中天 (MC)
    if (housesData.ascmc.length > 1) {
      specialPoints.add(PlanetPosition(
        id: 101,
        name: '中天',
        symbol: 'MC',
        color: Colors.blue,
        longitude: housesData.ascmc[1],
        latitude: 0.0,
        distance: 0.0,
        longitudeSpeed: 0.0,
        latitudeSpeed: 0.0,
        distanceSpeed: 0.0,
        sign: ZodiacCalculator.getZodiacSign(housesData.ascmc[1])['name'] as String,
        house: 10,
      ));
    }
    
    // 下降點 (DSC)
    if (housesData.ascmc.isNotEmpty) {
      final dscLongitude = AngleCalculator.normalizeAngle(housesData.ascmc[0] + 180);
      specialPoints.add(PlanetPosition(
        id: 102,
        name: '下降點',
        symbol: 'DSC',
        color: Colors.orange,
        longitude: dscLongitude,
        latitude: 0.0,
        distance: 0.0,
        longitudeSpeed: 0.0,
        latitudeSpeed: 0.0,
        distanceSpeed: 0.0,
        sign: ZodiacCalculator.getZodiacSign(dscLongitude)['name'] as String,
        house: 7,
      ));
    }
    
    // 天底 (IC)
    if (housesData.ascmc.length > 1) {
      final icLongitude = AngleCalculator.normalizeAngle(housesData.ascmc[1] + 180);
      specialPoints.add(PlanetPosition(
        id: 103,
        name: '天底',
        symbol: 'IC',
        color: Colors.green,
        longitude: icLongitude,
        latitude: 0.0,
        distance: 0.0,
        longitudeSpeed: 0.0,
        latitudeSpeed: 0.0,
        distanceSpeed: 0.0,
        sign: ZodiacCalculator.getZodiacSign(icLongitude)['name'] as String,
        house: 4,
      ));
    }
    
    return specialPoints;
  }
}
