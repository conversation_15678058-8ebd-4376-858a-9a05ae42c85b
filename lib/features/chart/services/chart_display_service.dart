import 'package:flutter/material.dart';

import '../../../data/models/astrology/chart_data.dart';
import '../core/chart_display_config.dart';
import '../models/chart_display_data.dart';
import '../models/chart_display_settings.dart';
import '../models/chart_display_theme.dart';

/// 星盤顯示服務
/// 
/// 提供星盤顯示相關的業務邏輯和數據處理
class ChartDisplayService {
  static final ChartDisplayService _instance = ChartDisplayService._internal();
  factory ChartDisplayService() => _instance;
  ChartDisplayService._internal();

  /// 創建預設配置
  ChartDisplayConfig createDefaultConfig(ChartData chartData) {
    return ChartDisplayConfig.defaultConfig(chartData: chartData);
  }

  /// 創建簡化配置
  ChartDisplayConfig createMinimalConfig(ChartData chartData) {
    return ChartDisplayConfig.minimal(chartData: chartData);
  }

  /// 創建完整配置
  ChartDisplayConfig createFullConfig(ChartData chartData) {
    return ChartDisplayConfig.full(chartData: chartData);
  }

  /// 創建自定義配置
  ChartDisplayConfig createCustomConfig({
    required ChartData chartData,
    ChartDisplayTheme? theme,
    ChartDisplaySettings? settings,
    bool showToolbar = true,
    bool showTabs = true,
    bool showInfoPanel = true,
    Function(dynamic planet)? onPlanetTap,
    Function(String error)? onError,
  }) {
    return ChartDisplayConfig(
      chartData: chartData,
      theme: theme ?? ChartDisplayTheme.defaultTheme(),
      settings: settings ?? ChartDisplaySettings.defaultSettings(),
      showToolbar: showToolbar,
      showTabs: showTabs,
      showInfoPanel: showInfoPanel,
      onPlanetTap: onPlanetTap,
      onError: onError,
    );
  }

  /// 驗證星盤數據
  bool validateChartData(ChartData chartData) {
    try {
      // 檢查基本數據
      if (chartData.primaryPerson.name.isEmpty) {
        return false;
      }
      
      // 檢查行星數據
      if (chartData.planets == null || chartData.planets!.isEmpty) {
        return false;
      }
      
      // 檢查宮位數據
      if (chartData.houses == null) {
        return false;
      }
      
      return true;
    } catch (e) {
      return false;
    }
  }

  /// 獲取支援的主題列表
  List<ChartDisplayTheme> getSupportedThemes() {
    return [
      ChartDisplayTheme.defaultTheme(),
      ChartDisplayTheme.darkTheme(),
      ChartDisplayTheme.minimal(),
    ];
  }

  /// 獲取主題名稱
  String getThemeName(ChartDisplayTheme theme) {
    if (theme == ChartDisplayTheme.defaultTheme()) {
      return '預設主題';
    } else if (theme == ChartDisplayTheme.darkTheme()) {
      return '深色主題';
    } else if (theme == ChartDisplayTheme.minimal()) {
      return '簡化主題';
    }
    return '自定義主題';
  }

  /// 創建主題變體
  ChartDisplayTheme createThemeVariant({
    required ChartDisplayTheme baseTheme,
    Color? primaryColor,
    Color? backgroundColor,
    double? borderRadius,
  }) {
    return baseTheme.copyWith(
      primaryColor: primaryColor,
      backgroundColor: backgroundColor,
      borderRadius: borderRadius,
    );
  }

  /// 獲取預設設定模板
  List<ChartDisplaySettings> getSettingsTemplates() {
    return [
      ChartDisplaySettings.minimal(),
      ChartDisplaySettings.defaultSettings(),
      ChartDisplaySettings.full(),
    ];
  }

  /// 獲取設定模板名稱
  String getSettingsTemplateName(ChartDisplaySettings settings) {
    if (settings.enabledTabs.length == 1) {
      return '簡化模式';
    } else if (settings.enabledTabs.length <= 4) {
      return '標準模式';
    } else {
      return '完整模式';
    }
  }

  /// 分析星盤數據統計
  Map<String, dynamic> analyzeChartStatistics(ChartDisplayData displayData) {
    final planets = displayData.planets;
    final aspects = displayData.aspects;
    
    // 元素統計
    final elementCounts = <String, int>{
      '火': 0,
      '土': 0,
      '風': 0,
      '水': 0,
    };
    
    // 性質統計
    final qualityCounts = <String, int>{
      '基本': 0,
      '固定': 0,
      '變動': 0,
    };
    
    // 極性統計
    final polarityCounts = <String, int>{
      '陽性': 0,
      '陰性': 0,
    };
    
    // 分析行星分佈
    for (final _ in planets) {
      // 這裡需要根據實際的星座數據來統計
      // 暫時使用模擬數據
    }
    
    // 相位統計
    final aspectCounts = <String, int>{};
    for (final aspect in aspects) {
      final aspectName = aspect.aspectType;
      aspectCounts[aspectName] = (aspectCounts[aspectName] ?? 0) + 1;
    }
    
    return {
      'planetCount': planets.length,
      'aspectCount': aspects.length,
      'elements': elementCounts,
      'qualities': qualityCounts,
      'polarities': polarityCounts,
      'aspects': aspectCounts,
    };
  }

  /// 生成星盤摘要
  String generateChartSummary(ChartDisplayData displayData) {
    final stats = analyzeChartStatistics(displayData);
    final planetCount = stats['planetCount'];
    final aspectCount = stats['aspectCount'];
    
    return '此星盤包含 $planetCount 個行星和 $aspectCount 個相位。';
  }

  /// 檢查功能兼容性
  bool isFeatureCompatible(ChartDisplayData displayData, String feature) {
    switch (feature) {
      case 'firdaria':
        return displayData.isFirdariaChart;
      case 'progression':
        return displayData.isProgressionChart;
      case 'synastry':
        return displayData.isTwoPersonChart;
      default:
        return true;
    }
  }

  /// 獲取建議的設定
  ChartDisplaySettings getSuggestedSettings(ChartDisplayData displayData) {
    if (displayData.isFirdariaChart) {
      // 法達盤建議設定
      return ChartDisplaySettings(
        enabledTabs: [
          ChartDisplayTabType.firdaria,
          ChartDisplayTabType.chart,
        ],
        chartCanvas: ChartCanvasSettings.defaultSettings(),
        planetList: PlanetListSettings.minimal(),
        houses: HouseSettings.minimal(),
        aspectTable: AspectTableSettings.minimal(),
        elements: ChartElementsSettings.minimal(),
        classical: ClassicalAstrologySettings.defaultSettings(),
        firdaria: FirdariaSettings.full(),
        export: ExportSettings.defaultSettings(),
      );
    } else if (displayData.isTwoPersonChart) {
      // 合盤建議設定
      return ChartDisplaySettings(
        enabledTabs: [
          ChartDisplayTabType.chart,
          ChartDisplayTabType.aspects,
          ChartDisplayTabType.elements,
        ],
        chartCanvas: ChartCanvasSettings.defaultSettings(),
        planetList: PlanetListSettings.defaultSettings(),
        houses: HouseSettings.defaultSettings(),
        aspectTable: AspectTableSettings.full(),
        elements: ChartElementsSettings.full(),
        classical: ClassicalAstrologySettings.minimal(),
        firdaria: FirdariaSettings.minimal(),
        export: ExportSettings.defaultSettings(),
      );
    } else {
      // 本命盤預設設定
      return ChartDisplaySettings.defaultSettings();
    }
  }
}
