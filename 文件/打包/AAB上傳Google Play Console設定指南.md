# AAB 上傳 Google Play Console 設定指南

本指南說明如何設定自動上傳 Android App Bundle (AAB) 到 Google Play Console。

## 📋 前置準備

### 1. Google Play Console 設定

1. **建立應用程式**
   - 登入 [Google Play Console](https://play.google.com/console)
   - 建立新應用程式或選擇現有應用程式

2. **建立服務帳戶**
   - 前往 Google Cloud Console
   - 選擇您的專案
   - 導航至「IAM 與管理」→「服務帳戶」
   - 點擊「建立服務帳戶」
   - 輸入服務帳戶名稱和描述
   - 點擊「建立並繼續」

3. **設定服務帳戶權限**
   - 在 Google Play Console 中，前往「設定」→「API 存取權」
   - 點擊「連結專案」（如果尚未連結）
   - 在「服務帳戶」區段中，找到您剛建立的服務帳戶
   - 點擊「授予存取權」
   - 選擇適當的權限：
     - **發布應用程式**：允許上傳 APK/AAB
     - **管理發布**：管理發布軌道
     - **查看應用程式資訊**：讀取應用程式資料

4. **下載服務帳戶金鑰**
   - 在 Google Cloud Console 的服務帳戶頁面
   - 點擊您的服務帳戶
   - 前往「金鑰」標籤
   - 點擊「新增金鑰」→「建立新金鑰」
   - 選擇「JSON」格式
   - 下載金鑰檔案

### 2. 專案設定

1. **放置服務帳戶金鑰**
   ```bash
   # 將下載的 JSON 檔案重新命名並放置到正確位置
   cp ~/Downloads/your-service-account-key.json android/play-console-service-account.json
   ```

2. **確保金鑰檔案安全**
   ```bash
   # 將金鑰檔案加入 .gitignore
   echo "android/play-console-service-account.json" >> .gitignore
   ```

## 🚀 使用方式

### 基本用法

```bash
# 建置並上傳 AAB 到 Google Play Console 內部測試軌道
./android_build.sh release aab --upload-play

# 只建置 AAB（不上傳）
./android_build.sh release aab

# 建置 APK 和 AAB
./android_build.sh release both

# 建置 AAB 並上傳（同時上傳 APK 到 Firebase）
./android_build.sh release both --upload-play
```

### 進階設定

您可以在 `android/app/build.gradle.kts` 中修改 Play Publisher 設定：

```kotlin
play {
    // 發布軌道選項：internal, alpha, beta, production
    track.set("internal")
    
    // 發布狀態：draft, inProgress, halted, completed
    releaseStatus.set(com.github.triplet.gradle.androidpublisher.ReleaseStatus.COMPLETED)
    
    // 更新優先級 (0-5)
    updatePriority.set(0)
}
```

## 📝 發布軌道說明

| 軌道 | 說明 | 適用場景 |
|------|------|----------|
| `internal` | 內部測試 | 開發團隊內部測試 |
| `alpha` | 封閉測試 | 小範圍測試用戶 |
| `beta` | 開放測試 | 公開測試版本 |
| `production` | 正式發布 | 正式版本發布 |

## ⚠️ 注意事項

1. **版本號管理**
   - 每次上傳的 `versionCode` 必須大於目前發布的版本
   - 建議使用時間戳或遞增數字作為 `versionCode`

2. **簽名設定**
   - 確保 `android/key.properties` 設定正確
   - Release 版本必須使用正確的簽名金鑰

3. **權限檢查**
   - 確保服務帳戶有足夠的權限
   - 檢查 Google Play Console 中的 API 存取權設定

4. **網路連線**
   - 上傳過程需要穩定的網路連線
   - AAB 檔案通常比 APK 小，但仍需要時間上傳

## 🔧 故障排除

### 常見錯誤

1. **403 Forbidden**
   - 檢查服務帳戶權限
   - 確認應用程式 ID 正確

2. **版本衝突**
   - 增加 `pubspec.yaml` 中的版本號
   - 確保 `versionCode` 大於目前版本

3. **簽名錯誤**
   - 檢查 `key.properties` 設定
   - 確認金鑰檔案路徑正確

4. **網路超時**
   - 檢查網路連線
   - 重試上傳過程

### 除錯指令

```bash
# 檢查 Gradle 設定
cd android && ./gradlew tasks --all | grep publish

# 測試服務帳戶連線
cd android && ./gradlew validateUpload

# 詳細錯誤訊息
cd android && ./gradlew publishReleaseBundle --info --stacktrace
```

## 📚 相關資源

- [Google Play Console 說明](https://support.google.com/googleplay/android-developer/)
- [Gradle Play Publisher 插件文件](https://github.com/Triple-T/gradle-play-publisher)
- [Android App Bundle 指南](https://developer.android.com/guide/app-bundle)
- [服務帳戶設定指南](https://developers.google.com/android-publisher/getting_started)
