# Google Play Console 服務帳戶快速設定

## 🚨 重要提醒

**Firebase Admin SDK 金鑰 ≠ Google Play Console 服務帳戶金鑰**

目前專案中的 `astreal-d3f70-firebase-adminsdk-fbsvc-8ae96f762a.json` 是 Firebase Admin SDK 的服務帳戶金鑰，**不能**用於 Google Play Console 上傳。

## 📋 快速設定步驟

### 1. 建立 Google Play Console 服務帳戶

1. **前往 Google Cloud Console**
   - 開啟 [Google Cloud Console](https://console.cloud.google.com/)
   - 選擇您的專案 (`astreal-d3f70`)

2. **建立服務帳戶**
   ```
   導航路徑：IAM 與管理 → 服務帳戶 → 建立服務帳戶
   
   服務帳戶名稱：play-console-publisher
   服務帳戶 ID：play-console-publisher
   描述：用於自動上傳 AAB 到 Google Play Console
   ```

3. **下載金鑰檔案**
   - 點擊新建立的服務帳戶
   - 前往「金鑰」標籤
   - 點擊「新增金鑰」→「建立新金鑰」
   - 選擇「JSON」格式
   - 下載金鑰檔案

### 2. 設定 Google Play Console 權限

1. **前往 Google Play Console**
   - 開啟 [Google Play Console](https://play.google.com/console)
   - 選擇您的應用程式

2. **連結 Google Cloud 專案**
   ```
   導航路徑：設定 → API 存取權 → 連結專案
   
   如果尚未連結，請連結您的 Google Cloud 專案
   ```

3. **授予服務帳戶權限**
   ```
   在「服務帳戶」區段中：
   1. 找到您剛建立的服務帳戶
   2. 點擊「授予存取權」
   3. 選擇權限：
      ✅ 發布應用程式
      ✅ 管理發布
      ✅ 查看應用程式資訊
   ```

### 3. 安裝金鑰檔案

```bash
# 將下載的金鑰檔案重新命名並放置到正確位置
cp ~/Downloads/astreal-d3f70-xxxxxxxxx.json android/play-console-service-account.json

# 確保檔案不會被提交到版本控制
echo "android/play-console-service-account.json" >> .gitignore
```

### 4. 測試設定

```bash
# 檢查環境設定
./test_build_setup.sh

# 測試 AAB 建置（不上傳）
./android_build.sh release aab

# 測試 AAB 上傳到內部測試軌道
./aab_upload.sh internal
```

## 🔧 故障排除

### 常見錯誤

1. **檔案不存在錯誤**
   ```
   Error: file 'android/play-console-service-account.json' doesn't exist
   ```
   **解決方案：** 確保金鑰檔案放置在正確位置且檔名正確

2. **權限不足錯誤**
   ```
   Error: The caller does not have permission
   ```
   **解決方案：** 檢查 Google Play Console 中的服務帳戶權限設定

3. **應用程式不存在錯誤**
   ```
   Error: Package not found
   ```
   **解決方案：** 確保應用程式已在 Google Play Console 中建立

### 檢查清單

- [ ] Google Cloud 專案中已建立服務帳戶
- [ ] 已下載 JSON 金鑰檔案
- [ ] Google Play Console 中已連結 Google Cloud 專案
- [ ] 服務帳戶已獲得適當權限
- [ ] 金鑰檔案已放置在 `android/play-console-service-account.json`
- [ ] 應用程式已在 Google Play Console 中建立

## 🎯 快速驗證

執行以下指令驗證設定：

```bash
# 1. 檢查金鑰檔案
ls -la android/play-console-service-account.json

# 2. 檢查檔案內容格式
head -5 android/play-console-service-account.json

# 3. 測試建置環境
./test_build_setup.sh

# 4. 測試 AAB 建置
./android_build.sh release aab

# 5. 測試上傳（如果設定完成）
./aab_upload.sh internal
```

## 📞 需要協助？

如果遇到問題，請檢查：

1. **詳細設定指南：** `文件/打包/AAB上傳Google Play Console設定指南.md`
2. **使用說明：** `文件/打包/Android打包腳本使用指南.md`
3. **實作總結：** `文件/打包/AAB打包上傳功能實作總結.md`

## 💡 臨時解決方案

如果暫時無法設定 Google Play Console 服務帳戶，您仍可以：

```bash
# 建置 AAB 檔案（手動上傳）
./android_build.sh release aab

# 建置 APK 並上傳到 Firebase App Distribution
./android_build.sh release apk

# 同時建置 APK 和 AAB（APK 自動上傳到 Firebase）
./android_build.sh release both
```

然後手動將 AAB 檔案上傳到 Google Play Console。
